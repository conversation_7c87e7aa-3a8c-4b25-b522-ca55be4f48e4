/**
 * STB_Image解码器测试程序
 * 专门测试在WebAssembly环境中使用STB_Image解码网络图像
 */

#include <iostream>
#include <string>
#include <vector>

#ifdef EMSCRIPTEN
#include <SDL2/SDL.h>
#include <SDL2/SDL_opengles2.h>
#include <emscripten.h>
#include <emscripten/html5.h>
#include <emscripten/fetch.h>

// STB_Image实现
#define STB_IMAGE_IMPLEMENTATION
#define STBI_NO_STDIO
#define STBI_NO_FAILURE_STRINGS
#define STBI_ONLY_PNG
#define STBI_ONLY_JPEG
#include "../../src/third_party/tinygltf/stb_image.h"
#else
#include <SDL.h>
#include <GL/gl.h>
#endif

// 日志宏
#define INFO_LOG(msg) std::cout << "[INFO] " << msg << std::endl
#define ERROR_LOG(msg) std::cerr << "[ERROR] " << msg << std::endl

// 应用程序上下文
struct AppContext
{
    SDL_Window *window = nullptr;
    SDL_GLContext context = nullptr;
    bool shouldExit = false;

    // 测试结果
    int successCount = 0;
    int failureCount = 0;
};

static AppContext *g_appContext = nullptr;

/**
 * 初始化 SDL
 */
bool initializeSDL()
{
    if (SDL_Init(SDL_INIT_VIDEO) < 0)
    {
        ERROR_LOG("SDL 初始化失败: " << SDL_GetError());
        return false;
    }

    // 设置 OpenGL 属性
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 3);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 0);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_PROFILE_MASK, SDL_GL_CONTEXT_PROFILE_ES);
    SDL_GL_SetAttribute(SDL_GL_DOUBLEBUFFER, 1);
    SDL_GL_SetAttribute(SDL_GL_DEPTH_SIZE, 24);

    // 创建窗口
    g_appContext->window = SDL_CreateWindow(
        "STB_Image Test",
        SDL_WINDOWPOS_CENTERED, SDL_WINDOWPOS_CENTERED,
        800, 600,
        SDL_WINDOW_OPENGL | SDL_WINDOW_SHOWN);

    if (!g_appContext->window)
    {
        ERROR_LOG("窗口创建失败: " << SDL_GetError());
        return false;
    }

    // 创建 OpenGL 上下文
    g_appContext->context = SDL_GL_CreateContext(g_appContext->window);
    if (!g_appContext->context)
    {
        ERROR_LOG("OpenGL 上下文创建失败: " << SDL_GetError());
        return false;
    }

    SDL_GL_SetSwapInterval(1);
    return true;
}

/**
 * 测试STB_Image解码功能
 */
void testSTBImageDecoding(const unsigned char *data, size_t size, const std::string &description)
{
#ifdef EMSCRIPTEN
    INFO_LOG("测试STB_Image解码: " << description);

    int width, height, channels;
    unsigned char *pixels = stbi_load_from_memory(data, static_cast<int>(size), &width, &height, &channels, 0);

    if (pixels)
    {
        INFO_LOG("✅ STB_Image解码成功: " << width << "x" << height << " 通道数: " << channels);
        g_appContext->successCount++;

        // 释放内存
        stbi_image_free(pixels);
    }
    else
    {
        ERROR_LOG("❌ STB_Image解码失败: " << description);
        g_appContext->failureCount++;
    }
#else
    INFO_LOG("STB_Image测试仅在WebAssembly环境中可用");
#endif
}

/**
 * 异步下载并测试图像解码
 */
void downloadAndTestImage(const std::string &url, const std::string &description)
{
#ifdef EMSCRIPTEN
    INFO_LOG("开始下载图像: " << url);

    // 创建回调数据
    struct CallbackData
    {
        std::string url;
        std::string description;
    };

    CallbackData *data = new CallbackData{url, description};

    // 配置fetch选项
    emscripten_fetch_attr_t attr;
    emscripten_fetch_attr_init(&attr);
    strcpy(attr.requestMethod, "GET");
    attr.attributes = EMSCRIPTEN_FETCH_LOAD_TO_MEMORY;
    attr.userData = data;

    // 设置成功回调
    attr.onsuccess = [](emscripten_fetch_t *fetch)
    {
        CallbackData *data = static_cast<CallbackData *>(fetch->userData);

        INFO_LOG("✅ 图像下载成功: " << data->description << " 大小: " << fetch->numBytes << " 字节");

        if (fetch->numBytes > 0 && fetch->data)
        {
            // 测试STB_Image解码
            testSTBImageDecoding(
                reinterpret_cast<const unsigned char *>(fetch->data),
                fetch->numBytes,
                data->description);
        }
        else
        {
            ERROR_LOG("❌ 下载的图像数据为空: " << data->description);
            g_appContext->failureCount++;
        }

        delete data;
        emscripten_fetch_close(fetch);
    };

    // 设置错误回调
    attr.onerror = [](emscripten_fetch_t *fetch)
    {
        CallbackData *data = static_cast<CallbackData *>(fetch->userData);

        ERROR_LOG("❌ 图像下载失败: " << data->description << " HTTP状态: " << fetch->status);
        g_appContext->failureCount++;

        delete data;
        emscripten_fetch_close(fetch);
    };

    // 发起异步请求
    emscripten_fetch(&attr, url.c_str());
#else
    INFO_LOG("图像下载测试仅在WebAssembly环境中可用");
#endif
}

/**
 * 运行所有测试
 */
void runAllTests()
{
    INFO_LOG("=== STB_Image解码器测试开始 ===");

    // 测试不同的图像URL
    std::vector<std::pair<std::string, std::string>> testImages = {
        {"https://mt1.google.com/vt/lyrs=s&x=0&y=0&z=1", "谷歌地图瓦片 LOD1"},
        {"https://mt1.google.com/vt/lyrs=s&x=1&y=0&z=1", "谷歌地图瓦片 LOD1-2"},
        {"https://mt1.google.com/vt/lyrs=s&x=0&y=0&z=2", "谷歌地图瓦片 LOD2"},
        {"https://httpbin.org/image/png", "测试PNG图像"},
        {"https://httpbin.org/image/jpeg", "测试JPEG图像"}};

    for (const auto &testImage : testImages)
    {
        downloadAndTestImage(testImage.first, testImage.second);
    }

    INFO_LOG("已启动 " << testImages.size() << " 个图像下载测试");
}

/**
 * 主循环
 */
void mainLoop()
{
    // 处理事件
    SDL_Event event;
    while (SDL_PollEvent(&event))
    {
        if (event.type == SDL_QUIT)
        {
            g_appContext->shouldExit = true;
        }
    }

    // 简单的清屏渲染
    glClearColor(0.2f, 0.3f, 0.4f, 1.0f);
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

    SDL_GL_SwapWindow(g_appContext->window);

    // 定期输出测试结果
    static int frameCount = 0;
    frameCount++;
    if (frameCount % 300 == 0) // 每5秒输出一次
    {
        INFO_LOG("测试进度 - 成功: " << g_appContext->successCount
                                     << " 失败: " << g_appContext->failureCount);
    }
}

/**
 * 主函数
 */
int main()
{
    INFO_LOG("STB_Image解码器测试程序启动中...");

    // 创建应用上下文
    g_appContext = new AppContext();

    // 初始化SDL
    if (!initializeSDL())
    {
        delete g_appContext;
        return -1;
    }

    // 运行测试
    runAllTests();

    // 启动主循环
#ifdef EMSCRIPTEN
    emscripten_set_main_loop(mainLoop, 60, 1);
#else
    while (!g_appContext->shouldExit)
    {
        mainLoop();
        std::this_thread::sleep_for(std::chrono::milliseconds(16));
    }
#endif

    // 清理资源
    if (g_appContext->context)
        SDL_GL_DeleteContext(g_appContext->context);
    if (g_appContext->window)
        SDL_DestroyWindow(g_appContext->window);
    delete g_appContext;

    INFO_LOG("STB_Image测试完成");
    return 0;
}
