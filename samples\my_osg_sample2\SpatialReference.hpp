#pragma once

#include <osg/Referenced>
#include <osg/Vec3d>
#include <osg/Matrix>
#include <osg/CoordinateSystemNode>
#include <string>
#include <memory>

namespace EarthSample
{
  /**
   * 椭球体类，用于定义地球形状参数
   */
  class Ellipsoid
  {
  public:
    // 构造函数 - 默认WGS84椭球体
    Ellipsoid();
    Ellipsoid(double semiMajorAxis, double semiMinorAxis);

    // 设置/获取长半轴（赤道半径）
    void setSemiMajorAxis(double value) { _semiMajorAxis = value; }
    double getSemiMajorAxis() const { return _semiMajorAxis; }

    // 设置/获取短半轴（极半径）
    void setSemiMinorAxis(double value) { _semiMinorAxis = value; }
    double getSemiMinorAxis() const { return _semiMinorAxis; }

    // 椭球体名称
    void setName(const std::string &name) { _name = name; }
    const std::string &getName() const { return _name; }

    // 地心坐标到本地坐标系变换矩阵
    osg::Matrix geocentricToLocalToWorld(const osg::Vec3d &xyz) const;

    // 地心坐标到大地坐标转换
    osg::Vec3d geocentricToGeodetic(const osg::Vec3d &xyz) const;

    // 大地坐标到地心坐标转换
    osg::Vec3d geodeticToGeocentric(const osg::Vec3d &lla) const;

    // 获取本地上方向量
    osg::Vec3d geocentricToUpVector(const osg::Vec3d &xyz) const;

  private:
    double _semiMajorAxis;
    double _semiMinorAxis;
    std::string _name;
    osg::ref_ptr<osg::EllipsoidModel> _ellipsoidModel;
  };

  /**
   * 坐标系类型枚举
   */
  enum CoordinateSystemType
  {
    GEOGRAPHIC, // 地理坐标系（经纬度）
    PROJECTED,  // 投影坐标系
    GEOCENTRIC  // 地心坐标系
  };

  /**
   * 投影类型枚举
   */
  enum ProjectionType
  {
    PROJ_LONGLAT,             // 经纬度投影
    PROJ_MERCATOR,            // 墨卡托投影
    PROJ_WEB_MERCATOR,        // Web墨卡托投影（球面墨卡托）
    PROJ_UTM,                 // UTM投影
    PROJ_TRANSVERSE_MERCATOR, // 横轴墨卡托投影
    PROJ_PLATE_CARREE,        // 等距圆柱投影
    PROJ_GEOCENTRIC           // 地心投影
  };

  /**
   * 空间参考系统类
   * 管理坐标系统信息和坐标转换
   */
  class SpatialReference : public osg::Referenced
  {
  public:
    // 创建方法
    static SpatialReference *create(const std::string &wkt);
    static SpatialReference *createGeographic();
    static SpatialReference *createWebMercator();
    static SpatialReference *createUTM(int zone, bool northern = true);
    static SpatialReference *createGeocentric();

    // 基本属性
    bool isGeographic() const { return _type == GEOGRAPHIC; }
    bool isProjected() const { return _type == PROJECTED; }
    bool isGeocentric() const { return _type == GEOCENTRIC; }
    bool isMercator() const { return _projection == PROJ_MERCATOR || _projection == PROJ_WEB_MERCATOR; }
    bool isSphericalMercator() const { return _projection == PROJ_WEB_MERCATOR; }

    // 获取椭球体
    const Ellipsoid &getEllipsoid() const { return _ellipsoid; }

    // 获取WKT字符串
    const std::string &getWKT() const { return _wkt; }

    // 获取投影类型
    ProjectionType getProjectionType() const { return _projection; }

    // 获取坐标系统类型
    CoordinateSystemType getCoordinateSystemType() const { return _type; }

    // 获取名称
    const std::string &getName() const { return _name; }

    // 获取单位
    const std::string &getUnits() const { return _units; }

    // 坐标转换
    bool transform(const osg::Vec3d &input, const SpatialReference *outputSRS, osg::Vec3d &output) const;
    bool transformPoints(const std::vector<osg::Vec3d> &input, const SpatialReference *outputSRS, std::vector<osg::Vec3d> &output) const;

    // 获取相关坐标系统
    const SpatialReference *getGeographicSRS() const;
    const SpatialReference *getGeocentricSRS() const;

    // 创建本地到世界坐标变换矩阵
    bool createLocalToWorld(const osg::Vec3d &xyz, osg::Matrix &out_local2world) const;
    bool createWorldToLocal(const osg::Vec3d &xyz, osg::Matrix &out_world2local) const;

    // 填充OSG坐标系统节点
    bool populateCoordinateSystemNode(osg::CoordinateSystemNode *csn) const;

    // 验证坐标系统是否有效
    bool valid() const { return _valid; }

  protected:
    SpatialReference();
    virtual ~SpatialReference();

    // 初始化方法
    void init();
    void initFromWKT(const std::string &wkt);

    // 坐标转换实现
    bool transformGeographicToGeocentric(const osg::Vec3d &input, osg::Vec3d &output) const;
    bool transformGeocentricToGeographic(const osg::Vec3d &input, osg::Vec3d &output) const;
    bool transformGeographicToWebMercator(const osg::Vec3d &input, osg::Vec3d &output) const;
    bool transformWebMercatorToGeographic(const osg::Vec3d &input, osg::Vec3d &output) const;

  private:
    CoordinateSystemType _type;
    ProjectionType _projection;
    Ellipsoid _ellipsoid;
    std::string _wkt;
    std::string _name;
    std::string _units;
    std::string _datum;
    bool _valid;

    // 投影参数
    double _centralMeridian;
    double _falseEasting;
    double _falseNorthing;
    double _scaleFactor;
    int _utmZone;
    bool _utmNorthern;

    // 相关坐标系统缓存
    mutable osg::ref_ptr<SpatialReference> _geographicSRS;
    mutable osg::ref_ptr<SpatialReference> _geocentricSRS;
  };

  /**
   * 地理范围类
   */
  class GeoExtent
  {
  public:
    GeoExtent();
    GeoExtent(const SpatialReference *srs, double xmin, double ymin, double xmax, double ymax);

    // 获取坐标系统
    const SpatialReference *getSRS() const { return _srs.get(); }

    // 获取范围
    double xMin() const { return _xmin; }
    double yMin() const { return _ymin; }
    double xMax() const { return _xmax; }
    double yMax() const { return _ymax; }

    double width() const { return _xmax - _xmin; }
    double height() const { return _ymax - _ymin; }

    // 中心点
    osg::Vec2d center() const { return osg::Vec2d((_xmin + _xmax) * 0.5, (_ymin + _ymax) * 0.5); }

    // 范围操作
    bool intersects(const GeoExtent &other) const;
    bool contains(const osg::Vec2d &point) const;
    bool contains(double x, double y) const;

    // 变换到其他坐标系统
    GeoExtent transform(const SpatialReference *targetSRS) const;

    // 验证是否有效
    bool valid() const { return _srs.valid() && _xmin < _xmax && _ymin < _ymax; }

  private:
    osg::ref_ptr<const SpatialReference> _srs;
    double _xmin, _ymin, _xmax, _ymax;
  };

  /**
   * 地理点类
   */
  class GeoPoint
  {
  public:
    GeoPoint();
    GeoPoint(const SpatialReference *srs, double x, double y, double z = 0.0);
    GeoPoint(const SpatialReference *srs, const osg::Vec3d &xyz);

    // 获取坐标系统
    const SpatialReference *getSRS() const { return _srs.get(); }

    // 获取坐标
    double x() const { return _x; }
    double y() const { return _y; }
    double z() const { return _z; }

    const osg::Vec3d &vec3d() const { return _xyz; }

    // 设置坐标
    void set(const SpatialReference *srs, double x, double y, double z = 0.0);
    void set(const SpatialReference *srs, const osg::Vec3d &xyz);

    // 转换到其他坐标系统
    GeoPoint transform(const SpatialReference *targetSRS) const;
    bool transform(const SpatialReference *targetSRS, GeoPoint &output) const;

    // 转换到世界坐标
    osg::Vec3d toWorld() const;

    // 验证是否有效
    bool valid() const { return _srs.valid(); }

  private:
    osg::ref_ptr<const SpatialReference> _srs;
    double _x, _y, _z;
    osg::Vec3d _xyz;
  };
}