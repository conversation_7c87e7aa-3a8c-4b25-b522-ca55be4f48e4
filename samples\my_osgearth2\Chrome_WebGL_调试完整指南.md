# Chrome WebGL/WebAssembly 调试完整指南

## 🎯 目标
使用 Chrome 开发者工具深度调试 osgEarth WebGL 显示问题，精确定位错误位置。

## 🛠️ 准备工作

### 1. 编译调试版本
```bash
# 运行构建脚本
build_debug_version.bat
```

### 2. 启动本地服务器
```bash
# 在 redist_wasm 目录下
python -m http.server 8080
# 或者
python -m http.server 8081
```

### 3. 打开调试页面
```
http://localhost:8080/debug.html
```

## 🔍 Chrome 调试步骤

### 第一步：基础错误检查

#### 1.1 打开开发者工具
- 按 `F12` 或右键 -> 检查元素
- 确保所有面板都可见

#### 1.2 Console 面板检查
```javascript
// 查看关键错误信息
// 重点关注：
// - WebGL context creation errors
// - Shader compilation errors  
// - Memory allocation errors
// - Module loading errors
```

**常见错误模式**：
- `WebGL context lost`
- `Cannot read properties of undefined`
- `Shader compilation failed`
- `Out of memory`

#### 1.3 Network 面板检查
- 确认所有资源（.js, .wasm）正确加载
- 检查 HTTP 状态码
- 验证 MIME 类型设置

### 第二步：WebGL 专项调试

#### 2.1 启用 WebGL 调试扩展
在地址栏输入：
```
chrome://flags/#enable-webgl-developer-extensions
```
设置为 **Enabled** 并重启浏览器。

#### 2.2 安装 WebGL 调试工具
推荐扩展：
- **WebGL Inspector**
- **Spector.js**

#### 2.3 WebGL 状态检查
在 Console 中执行：
```javascript
// 检查 WebGL 支持
const canvas = document.getElementById('canvas');
const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
console.log('WebGL Context:', gl);
console.log('WebGL Version:', gl.getParameter(gl.VERSION));
console.log('Vendor:', gl.getParameter(gl.VENDOR));
console.log('Renderer:', gl.getParameter(gl.RENDERER));

// 检查错误
console.log('GL Error:', gl.getError());
```

### 第三步：WebAssembly 调试

#### 3.1 Sources 面板设置
1. 展开 `wasm://` 节点
2. 找到 `osgearth_myviewer_debug.wasm`
3. 在关键函数设置断点：
   - `main`
   - `initializeViewer`
   - `mainLoop`

#### 3.2 断点调试技巧
```javascript
// 在 Console 中调用调试函数
Module.getDebugStatus();
Module.getLastError();
Module.getFrameCount();
Module.forceFrame();
```

#### 3.3 内存调试
在 Memory 面板：
1. 选择 "Heap snapshot"
2. 点击 "Take snapshot"
3. 查找 WebAssembly 相关内存使用

### 第四步：性能分析

#### 4.1 Performance 面板
1. 点击录制按钮
2. 等待几秒钟
3. 停止录制
4. 分析：
   - Main thread activity
   - GPU process
   - WebGL calls

#### 4.2 关键性能指标
- **Frame rate**: 应该接近 60 FPS
- **GPU memory**: 检查是否有内存泄漏
- **Draw calls**: 每帧的绘制调用次数

### 第五步：深度错误定位

#### 5.1 使用调试版本的特殊功能
```javascript
// 实时监控（每秒更新）
setInterval(() => {
    console.log('Status:', Module.getDebugStatus());
    console.log('Error:', Module.getLastError());
    console.log('Viewer Info:', Module.getViewerInfo());
}, 1000);
```

#### 5.2 手动触发渲染
```javascript
// 强制渲染一帧来测试
Module.forceFrame();
```

#### 5.3 检查 OSG 状态
```javascript
// 检查查看器是否正在运行
console.log('Viewer Running:', Module.isViewerRunning());
```

## 🎯 常见问题诊断

### 问题 1: 黑屏无显示
**检查步骤**：
1. Console 是否有 WebGL 错误
2. Canvas 尺寸是否正确
3. WebGL 上下文是否创建成功
4. Shader 是否编译成功

**调试代码**：
```javascript
const canvas = document.getElementById('canvas');
console.log('Canvas size:', canvas.width, canvas.height);
const gl = canvas.getContext('webgl2');
console.log('WebGL context:', gl);
if (gl) {
    console.log('GL Error:', gl.getError());
}
```

### 问题 2: 模块加载失败
**检查步骤**：
1. Network 面板查看 .wasm 文件加载
2. Console 查看模块初始化错误
3. 检查 CORS 设置

### 问题 3: 渲染性能问题
**检查步骤**：
1. Performance 面板录制
2. 查看 GPU 使用率
3. 检查内存使用情况

## 📊 调试信息收集

### 自动收集脚本
```javascript
function collectDebugInfo() {
    const info = {
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        webgl: {
            supported: !!canvas.getContext('webgl2'),
            version: gl ? gl.getParameter(gl.VERSION) : 'N/A',
            vendor: gl ? gl.getParameter(gl.VENDOR) : 'N/A',
            renderer: gl ? gl.getParameter(gl.RENDERER) : 'N/A'
        },
        module: {
            status: Module.getDebugStatus ? Module.getDebugStatus() : 'N/A',
            error: Module.getLastError ? Module.getLastError() : 'N/A',
            frameCount: Module.getFrameCount ? Module.getFrameCount() : 0
        },
        canvas: {
            width: canvas.width,
            height: canvas.height
        }
    };
    
    console.log('Debug Info:', JSON.stringify(info, null, 2));
    return info;
}

// 每 5 秒收集一次信息
setInterval(collectDebugInfo, 5000);
```

## 🔧 高级调试技巧

### 1. WebGL 调用跟踪
```javascript
// 包装 WebGL 调用以添加日志
function wrapGLContext(gl) {
    const originalMethods = {};
    
    for (let method in gl) {
        if (typeof gl[method] === 'function') {
            originalMethods[method] = gl[method];
            gl[method] = function(...args) {
                console.log(`GL.${method}(${args.join(', ')})`);
                const result = originalMethods[method].apply(this, args);
                const error = this.getError();
                if (error !== this.NO_ERROR) {
                    console.error(`GL Error after ${method}:`, error);
                }
                return result;
            };
        }
    }
}
```

### 2. 内存使用监控
```javascript
function monitorMemory() {
    if (performance.memory) {
        console.log('Memory:', {
            used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + ' MB',
            total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + ' MB',
            limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024) + ' MB'
        });
    }
}

setInterval(monitorMemory, 2000);
```

### 3. 帧率监控
```javascript
let frameCount = 0;
let lastTime = performance.now();

function monitorFPS() {
    frameCount++;
    const currentTime = performance.now();
    
    if (currentTime - lastTime >= 1000) {
        console.log('FPS:', frameCount);
        frameCount = 0;
        lastTime = currentTime;
    }
    
    requestAnimationFrame(monitorFPS);
}

monitorFPS();
```

## 📝 调试报告模板

### 问题报告格式
```
问题描述：
- 现象：[详细描述]
- 重现步骤：[步骤列表]
- 预期结果：[期望看到的结果]
- 实际结果：[实际发生的情况]

环境信息：
- 浏览器：[Chrome 版本]
- 操作系统：[系统版本]
- WebGL 版本：[从调试信息获取]

错误信息：
- Console 错误：[复制错误信息]
- WebGL 错误：[GL 错误代码]
- 模块错误：[Module.getLastError()]

调试数据：
- 帧数：[Module.getFrameCount()]
- 状态：[Module.getDebugStatus()]
- 内存使用：[从 Performance 面板获取]
```

## 🎉 成功标准

调试成功的标志：
1. ✅ WebGL 上下文创建成功
2. ✅ 模块加载无错误
3. ✅ 查看器初始化完成
4. ✅ 渲染循环正常运行
5. ✅ 画布显示内容（即使是简单的颜色）

---

**使用此指南，您应该能够精确定位 osgEarth WebGL 显示问题的根本原因！**
