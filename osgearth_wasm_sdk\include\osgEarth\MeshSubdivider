/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#ifndef OSGEARTHSYMBOLOGY_MESH_SUBDIVIDER
#define OSGEARTHSYMBOLOGY_MESH_SUBDIVIDER

#include <osgEarth/Common>
#include <osgEarth/GeoMath>
#include <osg/Geometry>
#include <osg/PrimitiveSet>

namespace osgEarth
{
    /**
     * Recursively subdivides (tesselates) a geocentric geometry mesh to the specified
     * granularity. This class will densify a geocentric primitive to more accurately
     * conform to the ellipsoidal surface of the earth, for example.
     */
    class OSGEARTH_EXPORT MeshSubdivider
    {
    public:
        /**
         * Construct a new subdivider.
         * Optionally, provide localizer/delocalizer transforms that will take
         * vertex locations in and out of world geocentric coordinates.
         */
        MeshSubdivider(
            const osg::Matrixd& world2local =osg::Matrixd::identity(),
            const osg::Matrixd& local2world =osg::Matrixd::identity() );

        /**
         * Sets the maximum number elements in each generated primitive set.
         */
        void setMaxElementsPerEBO( unsigned int value ) {
            _maxElementsPerEBO = value; }

        /**
         * Subdivides an OSG geometry's primitives to the specified granularity.
         * Granularity is an angle, specified in radians - it is the maximum
         * allowable angle between two points in a triangle.
         *
         * This method will also coalesce all the polygonal primitive sets in the geometry
         * into a single GL_TRIANGLES primitive.
         *
         * Note! This utility currently does nothing with repsect to the geometry's
         * color or texture attributes, so it is best used prior to setting those.
         */
        void run(
            osg::Geometry&   geom, 
            double           granurality_radians,
            GeoInterpolation interp =GEOINTERP_RHUMB_LINE );

    protected:
        osg::Matrixd _local2world, _world2local;
        unsigned int _maxElementsPerEBO;
    };
} // namespace osgEarth

#endif // OSGEARTHSYMBOLOGY_MESH_SUBDIVIDER
