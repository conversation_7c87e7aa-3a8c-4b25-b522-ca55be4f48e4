/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#ifndef OSGEARTH_TEXT_H
#define OSGEARTH_TEXT_H 1

#include <osgEarth/Common>
#include <osgText/Text>

namespace osgEarth
{
    /**
     * Text implementaiton that handles differences between pre-3.6 and post-3.6
     * osgText and adds support for virtual program composition
     */
    class OSGEARTH_EXPORT Text : public osgText::Text
    {
    public:
        Text();
        Text(const Text& rhs, const osg::CopyOp& copy =osg::CopyOp::SHALLOW_COPY);
        Text(const std::string& str);
        
        virtual osg::Object* cloneType() const { return new osgEarth::Text(); }
        virtual osg::Object* clone(const osg::CopyOp& copyop) const { return new osgEarth::Text(*this,copyop); }
        virtual bool isSameKindAs(const osg::Object* obj) const { return dynamic_cast<const osgEarth::Text*>(obj)!=NULL; }
        virtual const char* libraryName() const { return "osgEarth"; }
        
        virtual void setFont(osg::ref_ptr<osgText::Font>); // <= OSG 3.5.7

    protected:
        virtual ~Text();
        virtual osg::StateSet* createStateSet(); // >= OSG 3.5.8
    };
}

#endif // OSGEARTH_TEXT_H
