#include <osgViewer/Viewer>
#include <osgGA/TrackballManipulator>
#include <osgEarth/MapNode>
#include <osgEarth/Map>
#include <osgEarth/ImageLayer>
#include <osgEarth/XYZ>
#include <osgEarth/Profile>
#include <osgEarth/URI>
#include <osgEarth/GLUtils>
#include <osgEarth/Registry>
#include <osgGA/StateSetManipulator>
#include <osgGA/GUIEventHandler>
#include <osgViewer/ViewerEventHandlers>
#include <osg/Group>
#include <iostream>
#include <string>

#ifdef __EMSCRIPTEN__
#include <emscripten.h>
#include <emscripten/html5.h>
#endif

#define DEBUG_LOG(msg) std::cout << "[DEBUG] " << msg << std::endl
#define ERROR_LOG(msg) std::cerr << "[ERROR] " << msg << std::endl
#define INFO_LOG(msg) std::cout << "[INFO] " << msg << std::endl

// 全局变量
static osgViewer::Viewer *g_viewer = nullptr;
static osgEarth::MapNode *g_mapNode = nullptr;
static int g_frameCount = 0;
static bool g_initialized = false;
static std::string g_lastError = "";

/**
 * 创建简单地图
 */
osgEarth::Map* createSimpleMap()
{
    DEBUG_LOG("Creating simple map...");
    
    // 创建地图，使用地理坐标系
    osgEarth::Map* map = new osgEarth::Map();
    map->setProfile(osgEarth::Profile::create(osgEarth::Profile::GLOBAL_GEODETIC));
    
    DEBUG_LOG("Simple map created successfully");
    return map;
}

/**
 * 初始化查看器 - 不创建图形上下文版本
 */
bool initializeViewer()
{
    DEBUG_LOG("Initializing viewer without graphics context...");
    
    try
    {
        // 创建查看器
        g_viewer = new osgViewer::Viewer();
        
        // 设置单线程模式
        g_viewer->setThreadingModel(osgViewer::Viewer::SingleThreaded);
        g_viewer->setRunFrameScheme(osgViewer::Viewer::CONTINUOUS);

#ifdef __EMSCRIPTEN__
        // WebAssembly 环境设置 - 不创建窗口
        DEBUG_LOG("WebAssembly mode - setting up without window creation");
        
        // 不调用 setUpViewerAsEmbeddedInWindow，避免创建图形上下文
        // g_viewer->setUpViewerAsEmbeddedInWindow(0, 0, 800, 600);
        
        // 手动设置相机
        osg::Camera* camera = g_viewer->getCamera();
        camera->setClearColor(osg::Vec4(0.1f, 0.1f, 0.3f, 1.0f));
        camera->setClearMask(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
        
        // 设置投影
        camera->setProjectionMatrixAsPerspective(45.0, 800.0/600.0, 1.0, 1e12);
        camera->setComputeNearFarMode(osg::CullSettings::COMPUTE_NEAR_FAR_USING_BOUNDING_VOLUMES);
        
        // 设置视口
        camera->setViewport(new osg::Viewport(0, 0, 800, 600));
#else
        g_viewer->setUpViewInWindow(100, 100, 800, 600);
#endif
        
        // 创建地图
        osgEarth::Map* map = createSimpleMap();
        g_mapNode = new osgEarth::MapNode(map);
        
        // 设置场景
        g_viewer->setSceneData(g_mapNode);
        
        // 添加操作器
        g_viewer->setCameraManipulator(new osgGA::TrackballManipulator());
        
        // 添加事件处理器
        g_viewer->addEventHandler(new osgGA::StateSetManipulator(g_viewer->getCamera()->getOrCreateStateSet()));
        g_viewer->addEventHandler(new osgViewer::StatsHandler());
        g_viewer->addEventHandler(new osgViewer::WindowSizeHandler());
        
#ifdef __EMSCRIPTEN__
        // WebAssembly 环境下完全跳过 realize()
        DEBUG_LOG("WebAssembly mode: Completely skipping realize()");
        g_viewer->setDone(false);
        
        // 手动初始化一些必要的状态
        g_viewer->getCamera()->getOrCreateStateSet();
#else
        g_viewer->realize();
#endif
        
        DEBUG_LOG("Viewer initialized successfully without graphics context");
        g_initialized = true;
        return true;
    }
    catch (const std::exception& e)
    {
        ERROR_LOG("Exception in initializeViewer: " << e.what());
        return false;
    }
}

/**
 * 主循环函数
 */
void mainLoop()
{
    static bool firstFrame = true;
    static int frameCount = 0;
    
    if (firstFrame)
    {
        DEBUG_LOG("First frame - initializing...");
        if (!initializeViewer())
        {
            ERROR_LOG("Failed to initialize viewer");
            return;
        }
        firstFrame = false;
    }
    
    if (g_viewer && g_initialized && !g_viewer->done())
    {
        try
        {
            // 只执行更新遍历，跳过渲染
            g_viewer->updateTraversal();
            
            frameCount++;
            if (frameCount % 60 == 0)
            {
                DEBUG_LOG("Frame " << frameCount << " - Update traversal completed");
            }
        }
        catch (const std::exception& e)
        {
            ERROR_LOG("Exception during frame: " << e.what());
        }
    }
}

/**
 * 主函数
 */
int main()
{
    std::cout << "========================================" << std::endl;
    std::cout << "osgEarth No Context Test" << std::endl;
    std::cout << "========================================" << std::endl;
    
    // 初始化 osgEarth - 不创建图形上下文
    DEBUG_LOG("Initializing osgEarth without graphics context...");
    
    try 
    {
        // 直接初始化 osgEarth，让它使用默认的能力检测
        osgEarth::initialize();
        DEBUG_LOG("osgEarth initialized successfully");
    }
    catch (const std::exception& e)
    {
        ERROR_LOG("Failed to initialize osgEarth: " << e.what());
        return -1;
    }

#ifdef __EMSCRIPTEN__
    INFO_LOG("Starting Emscripten main loop...");
    emscripten_set_main_loop(mainLoop, 60, 1);
#else
    INFO_LOG("Starting desktop main loop...");
    
    if (!initializeViewer())
    {
        ERROR_LOG("Failed to initialize viewer");
        return -1;
    }
    
    while (!g_viewer->done())
    {
        mainLoop();
    }
#endif
    
    return 0;
}
