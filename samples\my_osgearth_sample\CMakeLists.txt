# OSGEarth WebAssembly - Simple Digital Earth Application
# 基于参考实现的成功经验，集成完整的WebGL兼容性和瓦片管理系统
# 支持多线程和完整的编译优化

cmake_minimum_required(VERSION 3.10)
project(osgearth_simple_earth)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

if(EMSCRIPTEN)
    include_directories(SYSTEM ${CMAKE_JS_INC_PATH})
endif()

# osgEarth 库路径
set(OSGEARTH_WASM_LIB_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../../../redist_wasm")
set(OSG_WASM_LIB_DIR "F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep")

# 包含头文件目录
include_directories(
    "${CMAKE_CURRENT_SOURCE_DIR}/../../"
    "${CMAKE_CURRENT_SOURCE_DIR}/../../../src/osgEarth"
    "${OSG_WASM_LIB_DIR}/include"
)

# 源文件
set(SOURCES
    main.cpp
)

# 头文件
set(HEADERS
    stb_image.h
)

# 创建可执行文件
add_executable(osgearth_simple_earth ${SOURCES} ${HEADERS})

if(EMSCRIPTEN)
    # osgEarth WASM 静态库
    set(OSGEARTH_LIBRARIES
        "${OSGEARTH_WASM_LIB_DIR}/libosgEarth.a"
        "${OSGEARTH_WASM_LIB_DIR}/osgdb_earth.a"
        "${OSGEARTH_WASM_LIB_DIR}/osgdb_osgearth_engine_rex.a"
        "${OSGEARTH_WASM_LIB_DIR}/osgdb_osgearth_cache_filesystem.a"
        "${OSGEARTH_WASM_LIB_DIR}/osgdb_osgearth_sky_simple.a"
        "${OSGEARTH_WASM_LIB_DIR}/osgdb_osgearth_tileindex.a"
        "${OSGEARTH_WASM_LIB_DIR}/osgdb_osgearth_xyz.a"
    )
    
    # OSG WASM 静态库
    set(OSG_LIBRARIES
        "${OSG_WASM_LIB_DIR}/lib/libosg.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgViewer.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgDB.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgGA.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgUtil.a"
        "${OSG_WASM_LIB_DIR}/lib/libOpenThreads.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgText.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgShadow.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgSim.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgAnimation.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgFX.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgManipulator.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgParticle.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgPresentation.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgTerrain.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgVolume.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgWidget.a"
    )
    
    # 第三方依赖库
    set(THIRD_PARTY_LIBRARIES
        "${OSG_WASM_LIB_DIR}/lib/libGeographicLib.a"
        "${OSG_WASM_LIB_DIR}/lib/libgeos.a"
        "${OSG_WASM_LIB_DIR}/lib/libgeos_c.a"
        "${OSG_WASM_LIB_DIR}/lib/libproj.a"
        "${OSG_WASM_LIB_DIR}/lib/libcurl.a"
        "${OSG_WASM_LIB_DIR}/lib/libz.a"
        "${OSG_WASM_LIB_DIR}/lib/libexpat.a"
        "${OSG_WASM_LIB_DIR}/lib/libpng16.a"
        "${OSG_WASM_LIB_DIR}/lib/libjpeg.a"
        "${OSG_WASM_LIB_DIR}/lib/libtiff.a"
        "${OSG_WASM_LIB_DIR}/lib/libfreetype.a"
        "${OSG_WASM_LIB_DIR}/lib/libfontconfig.a"
        "${OSG_WASM_LIB_DIR}/lib/libbz2.a"
        "${OSG_WASM_LIB_DIR}/lib/liblzma.a"
        "${OSG_WASM_LIB_DIR}/lib/libssl.a"
        "${OSG_WASM_LIB_DIR}/lib/libcrypto.a"
        "${OSG_WASM_LIB_DIR}/lib/libwebp.a"
        "${OSG_WASM_LIB_DIR}/lib/libwebpmux.a"
        "${OSG_WASM_LIB_DIR}/lib/libwebpdemux.a"
    )

    # 链接所有库
    target_link_libraries(osgearth_simple_earth
        ${OSGEARTH_LIBRARIES}
        ${OSG_LIBRARIES}
        ${THIRD_PARTY_LIBRARIES}
    )
    
    # 编译器标志
    set(EM_COMPILE_FLAGS
        "-DOSG_GLSL_VERSION=300"
        "-DOSGEARTH_HAVE_GEOS=1"
        "-DOSGEARTH_HAVE_GDAL=0"
        "-DOSGEARTH_HAVE_PROJ=1"
        "-DUSE_EXTERNAL_WASM_DEPENDS=ON"
        "-DSTB_IMAGE_IMPLEMENTATION"
        "-DSTBI_NO_STDIO"
        "-DSTBI_NO_FAILURE_STRINGS"
        "-DSTBI_ONLY_PNG"
        "-DSTBI_ONLY_JPEG"
        "-s USE_WEBGL2=1"
        "-s ALLOW_MEMORY_GROWTH=1"
        "-s MAXIMUM_MEMORY=2GB"
        "-s FETCH=1"
        "-O3"
    )

    # 链接器标志 - 支持多线程版本
    set(EM_LINK_FLAGS_LIST
        "-s USE_SDL=2"
        "-s USE_WEBGL2=1"
        "-s FULL_ES3=1"
        "-s WASM=1"
        "-s INITIAL_MEMORY=268435456"  # 256MB
        "-s ALLOW_MEMORY_GROWTH=1"
        "-s MAXIMUM_MEMORY=2147483648"  # 2GB
        "-s FETCH=1"
        "-s ASYNCIFY=1"
        "-s ASSERTIONS=1"
        "-s SAFE_HEAP=0"
        "-s STACK_OVERFLOW_CHECK=1"
        "-s EXPORTED_FUNCTIONS=['_main']"
        "-s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','HEAPU8','getValue','setValue']"
        "-s NO_EXIT_RUNTIME=1"
        "-s DISABLE_EXCEPTION_CATCHING=0"
        "-s FORCE_FILESYSTEM=1"
        "-s PTHREAD_POOL_SIZE=4"
        "-s USE_PTHREADS=1"
        "-s PROXY_TO_PTHREAD=1"
        "-s OFFSCREENCANVAS_SUPPORT=1"
        "-s OFFSCREEN_FRAMEBUFFER=1"
        "-s MODULARIZE=1"
        "-s EXPORT_NAME='createOSGEarthModule'"
        "-s ENVIRONMENT=web,worker"
        "-s SHARED_MEMORY=1"
        "-s TEXTDECODER=2"
        "-s ABORTING_MALLOC=0"
        "-s ALLOW_UNIMPLEMENTED_SYSCALLS=1"
        "-O3"
        "--bind"
    )
    
    # 如果有数据目录，预加载数据
    if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/data")
        list(APPEND EM_LINK_FLAGS_LIST "--preload-file ${CMAKE_CURRENT_SOURCE_DIR}/data@/data")
    endif()
    
    string(REPLACE ";" " " EM_LINK_FLAGS "${EM_LINK_FLAGS_LIST}")

    # 设置编译器标志
    target_compile_options(osgearth_simple_earth PRIVATE ${EM_COMPILE_FLAGS})
    
    # 设置链接器属性
    set_target_properties(osgearth_simple_earth PROPERTIES
        SUFFIX ".html"
        LINK_FLAGS "${EM_LINK_FLAGS}"
    )

    # 创建自定义HTML模板
    set(HTML_TEMPLATE_CONTENT "<!DOCTYPE html>
<html>
<head>
    <meta charset=\"utf-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>OSGEarth WebAssembly Digital Earth</title>
    <style>
        body { margin: 0; padding: 0; background: #000; }
        canvas { display: block; margin: 0 auto; }
        #status { position: absolute; top: 10px; left: 10px; color: white; font-family: Arial; }
        #controls { position: absolute; top: 10px; right: 10px; color: white; font-family: Arial; }
    </style>
</head>
<body>
    <div id=\"status\">Loading...</div>
    <div id=\"controls\">
        <div>鼠标左键: 拖拽旋转</div>
        <div>鼠标滚轮: 缩放</div>
        <div>R键: 重置视角</div>
    </div>
    <canvas id=\"canvas\" oncontextmenu=\"event.preventDefault()\"></canvas>
    <script>
        var Module = {
            preRun: [],
            postRun: [],
            print: function(text) {
                console.log(text);
            },
            printErr: function(text) {
                console.error(text);
            },
            canvas: (function() {
                var canvas = document.getElementById('canvas');
                canvas.width = 800;
                canvas.height = 600;
                return canvas;
            })(),
            setStatus: function(text) {
                document.getElementById('status').innerHTML = text;
            },
            totalDependencies: 0,
            monitorRunDependencies: function(left) {
                this.totalDependencies = Math.max(this.totalDependencies, left);
                Module.setStatus(left ? 'Preparing... (' + (this.totalDependencies-left) + '/' + this.totalDependencies + ')' : 'All downloads complete.');
            }
        };
        Module.setStatus('Downloading...');
        window.onerror = function(event) {
            Module.setStatus('Exception thrown, see JavaScript console');
        };
    </script>
    {{{ SCRIPT }}}
</body>
</html>")

    # 创建HTML模板文件
    file(WRITE "${CMAKE_CURRENT_BINARY_DIR}/shell_template.html" "${HTML_TEMPLATE_CONTENT}")
    
    # 添加自定义HTML模板到链接标志
    set_target_properties(osgearth_simple_earth PROPERTIES
        LINK_FLAGS "${EM_LINK_FLAGS} --shell-file ${CMAKE_CURRENT_BINARY_DIR}/shell_template.html"
    )

    message(STATUS "OSGEarth WebAssembly build configured with multi-threading support")
    message(STATUS "Output will be: osgearth_simple_earth.html, osgearth_simple_earth.js, osgearth_simple_earth.wasm")
    message(STATUS "Compile flags: ${EM_COMPILE_FLAGS}")
    message(STATUS "Link flags: ${EM_LINK_FLAGS}")
    
else()
    # 桌面版本配置
    find_package(OpenSceneGraph REQUIRED)
    find_package(osgEarth REQUIRED)
    
    target_link_libraries(osgearth_simple_earth
        ${OPENSCENEGRAPH_LIBRARIES}
        ${OSGEARTH_LIBRARIES}
    )
endif()

# 安装规则
install(TARGETS osgearth_simple_earth
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib)

if(EMSCRIPTEN)
    # 创建发布目录
    set(REDIST_DIR "${CMAKE_CURRENT_SOURCE_DIR}/redist_wasm")
    file(MAKE_DIRECTORY "${REDIST_DIR}")
    
    # 安装WebAssembly文件到 redist_wasm 目录
    install(FILES 
        "${CMAKE_CURRENT_BINARY_DIR}/osgearth_simple_earth.html"
        "${CMAKE_CURRENT_BINARY_DIR}/osgearth_simple_earth.js"
        "${CMAKE_CURRENT_BINARY_DIR}/osgearth_simple_earth.wasm"
        DESTINATION "${REDIST_DIR}"
        OPTIONAL)
    
    # 创建编译后自动复制的自定义目标
    add_custom_command(TARGET osgearth_simple_earth POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
            "${CMAKE_CURRENT_BINARY_DIR}/osgearth_simple_earth.html"
            "${CMAKE_CURRENT_BINARY_DIR}/osgearth_simple_earth.js"
            "${CMAKE_CURRENT_BINARY_DIR}/osgearth_simple_earth.wasm"
            "${REDIST_DIR}/"
        COMMENT "Copying WebAssembly files to redist_wasm directory"
    )
endif()

# 创建编译脚本
if(EMSCRIPTEN)
    set(BUILD_SCRIPT_CONTENT "#!/usr/bin/env pwsh
# OSGEarth WebAssembly 编译脚本
# 支持多线程和完整优化

Write-Host \"OSGEarth WebAssembly 编译开始...\" -ForegroundColor Green

# 设置 Emscripten 环境
\$env:EMSDK = \"C:/dev/emsdk\"
\$env:PATH = \"\$env:EMSDK;\" + \$env:PATH

# 激活 Emscripten
& \"\$env:EMSDK/emsdk_env.ps1\"

# 创建构建目录
if (-not (Test-Path \"build_wasm\")) {
    New-Item -ItemType Directory -Path \"build_wasm\"
}

Set-Location \"build_wasm\"

# 配置 CMake
Write-Host \"配置 CMake...\" -ForegroundColor Yellow
emcmake cmake -DUSE_EXTERNAL_WASM_DEPENDS=ON -DCMAKE_BUILD_TYPE=Release -DOSGEARTH_BUILD_SHARED_LIBS=OFF ..

# 编译
Write-Host \"开始编译...\" -ForegroundColor Yellow
emmake make -j4

# 检查编译结果
if (Test-Path \"osgearth_simple_earth.wasm\") {
    Write-Host \"编译成功!\" -ForegroundColor Green
    
    # 显示文件大小
    \$wasmSize = (Get-Item \"osgearth_simple_earth.wasm\").Length
    \$jsSize = (Get-Item \"osgearth_simple_earth.js\").Length
    \$htmlSize = (Get-Item \"osgearth_simple_earth.html\").Length
    
    Write-Host \"文件大小:\" -ForegroundColor Cyan
    Write-Host \"  WASM: \$([math]::Round(\$wasmSize/1MB, 2)) MB\" -ForegroundColor White
    Write-Host \"  JS:   \$([math]::Round(\$jsSize/1KB, 2)) KB\" -ForegroundColor White
    Write-Host \"  HTML: \$([math]::Round(\$htmlSize/1KB, 2)) KB\" -ForegroundColor White
    
    # 复制到发布目录
    if (-not (Test-Path \"../redist_wasm\")) {
        New-Item -ItemType Directory -Path \"../redist_wasm\"
    }
    
    Copy-Item \"osgearth_simple_earth.*\" -Destination \"../redist_wasm/\" -Force
    Write-Host \"文件已复制到 redist_wasm 目录\" -ForegroundColor Green
    
    Write-Host \"可以通过 HTTP 服务器访问: http://localhost:8000/osgearth_simple_earth.html\" -ForegroundColor Cyan
} else {
    Write-Host \"编译失败!\" -ForegroundColor Red
}

Set-Location ..
")
    
    file(WRITE "${CMAKE_CURRENT_SOURCE_DIR}/build_wasm.ps1" "${BUILD_SCRIPT_CONTENT}")
    
    # 创建快速编译脚本
    set(QUICK_BUILD_SCRIPT "#!/usr/bin/env pwsh
# 快速编译脚本

Set-Location \"build_wasm\"
emmake make -j4
if (Test-Path \"osgearth_simple_earth.wasm\") {
    Copy-Item \"osgearth_simple_earth.*\" -Destination \"../redist_wasm/\" -Force
    Write-Host \"快速编译完成!\" -ForegroundColor Green
}
Set-Location ..
")
    
    file(WRITE "${CMAKE_CURRENT_SOURCE_DIR}/quick_build.ps1" "${QUICK_BUILD_SCRIPT}")
    
    message(STATUS "Created build scripts: build_wasm.ps1, quick_build.ps1")
endif()
