/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/Common>
#include <osgEarth/Color>
#include <osgEarth/Config>
#include <osgEarth/Units>
#include <osgEarth/Expression>


namespace osgEarth
{
    /**
     * Drawing parameters for a line.
     */
    class OSGEARTH_EXPORT Stroke
    {
    public:

        /** Style for rendering the end caps of a line string. */
        enum LineCapStyle
        {
            LINECAP_FLAT,   /** no endcap. the line ends at the terminal point. */
            LINECAP_SQUARE, /** endcap extends width()/2 past the terminal point and is squared off. */
            LINECAP_ROUND   /** endcap extends width()/2 past the terminal point and is rounded off. */
        };

        /** Style for rendering the joins between line segments. */
        enum LineJoinStyle
        {
            LINEJOIN_MITRE, /** outside joins form a sharp point. */
            LINEJOIN_ROUND  /** outside joins form an arc. */
        };

    public:
        Stroke() = default;
        Stroke(float r, float g, float b, float a );
        Stroke(const Color& color );
        Stroke(const Config& conf );
        Stroke(const Stroke& rhs);

        /** Line color. */
        OE_PROPERTY(Color, color, Color::White);

        /** Capping of line ends. */
        OE_OPTION(LineCapStyle, lineCap, LINECAP_FLAT);

        /** How to render line joints in a LineString. */
        OE_OPTION(LineJoinStyle, lineJoin, LINEJOIN_ROUND);

        /** Line rendering width. */
        OE_OPTION(Expression<Distance>, width, { "1px" });

        /** Units for the width property. (default = Units::PIXELS) */
        OE_OPTION(UnitsType, widthUnits, Units::PIXELS);

        /** Minimum with of a line in pixels (default = 0.0, no minimum).
            This typically only applies to lines with map unit width, and
            tells the renderer to maintain a minimum pixel width so that 
            the geometry is always visible. */
        OE_OPTION(float, minPixels, 0.0f);

        /** Stippling pattern. Bitmask of pixels to draw. */
        OE_OPTION(std::uint16_t, stipplePattern, 0xFFFF);

        /** Stippling factor; number of times to repeat each bit in the stipplePattern. */
        OE_OPTION(std::uint32_t, stippleFactor, 1);

        /** Smoothing/antialiasing */
        OE_OPTION(bool, smooth, true);

        /** Rounding ratio - when rounding corners/caps, this is the ratio
            of rounded-segment length to width(). The smaller this value,
            the more detailed the rounding will be. (Default is 0.4) */
        OE_OPTION(float, roundingRatio, 0.4f);

        //! Outline color
        OE_OPTION(Color, outlineColor, Color::White);

        //! Outline stroke width (pixels)
        OE_OPTION(Expression<Distance>, outlineWidth, { "1px" });

    public:
        Config getConfig() const;
        void mergeConfig(const Config& conf);
    };
} // namespace osgEarth

OSGEARTH_SPECIALIZE_CONFIG(osgEarth::Stroke);
