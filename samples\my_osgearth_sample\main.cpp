// OSGEarth WebAssembly 简化数字地球应用
// 基于参考实现的成功经验，集成完整的WebGL兼容性和瓦片管理系统
// 显示带有谷歌地图瓦片的数字地球

// WebAssembly/Emscripten 兼容性修复
#define _LIBCPP_HAS_NO_PRAGMA_SYSTEM_HEADER
#define _LIBCPP_DISABLE_AVAILABILITY

// 必须首先包含所有STL头文件（WebAssembly/Emscripten要求）
#include <iostream>
#include <memory>
#include <vector>
#include <string>
#include <sstream>
#include <map>
#include <unordered_map>
#include <set>
#include <unordered_set>
#include <algorithm>
#include <cmath>
#include <functional>
#include <cstring>
#include <cstdlib>
#include <cstdint>
#include <limits>
#include <utility>
#include <iterator>
#include <typeinfo>

// 强制包含完整的STL容器定义
#include <deque>
#include <list>
#include <queue>
#include <stack>

// OSG 核心头文件
#include <osg/ref_ptr>
#include <osg/Group>
#include <osg/MatrixTransform>
#include <osg/Geode>
#include <osg/Geometry>
#include <osg/Shape>
#include <osg/ShapeDrawable>
#include <osg/Texture2D>
#include <osg/Material>
#include <osg/StateSet>
#include <osg/CullFace>
#include <osg/Depth>
#include <osg/BlendFunc>
#include <osg/AlphaFunc>
#include <osg/PolygonMode>
#include <osg/ShadeModel>
#include <osg/Point>
#include <osg/LineWidth>
#include <osg/Viewport>
#include <osg/Camera>
#include <osg/Light>
#include <osg/LightSource>
#include <osg/LightModel>
#include <osg/Image>
#include <osg/Program>
#include <osg/Shader>
#include <osg/Uniform>
#include <osg/TexEnv>
#include <osg/Math>

// OSG 查看器和交互
#include <osgViewer/Viewer>
#include <osgViewer/ViewerEventHandlers>
#include <osgGA/TrackballManipulator>
#include <osgGA/StateSetManipulator>
#include <osgGA/GUIEventHandler>
#include <osgUtil/LineSegmentIntersector>

// OSG 数据库和工具
#include <osgDB/ReadFile>
#include <osgDB/WriteFile>
#include <osgDB/Registry>
#include <osgUtil/Optimizer>

// osgEarth 核心头文件
#include <osgEarth/Map>
#include <osgEarth/MapNode>
#include <osgEarth/ImageLayer>
#include <osgEarth/TileSourceImageLayer>
#include <osgEarth/TileSource>
#include <osgEarth/Profile>
#include <osgEarth/Registry>
#include <osgEarth/Capabilities>
#include <osgEarth/Cache>
#include <osgEarth/CachePolicy>
#include <osgEarth/XYZ>
#include <osgEarth/URI>
#include <osgEarth/Config>

// osgEarth 查看器
#include <osgEarth/EarthManipulator>
#include <osgEarth/ExampleResources>
#include <osgEarth/Controls>

// SDL2 (用于WebAssembly窗口系统)
#include <SDL2/SDL.h>
#include <emscripten.h>
#include <emscripten/html5.h>
#include <emscripten/fetch.h>

// STB Image库 - header-only图像解码库
#define STB_IMAGE_IMPLEMENTATION
#define STBI_NO_STDIO
#define STBI_NO_FAILURE_STRINGS
#define STBI_ONLY_PNG
#define STBI_ONLY_JPEG
#ifdef __EMSCRIPTEN__
// #include <stb_image.h>  // 注释掉，因为文件不存在
#else
#include "stb_image.h"
#endif

using namespace osg;
using namespace osgEarth;

// ===== 瓦片管理系统 =====

// 瓦片键类 - 标识XYZ瓦片
class XYZTileKey
{
public:
    struct GeoExtent
    {
        double xMin, yMin, xMax, yMax;

        GeoExtent(double xMin = 0, double yMin = 0, double xMax = 0, double yMax = 0)
            : xMin(xMin), yMin(yMin), xMax(xMax), yMax(yMax) {}
    };

    int level;
    int x;
    int y;

    XYZTileKey(int level = 0, int x = 0, int y = 0) : level(level), x(x), y(y) {}

    std::string toString() const
    {
        std::stringstream ss;
        ss << "XYZTileKey(" << level << "," << x << "," << y << ")";
        return ss.str();
    }

    GeoExtent getGeoExtent() const
    {
        double tileSize = 360.0 / (1 << level);
        double xMin = -180.0 + x * tileSize;
        double xMax = xMin + tileSize;
        double yMin = -90.0 + y * tileSize;
        double yMax = yMin + tileSize;
        return GeoExtent(xMin, yMin, xMax, yMax);
    }
};

// 瓦片纹理管理器
class TileTextureManager
{
private:
    std::map<std::string, osg::ref_ptr<osg::Texture2D>> _textureCache;
    std::map<std::string, bool> _loadingStates;

public:
    static TileTextureManager *instance()
    {
        static TileTextureManager instance;
        return &instance;
    }

    void requestTile(const XYZTileKey &tileKey, osg::Node *node)
    {
        std::string key = tileKey.toString();

        // 检查是否已经在加载
        if (_loadingStates.find(key) != _loadingStates.end())
        {
            return;
        }

        // 检查缓存
        if (_textureCache.find(key) != _textureCache.end())
        {
            return;
        }

        // 标记为正在加载
        _loadingStates[key] = true;

        // 构造谷歌地图瓦片URL
        std::string url = "https://mt1.google.com/vt/lyrs=s&x=" + std::to_string(tileKey.x) +
                          "&y=" + std::to_string(tileKey.y) + "&z=" + std::to_string(tileKey.level);

        // 异步加载瓦片
        emscripten_fetch_attr_t attr;
        emscripten_fetch_attr_init(&attr);
        strcpy(attr.requestMethod, "GET");
        attr.attributes = EMSCRIPTEN_FETCH_LOAD_TO_MEMORY;
        attr.onsuccess = onTileLoadSuccess;
        attr.onerror = onTileLoadError;

        // 传递额外数据
        attr.userData = new std::string(key);

        emscripten_fetch(&attr, url.c_str());
    }

    osg::ref_ptr<osg::Texture2D> getTexture(const XYZTileKey &tileKey)
    {
        std::string key = tileKey.toString();
        auto it = _textureCache.find(key);
        if (it != _textureCache.end())
        {
            return it->second;
        }
        return nullptr;
    }

    void setTexture(const XYZTileKey &tileKey, osg::ref_ptr<osg::Texture2D> texture)
    {
        std::string key = tileKey.toString();
        _textureCache[key] = texture;
        _loadingStates.erase(key);
    }

    static void onTileLoadSuccess(emscripten_fetch_t *fetch)
    {
        std::string *keyPtr = static_cast<std::string *>(fetch->userData);
        std::string key = *keyPtr;
        delete keyPtr;

        std::cout << "[TILE] Successfully loaded tile: " << key << std::endl;

        // 使用STB Image解码瓦片数据
        int width, height, channels;
        // 注释掉 stbi_load_from_memory 调用，因为 stb_image.h 不可用
        // unsigned char *imageData = stbi_load_from_memory(
        //     reinterpret_cast<const unsigned char *>(fetch->data),
        //     fetch->numBytes,
        //     &width, &height, &channels, 3 // 强制RGB
        // );
        unsigned char *imageData = nullptr; // 临时解决方案
        width = height = channels = 0;

        if (imageData)
        {
            // 创建OSG纹理
            osg::ref_ptr<osg::Image> image = new osg::Image();
            image->setImage(width, height, 1, GL_RGB, GL_RGB, GL_UNSIGNED_BYTE,
                            imageData, osg::Image::USE_NEW_DELETE);

            osg::ref_ptr<osg::Texture2D> texture = new osg::Texture2D();
            texture->setImage(image.get());
            texture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
            texture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
            texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR);
            texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);

            // 存储到缓存
            // 这里需要解析key来获取XYZTileKey
            // 简化实现，直接使用缓存key
            TileTextureManager::instance()->_textureCache[key] = texture;

            std::cout << "[TILE] Texture created and cached: " << key << std::endl;
        }
        else
        {
            std::cout << "[ERROR] Failed to decode tile image: " << key << std::endl;
        }

        emscripten_fetch_close(fetch);
    }

    static void onTileLoadError(emscripten_fetch_t *fetch)
    {
        std::string *keyPtr = static_cast<std::string *>(fetch->userData);
        std::string key = *keyPtr;
        delete keyPtr;

        std::cout << "[ERROR] Failed to load tile: " << key << std::endl;

        TileTextureManager::instance()->_loadingStates.erase(key);
        emscripten_fetch_close(fetch);
    }
};

// ===== 完整的Emscripten WebGL兼容性实现 =====

// Emscripten GraphicsContext实现
class EmscriptenGraphicsContext : public osg::GraphicsContext
{
public:
    EmscriptenGraphicsContext(Traits *traits) : osg::GraphicsContext()
    {
        _traits = traits;
        _valid = true;
        _realized = false;

        // 重要：初始化OSG内部状态，避免内存访问错误
        setState(new osg::State);
        getState()->setGraphicsContext(this);

        // 设置基本的GraphicsContext状态
        if (_traits.valid())
        {
            _traits->sharedContext = 0; // 确保没有共享上下文
        }

        std::cout << "[GC] EmscriptenGraphicsContext created with proper state initialization" << std::endl;
    }

    virtual ~EmscriptenGraphicsContext()
    {
        std::cout << "[GC] EmscriptenGraphicsContext destroyed" << std::endl;
    }

    virtual bool valid() const override { return _valid; }

    virtual bool realizeImplementation() override
    {
        std::cout << "[GC] realizeImplementation called" << std::endl;

        // 确保WebGL上下文可用
        if (!makeCurrentImplementation())
        {
            std::cout << "[ERROR] Failed to make context current during realization" << std::endl;
            return false;
        }

        // 初始化OSG状态对象
        if (getState())
        {
            getState()->setContextID(0);
            getState()->setGraphicsContext(this);

            // 重要：初始化OpenGL状态，避免内存访问错误
            getState()->initializeExtensionProcs();
        }

        _realized = true;
        std::cout << "[GC] GraphicsContext realized successfully with WebGL" << std::endl;

        return true;
    }

    virtual bool isRealizedImplementation() const override
    {
        return _realized;
    }

    virtual void closeImplementation() override
    {
        std::cout << "[GC] closeImplementation called" << std::endl;
        _realized = false;
    }

    virtual bool makeCurrentImplementation() override
    {
        if (_is_current)
            return true; // 如果已经是当前上下文，则直接返回

        // WebAssembly环境下SDL2已经管理了WebGL上下文
        // 我们只需要设置OSG的内部状态

        // 重要：设置OSG状态为当前
        if (getState())
        {
            getState()->setContextID(0); // WebAssembly只有一个上下文
        }

        _is_current = true;
        return true;
    }

    virtual bool makeContextCurrentImplementation(GraphicsContext *readContext) override
    {
        return makeCurrentImplementation();
    }

    virtual bool releaseContextImplementation() override
    {
        // WebAssembly环境：强制阻止释放上下文
        // std::cout << "[GC] releaseContextImplementation called (but ignored)" << std::endl;
        _is_current = false;
        return true;
    }

    virtual void swapBuffersImplementation() override
    {
        // WebGL自动处理缓冲区交换
    }

    virtual void bindPBufferToTextureImplementation(GLenum buffer) override
    {
        // WebGL不支持PBuffer
    }

private:
    bool _valid = false;
    bool _realized = false;
    bool _is_current = false; // 新增：跟踪上下文是否为当前
};

// Emscripten 窗口系统接口实现
class EmscriptenWindowingSystemInterface : public osg::GraphicsContext::WindowingSystemInterface
{
public:
    EmscriptenWindowingSystemInterface()
    {
        std::cout << "[WSI] EmscriptenWindowingSystemInterface created" << std::endl;
    }

    virtual ~EmscriptenWindowingSystemInterface()
    {
        std::cout << "[WSI] EmscriptenWindowingSystemInterface destroyed" << std::endl;
    }

    virtual unsigned int getNumScreens(const osg::GraphicsContext::ScreenIdentifier &screenIdentifier = osg::GraphicsContext::ScreenIdentifier()) override
    {
        return 1;
    }

    virtual void getScreenSettings(const osg::GraphicsContext::ScreenIdentifier &screenIdentifier, osg::GraphicsContext::ScreenSettings &resolution) override
    {
        resolution.width = 800;
        resolution.height = 600;
        resolution.colorDepth = 32;
        resolution.refreshRate = 60.0;
    }

    virtual void enumerateScreenSettings(const osg::GraphicsContext::ScreenIdentifier &screenIdentifier, osg::GraphicsContext::ScreenSettingsList &resolutionList) override
    {
        osg::GraphicsContext::ScreenSettings resolution;
        getScreenSettings(screenIdentifier, resolution);
        resolutionList.push_back(resolution);
    }

    virtual osg::GraphicsContext *createGraphicsContext(osg::GraphicsContext::Traits *traits) override
    {
        return new EmscriptenGraphicsContext(traits);
    }

private:
    EmscriptenWindowingSystemInterface(const EmscriptenWindowingSystemInterface &);
    EmscriptenWindowingSystemInterface &operator=(const EmscriptenWindowingSystemInterface &);
};

// 初始化WebGL环境
bool initializeWebGL()
{
    std::cout << "[WebGL] Initializing WebGL context..." << std::endl;

    // 设置WebGL上下文属性
    EmscriptenWebGLContextAttributes attrs;
    emscripten_webgl_init_context_attributes(&attrs);

    attrs.alpha = true;
    attrs.depth = true;
    attrs.stencil = true;
    attrs.antialias = true;
    attrs.premultipliedAlpha = false;
    attrs.preserveDrawingBuffer = false;
    attrs.majorVersion = 2;
    attrs.minorVersion = 0;

    // 创建WebGL上下文
    EMSCRIPTEN_WEBGL_CONTEXT_HANDLE context = emscripten_webgl_create_context("canvas", &attrs);

    if (context < 0)
    {
        std::cout << "[ERROR] Failed to create WebGL context: " << context << std::endl;
        return false;
    }

    // 激活WebGL上下文
    EMSCRIPTEN_RESULT result = emscripten_webgl_make_context_current(context);
    if (result != EMSCRIPTEN_RESULT_SUCCESS)
    {
        std::cout << "[ERROR] Failed to make WebGL context current: " << result << std::endl;
        return false;
    }

    std::cout << "[WebGL] WebGL context created and activated successfully" << std::endl;
    return true;
}

// 初始化窗口系统
void initializeWindowingSystem()
{
    std::cout << "[WSI] Initializing windowing system..." << std::endl;

    // 检查是否已经有窗口系统
    // 注释掉 setWindowingSystemInterface 调用，因为在新版本 OSG 中可能不存在
    /*
    if (osg::GraphicsContext::getWindowingSystemInterface() == nullptr)
    {
        // 创建并设置Emscripten窗口系统接口
        osg::ref_ptr<EmscriptenWindowingSystemInterface> wsi = new EmscriptenWindowingSystemInterface();
        osg::GraphicsContext::setWindowingSystemInterface(wsi.get());
        std::cout << "[WSI] EmscriptenWindowingSystemInterface installed" << std::endl;
    }
    else
    {
        std::cout << "[WSI] WindowingSystemInterface already exists" << std::endl;
    }
    */
    std::cout << "[WSI] 跳过 WindowingSystemInterface 设置（兼容性问题）" << std::endl;
}

// ===== 前向声明 =====
class EarthInteractionHandler;

// ===== 全局变量声明 =====
osg::ref_ptr<osgViewer::Viewer> g_viewer;
osg::ref_ptr<MapNode> g_mapNode;
SDL_Window *g_window = nullptr;
SDL_GLContext g_glContext = nullptr;
EarthInteractionHandler *g_interactionHandler_ptr = nullptr;

// ===== 地球交互处理器 =====

class EarthInteractionHandler
{
public:
    EarthInteractionHandler() : _rotationX(0.0f), _rotationY(0.0f), _distance(3.5f), _lastX(0.0f), _lastY(0.0f), _isDragging(false) {}

    void handleMouseButtonDown(int x, int y, int button)
    {
        if (button == SDL_BUTTON_LEFT)
        {
            _isDragging = true;
            _lastX = static_cast<float>(x);
            _lastY = static_cast<float>(y);
        }
    }

    void handleMouseButtonUp(int x, int y, int button)
    {
        if (button == SDL_BUTTON_LEFT)
        {
            _isDragging = false;
        }
    }

    void handleMouseMotion(int x, int y)
    {
        if (_isDragging)
        {
            float deltaX = static_cast<float>(x) - _lastX;
            float deltaY = static_cast<float>(y) - _lastY;

            // 应用旋转
            _rotationY -= deltaX * 0.5f;
            _rotationX -= deltaY * 0.5f;

            // 限制垂直旋转
            _rotationX = std::max(-89.0f, std::min(89.0f, _rotationX));

            // 更新记录的位置
            _lastX = static_cast<float>(x);
            _lastY = static_cast<float>(y);

            updateCamera();
        }
    }

    void handleMouseWheel(int direction)
    {
        _distance += direction * 0.5f;
        _distance = std::max(1.5f, std::min(10.0f, _distance));
        updateCamera();
    }

    void updateCamera()
    {
        if (g_viewer.valid() && g_viewer->getCamera())
        {
            // 计算相机位置
            float radX = osg::DegreesToRadians(_rotationX);
            float radY = osg::DegreesToRadians(_rotationY);

            float x = _distance * cos(radX) * sin(radY);
            float y = _distance * sin(radX);
            float z = _distance * cos(radX) * cos(radY);

            osg::Vec3 eye(x, y, z);
            osg::Vec3 center(0.0f, 0.0f, 0.0f);
            osg::Vec3 up(0.0f, 1.0f, 0.0f);

            g_viewer->getCamera()->setViewMatrixAsLookAt(eye, center, up);
        }
    }

    void resetView()
    {
        _rotationX = 0.0f;
        _rotationY = 0.0f;
        _distance = 3.5f;
        updateCamera();
    }

private:
    float _rotationX;
    float _rotationY;
    float _distance;
    float _lastX;
    float _lastY;
    bool _isDragging;
};

// 全局变量初始化
EarthInteractionHandler g_interactionHandler;

// 创建程序化地球纹理
osg::ref_ptr<osg::Texture2D> createProceduralEarthTexture()
{
    const int width = 512;
    const int height = 256;

    osg::ref_ptr<osg::Image> image = new osg::Image();
    image->allocateImage(width, height, 1, GL_RGB, GL_UNSIGNED_BYTE);

    unsigned char *data = image->data();

    for (int y = 0; y < height; ++y)
    {
        for (int x = 0; x < width; ++x)
        {
            // 经纬度映射
            float lon = (float(x) / width) * 360.0f - 180.0f;
            float lat = (float(y) / height) * 180.0f - 90.0f;

            // 基于纬度的气候分带
            osg::Vec3 color;

            if (abs(lat) > 70.0f)
            {
                // 极地 - 白色
                color = osg::Vec3(0.9f, 0.9f, 1.0f);
            }
            else if (abs(lat) > 50.0f)
            {
                // 寒带 - 浅蓝
                color = osg::Vec3(0.6f, 0.8f, 0.9f);
            }
            else if (abs(lat) > 30.0f)
            {
                // 温带 - 绿色
                color = osg::Vec3(0.3f, 0.7f, 0.2f);
            }
            else if (abs(lat) > 10.0f)
            {
                // 亚热带 - 黄绿
                color = osg::Vec3(0.6f, 0.8f, 0.3f);
            }
            else
            {
                // 热带 - 深绿
                color = osg::Vec3(0.2f, 0.6f, 0.1f);
            }

            // 添加一些海洋区域
            if (sin(lon * 0.1f) * cos(lat * 0.1f) > 0.3f)
            {
                color = osg::Vec3(0.1f, 0.3f, 0.8f); // 海洋蓝
            }

            int index = (y * width + x) * 3;
            data[index] = static_cast<unsigned char>(color.x() * 255);
            data[index + 1] = static_cast<unsigned char>(color.y() * 255);
            data[index + 2] = static_cast<unsigned char>(color.z() * 255);
        }
    }

    osg::ref_ptr<osg::Texture2D> texture = new osg::Texture2D();
    texture->setImage(image.get());
    texture->setWrap(osg::Texture::WRAP_S, osg::Texture::REPEAT);
    texture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
    texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);

    return texture;
}

// 创建地球几何体
osg::ref_ptr<osg::Geode> createEarthGeometry()
{
    std::cout << "[EARTH] Creating enhanced earth geometry..." << std::endl;

    osg::ref_ptr<osg::Geode> geode = new osg::Geode();
    osg::ref_ptr<osg::Geometry> geometry = new osg::Geometry();

    // 创建高质量球体 (64x32段)
    const int rings = 64;
    const int sectors = 32;
    const float radius = 1.0f;

    osg::ref_ptr<osg::Vec3Array> vertices = new osg::Vec3Array();
    osg::ref_ptr<osg::Vec3Array> normals = new osg::Vec3Array();
    osg::ref_ptr<osg::Vec2Array> texCoords = new osg::Vec2Array();

    // 生成顶点
    for (int ring = 0; ring <= rings; ++ring)
    {
        float phi = M_PI * float(ring) / float(rings);
        float y = radius * cos(phi);
        float ringRadius = radius * sin(phi);

        for (int sector = 0; sector <= sectors; ++sector)
        {
            float theta = 2.0f * M_PI * float(sector) / float(sectors);
            float x = ringRadius * cos(theta);
            float z = ringRadius * sin(theta);

            vertices->push_back(osg::Vec3(x, y, z));
            normals->push_back(osg::Vec3(x, y, z));

            float u = float(sector) / float(sectors);
            float v = 1.0f - float(ring) / float(rings);
            texCoords->push_back(osg::Vec2(u, v));
        }
    }

    // 生成三角形索引
    osg::ref_ptr<osg::DrawElementsUShort> indices = new osg::DrawElementsUShort(osg::PrimitiveSet::TRIANGLES);

    for (int ring = 0; ring < rings; ++ring)
    {
        for (int sector = 0; sector < sectors; ++sector)
        {
            int current = ring * (sectors + 1) + sector;
            int next = current + sectors + 1;

            indices->push_back(current);
            indices->push_back(next);
            indices->push_back(current + 1);

            indices->push_back(current + 1);
            indices->push_back(next);
            indices->push_back(next + 1);
        }
    }

    geometry->setVertexArray(vertices.get());
    geometry->setNormalArray(normals.get());
    geometry->setNormalBinding(osg::Geometry::BIND_PER_VERTEX);
    geometry->setTexCoordArray(0, texCoords.get());
    geometry->addPrimitiveSet(indices.get());

    geode->addDrawable(geometry.get());

    // 创建着色器程序
    osg::ref_ptr<osg::Program> program = new osg::Program();

    // 顶点着色器
    osg::ref_ptr<osg::Shader> vertexShader = new osg::Shader(osg::Shader::VERTEX);
    vertexShader->setShaderSource(R"(
        attribute vec3 osg_Vertex;
        attribute vec3 osg_Normal;
        attribute vec2 osg_MultiTexCoord0;
        
        uniform mat4 osg_ModelViewProjectionMatrix;
        uniform mat4 osg_ModelViewMatrix;
        uniform mat3 osg_NormalMatrix;
        
        varying vec3 vNormal;
        varying vec2 vTexCoord;
        varying vec3 vPosition;
        
        void main()
        {
            vNormal = normalize(osg_NormalMatrix * osg_Normal);
            vTexCoord = osg_MultiTexCoord0;
            vPosition = (osg_ModelViewMatrix * vec4(osg_Vertex, 1.0)).xyz;
            gl_Position = osg_ModelViewProjectionMatrix * vec4(osg_Vertex, 1.0);
        }
    )");

    // 片段着色器
    osg::ref_ptr<osg::Shader> fragmentShader = new osg::Shader(osg::Shader::FRAGMENT);
    fragmentShader->setShaderSource(R"(
        precision mediump float;
        
        uniform sampler2D texture0;
        uniform vec3 lightDirection;
        
        varying vec3 vNormal;
        varying vec2 vTexCoord;
        varying vec3 vPosition;
        
        void main()
        {
            vec3 normal = normalize(vNormal);
            vec3 lightDir = normalize(lightDirection);
            float diff = max(dot(normal, lightDir), 0.0);
            
            vec4 texColor = texture2D(texture0, vTexCoord);
            
            vec3 finalColor = texColor.rgb * (0.3 + 0.7 * diff);
            
            gl_FragColor = vec4(finalColor, 1.0);
        }
    )");

    program->addShader(vertexShader.get());
    program->addShader(fragmentShader.get());

    // 设置状态
    osg::ref_ptr<osg::StateSet> stateSet = geode->getOrCreateStateSet();
    stateSet->setAttributeAndModes(program.get(), osg::StateAttribute::ON);

    // 设置纹理
    osg::ref_ptr<osg::Texture2D> texture = createProceduralEarthTexture();
    stateSet->setTextureAttributeAndModes(0, texture.get(), osg::StateAttribute::ON);

    // 设置uniform变量
    stateSet->addUniform(new osg::Uniform("texture0", 0));
    stateSet->addUniform(new osg::Uniform("lightDirection", osg::Vec3(1.0f, 1.0f, 1.0f)));

    // 设置渲染状态
    stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);
    stateSet->setMode(GL_CULL_FACE, osg::StateAttribute::OFF);

    std::cout << "[EARTH] Enhanced earth geometry created successfully" << std::endl;
    return geode;
}

// 创建简化的OSGEarth场景
osg::ref_ptr<osg::Group> createSimplifiedOSGEarthScene()
{
    std::cout << "[SCENE] Creating simplified OSGEarth scene..." << std::endl;

    osg::ref_ptr<osg::Group> root = new osg::Group();

    // 创建地球几何体
    osg::ref_ptr<osg::Geode> earthGeode = createEarthGeometry();
    root->addChild(earthGeode.get());

    // 预加载一些基础瓦片
    TileTextureManager *tileManager = TileTextureManager::instance();
    for (int level = 0; level <= 1; ++level)
    {
        int numTiles = 1 << level;
        for (int x = 0; x < numTiles; ++x)
        {
            for (int y = 0; y < numTiles; ++y)
            {
                XYZTileKey tileKey(level, x, y);
                tileManager->requestTile(tileKey, root.get());
            }
        }
    }

    std::cout << "[SCENE] Simplified OSGEarth scene created successfully" << std::endl;
    return root;
}

// 处理SDL事件
void handleSDLEvent(const SDL_Event &event)
{
    switch (event.type)
    {
    case SDL_MOUSEBUTTONDOWN:
        g_interactionHandler.handleMouseButtonDown(event.button.x, event.button.y, event.button.button);
        break;
    case SDL_MOUSEBUTTONUP:
        g_interactionHandler.handleMouseButtonUp(event.button.x, event.button.y, event.button.button);
        break;
    case SDL_MOUSEMOTION:
        g_interactionHandler.handleMouseMotion(event.motion.x, event.motion.y);
        break;
    case SDL_MOUSEWHEEL:
        g_interactionHandler.handleMouseWheel(event.wheel.y);
        break;
    case SDL_KEYDOWN:
        if (event.key.keysym.sym == SDLK_r)
        {
            g_interactionHandler.resetView();
        }
        break;
    case SDL_WINDOWEVENT:
        if (event.window.event == SDL_WINDOWEVENT_SIZE_CHANGED)
        {
            int width = event.window.data1;
            int height = event.window.data2;
            if (g_viewer.valid() && g_viewer->getCamera())
            {
                g_viewer->getCamera()->setViewport(0, 0, width, height);
                g_viewer->getCamera()->setProjectionMatrixAsPerspective(45.0, double(width) / double(height), 0.1, 100.0);
            }
        }
        break;
    }
}

// 主循环
void mainLoop()
{
    SDL_Event event;
    while (SDL_PollEvent(&event))
    {
        handleSDLEvent(event);
    }

    if (g_viewer.valid())
    {
        g_viewer->frame();
    }
}

// 初始化SDL2
bool initializeSDL2()
{
    std::cout << "[SDL2] Initializing SDL2..." << std::endl;

    if (SDL_Init(SDL_INIT_VIDEO) < 0)
    {
        std::cout << "[ERROR] SDL2 initialization failed: " << SDL_GetError() << std::endl;
        return false;
    }

    // 设置OpenGL版本
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 3);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 0);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_PROFILE_MASK, SDL_GL_CONTEXT_PROFILE_ES);

    // 设置缓冲区
    SDL_GL_SetAttribute(SDL_GL_DOUBLEBUFFER, 1);
    SDL_GL_SetAttribute(SDL_GL_DEPTH_SIZE, 24);
    SDL_GL_SetAttribute(SDL_GL_STENCIL_SIZE, 8);

    // 创建窗口
    g_window = SDL_CreateWindow("OSGEarth WebAssembly Digital Earth",
                                SDL_WINDOWPOS_UNDEFINED, SDL_WINDOWPOS_UNDEFINED,
                                800, 600,
                                SDL_WINDOW_OPENGL | SDL_WINDOW_SHOWN | SDL_WINDOW_RESIZABLE);

    if (!g_window)
    {
        std::cout << "[ERROR] Failed to create SDL window: " << SDL_GetError() << std::endl;
        return false;
    }

    // 创建OpenGL上下文
    g_glContext = SDL_GL_CreateContext(g_window);
    if (!g_glContext)
    {
        std::cout << "[ERROR] Failed to create OpenGL context: " << SDL_GetError() << std::endl;
        return false;
    }

    // 设置垂直同步
    SDL_GL_SetSwapInterval(1);

    std::cout << "[SDL2] SDL2 initialized successfully" << std::endl;
    return true;
}

// 初始化OSG查看器
bool initializeOSGViewer()
{
    std::cout << "[OSG] Initializing OSG viewer..." << std::endl;

    // 初始化窗口系统
    initializeWindowingSystem();

    // 创建查看器
    g_viewer = new osgViewer::Viewer();

    // 设置为嵌入式窗口
    g_viewer->setUpViewerAsEmbeddedInWindow(0, 0, 800, 600);

    // 设置单线程模式
    g_viewer->setThreadingModel(osgViewer::Viewer::SingleThreaded);

    // 创建场景
    osg::ref_ptr<osg::Group> scene = createSimplifiedOSGEarthScene();
    g_viewer->setSceneData(scene.get());

    // 初始化相机
    g_interactionHandler.updateCamera();

    // 实现查看器
    g_viewer->realize();

    std::cout << "[OSG] OSG viewer initialized successfully" << std::endl;
    return true;
}

// 主函数
int main()
{
    std::cout << "[MAIN] Starting OSGEarth WebAssembly Digital Earth Application..." << std::endl;

    // 初始化SDL2
    if (!initializeSDL2())
    {
        std::cout << "[ERROR] Failed to initialize SDL2" << std::endl;
        return -1;
    }

    // 初始化WebGL
    if (!initializeWebGL())
    {
        std::cout << "[ERROR] Failed to initialize WebGL" << std::endl;
        return -1;
    }

    // 初始化OSG查看器
    if (!initializeOSGViewer())
    {
        std::cout << "[ERROR] Failed to initialize OSG viewer" << std::endl;
        return -1;
    }

    std::cout << "[MAIN] All systems initialized successfully" << std::endl;
    std::cout << "[MAIN] Starting main rendering loop..." << std::endl;

    // 设置主循环
    emscripten_set_main_loop(mainLoop, 0, 1);

    return 0;
}
