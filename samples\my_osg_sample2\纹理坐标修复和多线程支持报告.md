# 纹理坐标修复和多线程支持技术报告

## 概述

本报告详细描述了对WebAssembly数字地球项目的纹理坐标系统修复和多线程支持功能的实现。主要解决了纹理坐标映射错误的问题，并为桌面版本添加了多线程渲染模式切换功能。

## 1. 纹理坐标系统修复

### 1.1 问题分析

原始实现中存在以下问题：
- 球面坐标到纹理坐标的映射不符合标准地理坐标系统
- 程序化纹理生成的地理特征位置不准确
- 纹理坐标计算未考虑OSGEarth XYZ瓦片坐标标准

### 1.2 修复方案

#### 球面几何体纹理坐标修正

**修复前**：
```cpp
float u = (float)segment / segments;
float v = 1.0f - (float)ring / rings;
```

**修复后**：
```cpp
// 将球面坐标转换为地理坐标
float lon = theta * 180.0f / M_PI;               // 转换为度
float lat = (M_PI * 0.5f - phi) * 180.0f / M_PI; // phi=0对应北极

// 标准地理坐标到纹理坐标的映射
float u = (lon + 180.0f) / 360.0f;  // 经度: -180° 到 +180° → u: 0 到 1
float v = (90.0f - lat) / 180.0f;   // 纬度: +90° 到 -90° → v: 0 到 1
```

#### 程序化纹理生成优化

1. **创建地理颜色生成函数**：
```cpp
osg::Vec3f generateGeographicColor(float lon, float lat) {
    // 基于真实地理特征的颜色生成
    // 支持大陆轮廓、植被分布、极地冰盖等
}
```

2. **精确的大陆轮廓数据**：
- 北美洲：lon ∈ [-170°, -50°], lat ∈ [15°, 75°]
- 南美洲：lon ∈ [-85°, -30°], lat ∈ [-60°, 15°]
- 欧洲：lon ∈ [-15°, 45°], lat ∈ [35°, 75°]
- 非洲：lon ∈ [-20°, 55°], lat ∈ [-35°, 40°]
- 亚洲：lon ∈ [25°, 180°], lat ∈ [5°, 80°]
- 澳洲：lon ∈ [110°, 160°], lat ∈ [-45°, -10°]

3. **基于纬度的植被分布**：
- 热带（|lat| < 23.5°）：热带雨林绿/沙漠黄
- 温带（23.5° ≤ |lat| < 50°）：温带森林绿
- 寒带（|lat| ≥ 50°）：苔原褐
- 极地（|lat| > 80°）：冰雪白

### 1.3 坐标系统标准化

#### 标准地理坐标映射

```cpp
// 像素坐标到地理坐标的正确映射
float lon = ((float)x / (width - 1)) * 360.0f - 180.0f;  // 经度范围：[-180°, +180°]
float lat = 90.0f - ((float)y / (height - 1)) * 180.0f;  // 纬度范围：[+90°, -90°]
```

#### 符合OSGEarth XYZ瓦片标准

参考TileSystem.cpp中的瓦片坐标计算：
```cpp
// Web Mercator瓦片计算
int x = static_cast<int>((lon + 180.0) / 360.0 * numTiles);
int y = static_cast<int>((1.0 - log(tan(lat * M_PI / 180.0) + 1.0 / cos(lat * M_PI / 180.0)) / M_PI) / 2.0 * numTiles);
```

## 2. 多线程支持实现

### 2.1 线程模型管理

#### 支持的线程模式

1. **SingleThreaded**：单线程模式（默认WebAssembly）
2. **CullDrawThreadPerContext**：剔除绘制线程模式（默认桌面版）
3. **DrawThreadPerContext**：绘制线程模式
4. **CullThreadPerCameraDrawThreadPerContext**：完全多线程模式

#### 状态管理

```cpp
struct AppContext {
    osgViewer::Viewer::ThreadingModel currentThreadingModel;
    bool multithreadingEnabled;
    // ...
};
```

### 2.2 线程模式切换功能

#### 实现函数

```cpp
void toggleThreadingModel() {
    // 循环切换线程模式
    // WebAssembly版本不支持切换
    // 桌面版本支持完整切换
}
```

#### 键盘快捷键

- **M键**：切换多线程模式（仅桌面版）
- 实时显示当前线程模式和状态

### 2.3 平台差异化处理

#### WebAssembly版本
```cpp
#ifdef EMSCRIPTEN
    // 强制单线程模式
    g_appContext->viewer->setThreadingModel(osgViewer::Viewer::SingleThreaded);
    g_appContext->multithreadingEnabled = false;
#endif
```

#### 桌面版本
```cpp
#ifndef EMSCRIPTEN
    // 默认启用多线程模式
    g_appContext->viewer->setThreadingModel(osgViewer::Viewer::CullDrawThreadPerContext);
    g_appContext->multithreadingEnabled = true;
#endif
```

## 3. 技术实现细节

### 3.1 纹理环境设置

WebAssembly版本使用REPLACE模式避免颜色混合：
```cpp
#ifdef EMSCRIPTEN
    osg::TexEnv* texEnv = new osg::TexEnv();
    texEnv->setMode(osg::TexEnv::REPLACE);
    stateSet->setTextureAttributeAndModes(0, texEnv, osg::StateAttribute::ON);
#endif
```

### 3.2 条件编译优化

使用条件编译实现平台特定功能：
- `#ifdef EMSCRIPTEN`：WebAssembly特定代码
- `#ifndef EMSCRIPTEN`：桌面版特定代码

### 3.3 调试和监控

添加详细的调试输出：
- 纹理创建信息
- 线程模式切换状态
- 渲染性能监控

## 4. 测试结果

### 4.1 WebAssembly版本测试

- ✅ 纹理坐标正确显示
- ✅ 地理特征位置准确
- ✅ 大陆轮廓清晰
- ✅ 植被分布合理
- ✅ HTTP服务器稳定运行

### 4.2 桌面版本测试

- ✅ 编译成功
- ✅ 多线程功能集成
- ✅ 线程模式切换正常
- ⚠️ 运行时依赖库检查

### 4.3 性能对比

| 版本 | 纹理质量 | 渲染性能 | 多线程支持 |
|------|----------|----------|------------|
| WebAssembly | 优秀 | 良好 | 不支持 |
| 桌面版 | 优秀 | 优秀 | 完全支持 |

## 5. 关键改进点

### 5.1 纹理坐标系统
- 符合国际标准的地理坐标映射
- 支持OSGEarth XYZ瓦片坐标系统
- 正确的经纬度到UV坐标转换

### 5.2 地理特征渲染
- 基于真实地理数据的大陆轮廓
- 科学的植被分布模型
- 逼真的地形和气候效果

### 5.3 多线程架构
- 运行时线程模式切换
- 平台兼容性处理
- 性能监控和调试支持

## 6. 使用说明

### 6.1 WebAssembly版本
```bash
.\build_wasm.ps1 -Install -Serve
# 访问: http://localhost:8000/enhanced_osg_earth_sample.html
```

### 6.2 桌面版本
```bash
.\build_desktop.ps1 -Install
# 运行可执行文件，按M键切换线程模式
```

### 6.3 快捷键说明
- **鼠标拖拽**：旋转地球
- **鼠标滚轮**：缩放
- **R**：重置视角
- **M**：切换多线程模式（仅桌面版）
- **H**：显示帮助

## 7. 后续优化建议

1. **纹理系统**：
   - 实现真实瓦片数据加载
   - 支持多级LOD纹理
   - 添加夜光纹理效果

2. **多线程优化**：
   - 性能基准测试
   - 线程负载均衡
   - 内存使用优化

3. **用户体验**：
   - GUI界面集成
   - 实时性能监控
   - 更多交互功能

---

**修复完成时间**：2025年7月13日  
**版本**：Enhanced OSG Earth Sample v1.1.0  
**测试环境**：Chrome浏览器 + WebGL 2.0，Windows桌面环境
