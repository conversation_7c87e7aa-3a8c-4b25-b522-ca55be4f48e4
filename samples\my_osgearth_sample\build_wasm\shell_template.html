<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OSGEarth WebAssembly Digital Earth</title>
    <style>
        body { margin: 0; padding: 0; background: #000; }
        canvas { display: block; margin: 0 auto; }
        #status { position: absolute; top: 10px; left: 10px; color: white; font-family: Arial; }
        #controls { position: absolute; top: 10px; right: 10px; color: white; font-family: Arial; }
    </style>
</head>
<body>
    <div id="status">Loading...</div>
    <div id="controls">
        <div>鼠标左键: 拖拽旋转</div>
        <div>鼠标滚轮: 缩放</div>
        <div>R键: 重置视角</div>
    </div>
    <canvas id="canvas" oncontextmenu="event.preventDefault()"></canvas>
    <script>
        var Module = {
            preRun: [],
            postRun: [],
            print: function(text) {
                console.log(text);
            },
            printErr: function(text) {
                console.error(text);
            },
            canvas: (function() {
                var canvas = document.getElementById('canvas');
                canvas.width = 800;
                canvas.height = 600;
                return canvas;
            })(),
            setStatus: function(text) {
                document.getElementById('status').innerHTML = text;
            },
            totalDependencies: 0,
            monitorRunDependencies: function(left) {
                this.totalDependencies = Math.max(this.totalDependencies, left);
                Module.setStatus(left ? 'Preparing... (' + (this.totalDependencies-left) + '/' + this.totalDependencies + ')' : 'All downloads complete.');
            }
        };
        Module.setStatus('Downloading...');
        window.onerror = function(event) {
            Module.setStatus('Exception thrown, see JavaScript console');
        };
    </script>
    {{{ SCRIPT }}}
</body>
</html>