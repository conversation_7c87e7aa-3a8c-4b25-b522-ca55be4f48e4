<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OSGEarth Digital Earth - Simple Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #000;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        #canvas {
            border: 2px solid #00ff00;
            display: block;
            margin: 20px auto;
            background: #111;
        }
        
        .info {
            text-align: center;
            margin: 20px;
        }
        
        .log {
            background: #222;
            padding: 10px;
            margin: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .error {
            color: #ff6666;
        }
        
        .success {
            color: #66ff66;
        }
        
        .info-text {
            color: #66ccff;
        }
    </style>
</head>
<body>
    <div class="info">
        <h1>🌍 OSGEarth Digital Earth - Simple Test</h1>
        <p>WebAssembly数字地球测试页面</p>
        <div id="status">正在初始化...</div>
    </div>

    <canvas id="canvas" width="800" height="600"></canvas>

    <div class="log" id="log">
        <div class="info-text">[初始化] 页面加载完成</div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const line = document.createElement('div');
            line.className = type;
            line.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logDiv.appendChild(line);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        // 检测WebGL支持
        function checkWebGL() {
            try {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
                if (gl) {
                    const version = gl.getParameter(gl.VERSION);
                    const renderer = gl.getParameter(gl.RENDERER);
                    log(`WebGL检测成功: ${version}`, 'success');
                    log(`渲染器: ${renderer}`, 'info-text');
                    return true;
                } else {
                    log('WebGL不支持', 'error');
                    return false;
                }
            } catch (e) {
                log(`WebGL检测失败: ${e.message}`, 'error');
                return false;
            }
        }

        // 检测WebAssembly支持
        function checkWebAssembly() {
            if (typeof WebAssembly === 'object' && typeof WebAssembly.instantiate === 'function') {
                log('WebAssembly支持检测成功', 'success');
                return true;
            } else {
                log('WebAssembly不支持', 'error');
                return false;
            }
        }

        // 初始化检查
        document.addEventListener('DOMContentLoaded', function() {
            log('开始系统检查...', 'info-text');
            
            const webglOk = checkWebGL();
            const wasmOk = checkWebAssembly();
            
            if (webglOk && wasmOk) {
                log('系统检查通过，准备加载WebAssembly模块...', 'success');
                document.getElementById('status').textContent = '系统检查通过，正在加载...';
            } else {
                log('系统检查失败，无法运行应用', 'error');
                document.getElementById('status').textContent = '系统检查失败';
                return;
            }
        });

        // WebAssembly模块配置
        var Module = {
            canvas: document.getElementById('canvas'),
            
            print: function(text) {
                log(`[WASM] ${text}`, 'info-text');
            },
            
            printErr: function(text) {
                log(`[WASM ERROR] ${text}`, 'error');
            },
            
            onRuntimeInitialized: function() {
                log('WebAssembly运行时初始化完成！', 'success');
                document.getElementById('status').textContent = '✅ 数字地球已就绪';
            },
            
            onAbort: function(what) {
                log(`WebAssembly运行时中止: ${what}`, 'error');
                document.getElementById('status').textContent = '❌ 加载失败';
            },
            
            locateFile: function(path, prefix) {
                // 确保.wasm文件路径正确
                if (path.endsWith('.wasm')) {
                    log(`正在加载WASM文件: ${path}`, 'info-text');
                }
                return prefix + path;
            },
            
            onProgress: function(progress) {
                if (progress) {
                    const percent = Math.round(progress * 100);
                    log(`加载进度: ${percent}%`, 'info-text');
                    document.getElementById('status').textContent = `加载中... ${percent}%`;
                }
            }
        };

        // 错误处理
        window.addEventListener('error', function(e) {
            log(`JavaScript错误: ${e.message}`, 'error');
        });

        window.addEventListener('unhandledrejection', function(e) {
            log(`Promise错误: ${e.reason}`, 'error');
        });
    </script>

    <!-- 加载WebAssembly模块 -->
    <script>
        log('开始加载WebAssembly JavaScript文件...', 'info-text');
    </script>
    <script src="osgearth_digital_earth_webgl_fixed.js"></script>
</body>
</html>
