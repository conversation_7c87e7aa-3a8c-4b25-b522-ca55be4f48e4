<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OSGEarth MyViewer - WebAssembly</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #000;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        
        #container {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        #header {
            background-color: #333;
            color: white;
            padding: 10px;
            text-align: center;
            font-size: 18px;
            z-index: 1000;
        }
        
        #canvas-container {
            flex: 1;
            position: relative;
            background-color: #000;
        }
        
        #canvas {
            width: 100%;
            height: 100%;
            display: block;
            background-color: #000;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            text-align: center;
            z-index: 1000;
        }
        
        #controls {
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
        
        .control-item {
            margin: 5px 0;
        }
        
        #status {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="header">
            OSGEarth MyViewer - WebAssembly版本
        </div>
        
        <div id="canvas-container">
            <canvas id="canvas"></canvas>
            
            <div id="loading">
                <div>正在加载OSGEarth WebAssembly模块...</div>
                <div id="progress">0%</div>
            </div>
            
            <div id="controls" class="hidden">
                <div class="control-item"><strong>控制说明:</strong></div>
                <div class="control-item">• 左键拖拽: 平移地图</div>
                <div class="control-item">• 中键拖拽: 旋转视角</div>
                <div class="control-item">• 右键拖拽: 缩放</div>
                <div class="control-item">• 滚轮: 缩放</div>
                <div class="control-item">• T键: 时间动画开关</div>
                <div class="control-item">• +/-键: 调整时间</div>
                <div class="control-item">• 1-4键: 快速时间设置</div>
            </div>
            
            <div id="status" class="hidden">
                <div id="fps">FPS: --</div>
                <div id="memory">内存: --</div>
                <div id="position">位置: --</div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let Module = {};
        let canvas = document.getElementById('canvas');
        let loadingDiv = document.getElementById('loading');
        let progressDiv = document.getElementById('progress');
        let controlsDiv = document.getElementById('controls');
        let statusDiv = document.getElementById('status');
        
        // 设置canvas大小
        function resizeCanvas() {
            const container = document.getElementById('canvas-container');
            canvas.width = container.clientWidth;
            canvas.height = container.clientHeight;
        }
        
        // 初始化canvas
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
        
        // WebAssembly模块配置
        Module = {
            canvas: canvas,
            
            // 预运行函数
            preRun: [],
            
            // 后运行函数
            postRun: [function() {
                console.log('OSGEarth WebAssembly模块加载完成');
                loadingDiv.classList.add('hidden');
                controlsDiv.classList.remove('hidden');
                statusDiv.classList.remove('hidden');
                
                // 启动状态监控
                startStatusMonitoring();
            }],
            
            // 打印函数
            print: function(text) {
                console.log('OSGEarth:', text);
            },
            
            // 错误打印函数
            printErr: function(text) {
                console.error('OSGEarth Error:', text);
            },
            
            // 加载进度回调
            setStatus: function(text) {
                if (text) {
                    console.log('Status:', text);
                    if (text.includes('%')) {
                        progressDiv.textContent = text;
                    }
                }
            },
            
            // 总下载进度
            totalDependencies: 0,
            monitorRunDependencies: function(left) {
                this.totalDependencies = Math.max(this.totalDependencies, left);
                if (left == 0) {
                    this.setStatus('模块加载完成');
                } else {
                    const progress = Math.round((this.totalDependencies - left) / this.totalDependencies * 100);
                    this.setStatus(`加载中... ${progress}%`);
                }
            }
        };
        
        // 状态监控
        function startStatusMonitoring() {
            setInterval(function() {
                // 更新FPS (如果可用)
                if (Module.getFPS) {
                    document.getElementById('fps').textContent = `FPS: ${Module.getFPS()}`;
                }
                
                // 更新内存使用
                if (Module.HEAPU8) {
                    const memoryMB = Math.round(Module.HEAPU8.length / 1024 / 1024);
                    document.getElementById('memory').textContent = `内存: ${memoryMB}MB`;
                }
                
                // 更新位置信息 (如果可用)
                if (Module.getCurrentPosition) {
                    const pos = Module.getCurrentPosition();
                    document.getElementById('position').textContent = `位置: ${pos}`;
                }
            }, 1000);
        }
        
        // 键盘事件处理
        document.addEventListener('keydown', function(event) {
            // 将键盘事件传递给WebAssembly模块
            if (Module.handleKeyDown) {
                Module.handleKeyDown(event.keyCode);
            }
        });
        
        // 鼠标事件处理
        canvas.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });
        
        // 错误处理
        window.addEventListener('error', function(e) {
            console.error('页面错误:', e.error);
        });
        
        // 加载WebAssembly模块
        console.log('开始加载OSGEarth WebAssembly模块...');
        
        // 检查浏览器支持
        if (!window.WebAssembly) {
            alert('您的浏览器不支持WebAssembly，请使用现代浏览器。');
        } else {
            // 加载模块脚本
            const script = document.createElement('script');
            script.src = 'osgearth_myviewer_wasm.js';
            script.onerror = function() {
                loadingDiv.innerHTML = '<div style="color: red;">加载失败: 找不到osgearth_myviewer_wasm.js文件</div>';
            };
            document.head.appendChild(script);
        }
    </script>
</body>
</html>
