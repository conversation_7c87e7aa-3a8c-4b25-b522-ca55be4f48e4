#!/usr/bin/env pwsh
# OSGEarth WebAssembly 构建测试脚本
# 用于验证环境配置和编译过程

param(
    [switch]$Clean = $false,
    [switch]$Verbose = $false
)

Write-Host "OSGEarth WebAssembly 构建测试开始..." -ForegroundColor Green

# 设置错误处理
$ErrorActionPreference = "Stop"

try {
    # 检查 Emscripten 环境
    Write-Host "检查 Emscripten 环境..." -ForegroundColor Yellow
    
    $env:EMSDK = "C:\dev\emsdk"
    if (-not (Test-Path "$env:EMSDK")) {
        Write-Host "❌ 未找到 Emscripten SDK: $env:EMSDK" -ForegroundColor Red
        exit 1
    }
    
    # 设置 PATH
    $env:PATH = "$env:EMSDK;$env:EMSDK\upstream\emscripten;$env:EMSDK\node\16.20.0_64bit\bin;" + $env:PATH
    
    # 激活 Emscripten
    Write-Host "激活 Emscripten..." -ForegroundColor Yellow
    & "$env:EMSDK\emsdk_env.ps1"
    
    # 检查工具是否可用
    Write-Host "检查编译工具..." -ForegroundColor Yellow
    try {
        $emccVersion = & emcc --version 2>&1 | Select-String "emcc"
        Write-Host "✅ Emscripten: $emccVersion" -ForegroundColor Green
    } catch {
        Write-Host "❌ emcc 不可用" -ForegroundColor Red
        exit 1
    }
    
    try {
        $cmakeVersion = & cmake --version 2>&1 | Select-String "cmake version"
        Write-Host "✅ CMake: $cmakeVersion" -ForegroundColor Green
    } catch {
        Write-Host "❌ cmake 不可用" -ForegroundColor Red
        exit 1
    }
    
    # 检查库路径
    Write-Host "检查库路径..." -ForegroundColor Yellow
    
    $osgWasmDir = "F:\cmo-dev\my_osgearth_web\osgearth_third_party\wasm_dep"
    if (-not (Test-Path "$osgWasmDir")) {
        Write-Host "❌ 未找到 OSG WASM 库: $osgWasmDir" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ OSG WASM 库: $osgWasmDir" -ForegroundColor Green
    
    $osgearthWasmDir = "F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\redist_wasm"
    if (-not (Test-Path "$osgearthWasmDir")) {
        Write-Host "❌ 未找到 OSGEarth WASM 库: $osgearthWasmDir" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ OSGEarth WASM 库: $osgearthWasmDir" -ForegroundColor Green
    
    # 检查头文件
    Write-Host "检查头文件..." -ForegroundColor Yellow
    $osgEarthSrc = "F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\src\osgEarth"
    if (-not (Test-Path "$osgEarthSrc")) {
        Write-Host "❌ 未找到 OSGEarth 头文件: $osgEarthSrc" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ OSGEarth 头文件: $osgEarthSrc" -ForegroundColor Green
    
    # 检查源文件
    Write-Host "检查源文件..." -ForegroundColor Yellow
    if (-not (Test-Path "main.cpp")) {
        Write-Host "❌ 未找到 main.cpp" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ main.cpp 存在" -ForegroundColor Green
    
    if (-not (Test-Path "stb_image.h")) {
        Write-Host "❌ 未找到 stb_image.h" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ stb_image.h 存在" -ForegroundColor Green
    
    if (-not (Test-Path "CMakeLists.txt")) {
        Write-Host "❌ 未找到 CMakeLists.txt" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ CMakeLists.txt 存在" -ForegroundColor Green
    
    # 创建构建目录
    Write-Host "准备构建目录..." -ForegroundColor Yellow
    
    if ($Clean -and (Test-Path "build_wasm")) {
        Write-Host "清理旧的构建目录..." -ForegroundColor Yellow
        Remove-Item "build_wasm" -Recurse -Force
    }
    
    if (-not (Test-Path "build_wasm")) {
        New-Item -ItemType Directory -Path "build_wasm" | Out-Null
    }
    
    if (-not (Test-Path "redist_wasm")) {
        New-Item -ItemType Directory -Path "redist_wasm" | Out-Null
    }
    
    Set-Location "build_wasm"
    
    # 配置 CMake
    Write-Host "配置 CMake..." -ForegroundColor Yellow
    
    $cmakeArgs = @(
        "-DUSE_EXTERNAL_WASM_DEPENDS=ON",
        "-DCMAKE_BUILD_TYPE=Release",
        "-DOSGEARTH_BUILD_SHARED_LIBS=OFF"
    )
    
    if ($Verbose) {
        $cmakeArgs += "--verbose"
    }
    
    & emcmake cmake @cmakeArgs ".."
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ CMake 配置失败" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "✅ CMake 配置成功" -ForegroundColor Green
    
    # 编译
    Write-Host "开始编译..." -ForegroundColor Yellow
    
    if ($Verbose) {
        & emmake make VERBOSE=1
    } else {
        & emmake make
    }
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ 编译失败" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "✅ 编译成功" -ForegroundColor Green
    
    # 检查输出文件
    Write-Host "检查输出文件..." -ForegroundColor Yellow
    
    $outputFiles = @("osgearth_simple_earth.html", "osgearth_simple_earth.js", "osgearth_simple_earth.wasm")
    
    foreach ($file in $outputFiles) {
        if (Test-Path $file) {
            $size = (Get-Item $file).Length
            $sizeStr = if ($size -gt 1MB) { 
                "$([math]::Round($size/1MB, 2)) MB" 
            } elseif ($size -gt 1KB) { 
                "$([math]::Round($size/1KB, 2)) KB" 
            } else { 
                "$size bytes" 
            }
            Write-Host "✅ $file ($sizeStr)" -ForegroundColor Green
        } else {
            Write-Host "❌ $file 未生成" -ForegroundColor Red
        }
    }
    
    # 复制到发布目录
    Write-Host "复制到发布目录..." -ForegroundColor Yellow
    
    foreach ($file in $outputFiles) {
        if (Test-Path $file) {
            Copy-Item $file -Destination "../redist_wasm/" -Force
        }
    }
    
    Write-Host "✅ 文件已复制到 redist_wasm 目录" -ForegroundColor Green
    
    # 创建测试服务器脚本
    $serverScript = @"
#!/usr/bin/env python3
import http.server
import socketserver
import webbrowser
import threading
import time

PORT = 8000

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Cross-Origin-Opener-Policy', 'same-origin')
        self.send_header('Cross-Origin-Embedder-Policy', 'require-corp')
        super().end_headers()

def start_server():
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        print(f"Server running at http://localhost:{PORT}/")
        httpd.serve_forever()

if __name__ == "__main__":
    # 启动服务器
    server_thread = threading.Thread(target=start_server)
    server_thread.daemon = True
    server_thread.start()
    
    # 等待服务器启动
    time.sleep(1)
    
    # 打开浏览器
    url = f"http://localhost:{PORT}/osgearth_simple_earth.html"
    print(f"Opening browser to: {url}")
    webbrowser.open(url)
    
    # 保持服务器运行
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nServer stopped.")
"@
    
    Set-Content -Path "../redist_wasm/test_server.py" -Value $serverScript -Encoding UTF8
    
    # 返回主目录
    Set-Location ".."
    
    Write-Host "构建测试完成!" -ForegroundColor Green
    Write-Host "启动测试服务器:" -ForegroundColor Cyan
    Write-Host "  cd redist_wasm" -ForegroundColor White
    Write-Host "  python test_server.py" -ForegroundColor White
    Write-Host "或者:" -ForegroundColor Cyan
    Write-Host "  python -m http.server 8000" -ForegroundColor White
    Write-Host "然后访问: http://localhost:8000/osgearth_simple_earth.html" -ForegroundColor White
    
} catch {
    Write-Host "❌ 构建测试失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
} 