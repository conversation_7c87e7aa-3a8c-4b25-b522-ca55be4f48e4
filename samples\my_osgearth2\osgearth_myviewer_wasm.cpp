/* osgEarth WebAssembly版本
 * 基于osgearth_myviewer.cpp修改
 * 适配WebAssembly环境，移除Windows特定代码
 * 集成EmscriptenWindowingSystemInterface解决段错误问题
 */

#include <osgViewer/Viewer>
#include <osgViewer/ViewerEventHandlers>
#include <osgEarth/EarthManipulator>
#include <osgEarth/ExampleResources>
#include <osgEarth/MapNode>
#include <osgEarth/Map>
#include <osgEarth/TerrainEngineNode>
#include <osgEarth/XYZ>
#include <osgEarth/TileKey>
#include <osgEarth/ImageLayer>
#include <osgEarth/GeodeticGraticule>
#include <osgEarth/Sky>
#include <osgEarth/HTTPClient>
#include <osgEarth/Cache>
#include <osgEarth/CachePolicy>
#include <osgGA/TrackballManipulator>
#include <osgDB/ReadFile>
#include <osgDB/FileUtils>
#include <iostream>
#include <thread>
#include <chrono>

#ifdef __EMSCRIPTEN__
#include <emscripten.h>
#include <emscripten/html5.h>
#include <osg/GraphicsContext>
#endif

#define LC "[myviewer_wasm] "

using namespace osgEarth;
using namespace osgEarth::Util;

// 全局变量
static osgViewer::Viewer *g_viewer = nullptr;
static MapNode *g_mapNode = nullptr;
static bool g_running = true;

#ifdef __EMSCRIPTEN__
// WebGL上下文管理
static EMSCRIPTEN_WEBGL_CONTEXT_HANDLE g_webgl_context = 0;
static bool g_webgl_initialized = false;

// 简单的Emscripten GraphicsContext
class EmscriptenGraphicsContext : public osg::GraphicsContext
{
public:
  EmscriptenGraphicsContext(Traits *traits) : osg::GraphicsContext()
  {
    _traits = traits;
    _valid = true;
    setState(new osg::State);
    getState()->setGraphicsContext(this);

    std::cout << LC << "[GC] EmscriptenGraphicsContext created" << std::endl;
  }

  virtual ~EmscriptenGraphicsContext()
  {
    std::cout << LC << "[GC] EmscriptenGraphicsContext destroyed" << std::endl;
  }

  virtual bool valid() const override { return _valid; }

  virtual bool realizeImplementation() override
  {
    std::cout << LC << "[GC] realizeImplementation called" << std::endl;

    // 确保WebGL上下文是当前的
    if (g_webgl_context > 0)
    {
      EMSCRIPTEN_RESULT result = emscripten_webgl_make_context_current(g_webgl_context);
      if (result == EMSCRIPTEN_RESULT_SUCCESS)
      {
        std::cout << LC << "[GC] WebGL context made current successfully" << std::endl;
        _realized = true;
        return true;
      }
      else
      {
        std::cout << LC << "[GC] Failed to make WebGL context current: " << result << std::endl;
      }
    }

    return false;
  }

  virtual bool isRealizedImplementation() const override
  {
    return _realized;
  }

  virtual void closeImplementation() override
  {
    std::cout << LC << "[GC] closeImplementation called" << std::endl;
    _realized = false;
  }

  virtual bool makeCurrentImplementation() override
  {
    if (g_webgl_context > 0)
    {
      EMSCRIPTEN_RESULT result = emscripten_webgl_make_context_current(g_webgl_context);
      return (result == EMSCRIPTEN_RESULT_SUCCESS);
    }
    return false;
  }

  virtual bool makeContextCurrentImplementation(GraphicsContext *readContext) override
  {
    // 对于WebGL，读取上下文和绘制上下文是同一个
    return makeCurrentImplementation();
  }

  virtual bool releaseContextImplementation() override
  {
    // WebGL上下文不需要释放
    return true;
  }

  virtual void swapBuffersImplementation() override
  {
    // WebGL自动处理缓冲区交换
  }

  virtual void bindPBufferToTextureImplementation(GLenum buffer) override
  {
    // WebGL不支持PBuffer
  }

private:
  bool _valid = false;
  bool _realized = false;
};

// 简单的Emscripten WindowingSystemInterface
class EmscriptenWindowingSystemInterface : public osg::GraphicsContext::WindowingSystemInterface
{
public:
  EmscriptenWindowingSystemInterface()
  {
    std::cout << LC << "[WSI] Creating Emscripten WindowingSystemInterface" << std::endl;
  }

  virtual ~EmscriptenWindowingSystemInterface()
  {
    std::cout << LC << "[WSI] Destroying Emscripten WindowingSystemInterface" << std::endl;
  }

  virtual unsigned int getNumScreens(const osg::GraphicsContext::ScreenIdentifier &screenIdentifier = osg::GraphicsContext::ScreenIdentifier()) override
  {
    return 1;
  }

  virtual void getScreenSettings(const osg::GraphicsContext::ScreenIdentifier &screenIdentifier, osg::GraphicsContext::ScreenSettings &resolution) override
  {
    resolution.width = 800;
    resolution.height = 600;
    resolution.refreshRate = 60.0f;
    resolution.colorDepth = 24;
    std::cout << LC << "[WSI] getScreenSettings: " << resolution.width << "x" << resolution.height << std::endl;
  }

  virtual void enumerateScreenSettings(const osg::GraphicsContext::ScreenIdentifier &screenIdentifier, osg::GraphicsContext::ScreenSettingsList &resolutionList) override
  {
    osg::GraphicsContext::ScreenSettings settings;
    settings.width = 800;
    settings.height = 600;
    settings.refreshRate = 60.0f;
    settings.colorDepth = 24;
    resolutionList.push_back(settings);
    std::cout << LC << "[WSI] enumerateScreenSettings: Added 800x600" << std::endl;
  }

  virtual osg::GraphicsContext *createGraphicsContext(osg::GraphicsContext::Traits *traits) override
  {
    std::cout << LC << "[WSI] createGraphicsContext called" << std::endl;

    if (!traits)
    {
      std::cout << LC << "[WSI] No traits provided" << std::endl;
      return nullptr;
    }

    std::cout << LC << "[WSI] Creating graphics context for " << traits->width << "x" << traits->height << std::endl;

    // 创建一个简单的GraphicsContext
    // 我们已经有了WebGL上下文，所以这里主要是告诉OSG我们有一个有效的上下文
    osg::ref_ptr<EmscriptenGraphicsContext> context = new EmscriptenGraphicsContext(traits);

    return context.release();
  }
};

// 初始化WebGL上下文 - 基于osg_texture_test.cpp的成功实现
bool initializeWebGL()
{
  if (g_webgl_initialized)
    return true;

  std::cout << LC << "[WebGL] Initializing WebGL context..." << std::endl;

  // 创建WebGL上下文属性 - 使用与osg_texture_test相同的配置
  EmscriptenWebGLContextAttributes attrs;
  emscripten_webgl_init_context_attributes(&attrs);

  attrs.alpha = 1;
  attrs.depth = 1;
  attrs.stencil = 0;
  attrs.antialias = 1;
  attrs.premultipliedAlpha = 0;
  attrs.preserveDrawingBuffer = 0;
  attrs.powerPreference = EM_WEBGL_POWER_PREFERENCE_DEFAULT;
  attrs.failIfMajorPerformanceCaveat = 0;
  attrs.majorVersion = 2;
  attrs.minorVersion = 0;
  attrs.enableExtensionsByDefault = 1;

  // 尝试创建WebGL 2.0上下文
  g_webgl_context = emscripten_webgl_create_context("#canvas", &attrs);

  if (g_webgl_context <= 0)
  {
    std::cout << LC << "[WebGL] WebGL2 failed, trying WebGL1..." << std::endl;
    attrs.majorVersion = 1;
    attrs.minorVersion = 0;
    g_webgl_context = emscripten_webgl_create_context("#canvas", &attrs);
  }

  if (g_webgl_context > 0)
  {
    EMSCRIPTEN_RESULT result = emscripten_webgl_make_context_current(g_webgl_context);
    if (result == EMSCRIPTEN_RESULT_SUCCESS)
    {
      g_webgl_initialized = true;
      std::cout << LC << "[WebGL] WebGL context initialized successfully" << std::endl;

      // 基本WebGL设置 - 与osg_texture_test保持一致
      glEnable(GL_DEPTH_TEST);
      glDepthFunc(GL_LEQUAL);
      glClearColor(0.2f, 0.2f, 0.2f, 1.0f);

      // 设置视口
      int canvas_width, canvas_height;
      emscripten_get_canvas_element_size("#canvas", &canvas_width, &canvas_height);
      glViewport(0, 0, canvas_width, canvas_height);

      std::cout << LC << "[WebGL] Canvas size: " << canvas_width << "x" << canvas_height << std::endl;

      // 输出WebGL版本信息
      const char* version = (const char*)glGetString(GL_VERSION);
      const char* renderer = (const char*)glGetString(GL_RENDERER);
      std::cout << LC << "[WebGL] Version: " << (version ? version : "unknown") << std::endl;
      std::cout << LC << "[WebGL] Renderer: " << (renderer ? renderer : "unknown") << std::endl;

      return true;
    }
    else
    {
      std::cout << LC << "[WebGL] Failed to make WebGL context current: " << result << std::endl;
    }
  }

  std::cout << LC << "[WebGL] Failed to create WebGL context" << std::endl;
  return false;
}

// 初始化WindowingSystemInterface
void initializeWindowingSystem()
{
  std::cout << LC << "[WSI] Initializing Emscripten WindowingSystemInterface..." << std::endl;

  // 创建并注册我们的WindowingSystemInterface
  osg::ref_ptr<EmscriptenWindowingSystemInterface> wsi = new EmscriptenWindowingSystemInterface();

  // 添加到WindowingSystemInterfaces
  osg::GraphicsContext::getWindowingSystemInterfaces()->addWindowingSystemInterface(wsi.get());

  std::cout << LC << "[WSI] WindowingSystemInterface registered successfully" << std::endl;
}
#endif

// WebAssembly配置网络设置
void configureNetworking()
{
    std::cout << LC << "Configuring networking for WebAssembly..." << std::endl;

    // WebAssembly环境下的网络配置
    // 不需要代理设置，浏览器会处理网络请求

    // 设置HTTP超时
    HTTPClient::setTimeout(30);        // 30秒超时
    HTTPClient::setConnectTimeout(10); // 10秒连接超时

    // 设置用户代理
    HTTPClient::setUserAgent("Mozilla/5.0 (WebAssembly) OSGEarth/2.10.1");

    std::cout << LC << "Network configuration completed for WebAssembly" << std::endl;
}

// 创建简化地图（避免复杂功能导致段错误）
osg::ref_ptr<Map> createSimpleMap()
{
    std::cout << LC << "Creating minimal map for WebAssembly..." << std::endl;

    auto map = new Map();

#ifdef __EMSCRIPTEN__
    // WebAssembly环境下使用最简化的配置
    std::cout << LC << "WebAssembly mode: Creating empty map to avoid potential crashes" << std::endl;

    // 不添加任何图层，避免潜在的内存访问问题
    // 这样可以先确保基本的OSG渲染循环能正常工作
    std::cout << LC << "Created empty map - no layers added" << std::endl;

#else
    // 桌面版本可以使用完整功能
    std::cout << LC << "Desktop mode: Creating full-featured map" << std::endl;

    // 添加Google Maps卫星图像
    auto googleImagery = new XYZImageLayer();
    googleImagery->setName("Google Satellite");

    std::string googleURL = "https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}";
    URI googleURI(googleURL);
    googleImagery->setURL(googleURI);
    googleImagery->setFormat("jpg");

    // 使用global-mercator投影
    const Profile *profile = Profile::create("global-mercator");
    googleImagery->setProfile(profile);
    googleImagery->setMinLevel(0);
    googleImagery->setMaxLevel(18);
    googleImagery->setEnabled(true);
    googleImagery->setVisible(true);

    map->addLayer(googleImagery);
    std::cout << LC << "Added Google Maps satellite imagery layer" << std::endl;

    // 添加经纬网
    auto graticule = new GeodeticGraticule();
    graticule->setName("Graticule");
    map->addLayer(graticule);
    std::cout << LC << "Added geodetic graticule layer" << std::endl;
#endif

    return map;
}

// 主循环函数（用于Emscripten）
void mainLoop()
{
    if (!g_running || !g_viewer)
    {
        return;
    }

#ifdef __EMSCRIPTEN__
    // 确保WebGL上下文仍然有效
    if (g_webgl_context > 0)
    {
        emscripten_webgl_make_context_current(g_webgl_context);
    }
#endif

    static int frameCount = 0;
    frameCount++;

    if (!g_viewer->done())
    {
        try
        {
            // 渲染一帧
            g_viewer->frame();

            // 每60帧打印一次状态用于调试
            if (frameCount % 60 == 0)
            {
                std::cout << LC << "Frame " << frameCount << " - OSGEarth rendering normally" << std::endl;
            }
        }
        catch (const std::exception &e)
        {
            std::cerr << LC << "Exception in main loop: " << e.what() << std::endl;
        }
        catch (...)
        {
            std::cerr << LC << "Unknown exception in main loop" << std::endl;
        }
    }
}

// 初始化函数
int initializeViewer()
{
    std::cout << LC << "Initializing OSGEarth WebAssembly viewer..." << std::endl;

    try
    {
#ifdef __EMSCRIPTEN__
        // 首先初始化WebGL上下文
        if (!initializeWebGL())
        {
            std::cout << LC << "Failed to initialize WebGL context" << std::endl;
            return -1;
        }

        // 初始化WindowingSystemInterface
        initializeWindowingSystem();
#endif

        // 配置网络
        configureNetworking();

        // 创建查看器
        g_viewer = new osgViewer::Viewer();

    // 应用全局默认设置
    GLUtils::setGlobalDefaults(g_viewer->getCamera()->getOrCreateStateSet());

    // 设置窗口尺寸
    int width = 800;  // 与HTML canvas匹配
    int height = 600; // 与HTML canvas匹配

#ifdef __EMSCRIPTEN__
    // WebAssembly版本 - 为WebAssembly环境创建专用的GraphicsContext
    osg::ref_ptr<osg::GraphicsContext::Traits> traits = new osg::GraphicsContext::Traits();
    traits->x = 0;
    traits->y = 0;
    traits->width = width;
    traits->height = height;
    traits->red = 8;
    traits->green = 8;
    traits->blue = 8;
    traits->alpha = 8;
    traits->depth = 24;
    traits->stencil = 0;
    traits->windowDecoration = false;
    traits->doubleBuffer = true;
    traits->sharedContext = nullptr;
    traits->setInheritedWindowPixelFormat = false;

    std::cout << LC << "Creating EmscriptenGraphicsContext..." << std::endl;
    osg::ref_ptr<EmscriptenGraphicsContext> gc = new EmscriptenGraphicsContext(traits.get());

    if (gc.valid())
    {
        std::cout << LC << "EmscriptenGraphicsContext created successfully" << std::endl;

        // 设置相机使用我们的GraphicsContext - 基于osg_texture_test的成功配置
        osg::Camera *camera = g_viewer->getCamera();
        camera->setGraphicsContext(gc.get());
        camera->setViewport(new osg::Viewport(0, 0, width, height));

        // 使用适合地球渲染的投影矩阵参数
        camera->setProjectionMatrixAsPerspective(45.0, (double)width / (double)height, 1.0, 100000000.0);

        // 设置清除颜色为深蓝色（模拟太空背景）
        camera->setClearColor(osg::Vec4(0.1f, 0.1f, 0.2f, 1.0f));
        camera->setClearMask(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

        std::cout << LC << "Camera configured with EmscriptenGraphicsContext" << std::endl;
        std::cout << LC << "Viewport: " << width << "x" << height << std::endl;
        std::cout << LC << "Projection: 45° FOV, near=1.0, far=100000000.0" << std::endl;
    }
    else
    {
        std::cout << LC << "Failed to create EmscriptenGraphicsContext" << std::endl;
        return -1;
    }

    // WebGL兼容性设置 - 强制单线程
    g_viewer->setThreadingModel(osgViewer::Viewer::SingleThreaded);

    // 禁用不支持的OpenGL功能
    osg::ref_ptr<osg::StateSet> globalStateSet = g_viewer->getCamera()->getOrCreateStateSet();

    // 禁用WebGL不支持的功能
    globalStateSet->setMode(GL_ALPHA_TEST, osg::StateAttribute::OFF);

    // 强制使用VBO
    osg::DisplaySettings::instance()->setVertexBufferHint(osg::DisplaySettings::VERTEX_BUFFER_OBJECT);

    // WebGL纹理渲染优化设置
    globalStateSet->setMode(GL_CULL_FACE, osg::StateAttribute::ON);
    globalStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);

    // 清除颜色已在上面的相机配置中设置

    std::cout << LC << "WebGL compatibility settings applied successfully" << std::endl;
#else
    // 桌面版本 - 使用GraphicsContext
    std::cout << LC << "Desktop mode: Creating graphics context..." << std::endl;
    osg::ref_ptr<osg::GraphicsContext::Traits> traits = new osg::GraphicsContext::Traits;
    traits->x = 0;
    traits->y = 0;
    traits->width = width;
    traits->height = height;
    traits->windowDecoration = false;
    traits->doubleBuffer = true;
    traits->sharedContext = 0;

    osg::ref_ptr<osg::GraphicsContext> gc = osg::GraphicsContext::createGraphicsContext(traits.get());
    if (gc.valid())
    {
        std::cout << LC << "Graphics context created successfully" << std::endl;
        g_viewer->getCamera()->setGraphicsContext(gc.get());
        g_viewer->getCamera()->setViewport(new osg::Viewport(0, 0, width, height));

        // 设置投影矩阵
        double fovy = 45.0;
        double aspectRatio = double(width) / double(height);
        double zNear = 1.0;
        double zFar = 1e12;

        g_viewer->getCamera()->setProjectionMatrixAsPerspective(fovy, aspectRatio, zNear, zFar);
        g_viewer->getCamera()->setComputeNearFarMode(osg::CullSettings::COMPUTE_NEAR_FAR_USING_BOUNDING_VOLUMES);

        std::cout << LC << "Camera and viewport configured successfully" << std::endl;
    }
    else
    {
        std::cerr << LC << "Unable to create graphics context." << std::endl;
        return 1;
    }
#endif

    // 设置操控器（即使没有图形上下文也要设置）
    std::cout << LC << "Setting up camera manipulator..." << std::endl;

#ifdef __EMSCRIPTEN__
    // WebAssembly环境下使用EarthManipulator的简化版本
    osg::ref_ptr<EarthManipulator> manip = new EarthManipulator();

    // 简化的EarthManipulator设置，适合WebAssembly环境
    EarthManipulator::Settings *settings = manip->getSettings();

    // 基本的鼠标绑定
    settings->bindMouse(EarthManipulator::ACTION_PAN, EarthManipulator::MOUSE_LEFT_BUTTON);
    settings->bindMouse(EarthManipulator::ACTION_ROTATE, EarthManipulator::MOUSE_MIDDLE_BUTTON);
    settings->bindMouse(EarthManipulator::ACTION_ZOOM, EarthManipulator::MOUSE_RIGHT_BUTTON);

    // 滚轮缩放
    settings->bindScroll(EarthManipulator::ACTION_ZOOM_IN, osgGA::GUIEventAdapter::SCROLL_UP);
    settings->bindScroll(EarthManipulator::ACTION_ZOOM_OUT, osgGA::GUIEventAdapter::SCROLL_DOWN);

    // 设置合理的距离范围
    settings->setMinMaxDistance(1000.0, 50000000.0);
    settings->setZoomToMouse(true);

    std::cout << LC << "Using simplified EarthManipulator for WebAssembly" << std::endl;
#else
    // 桌面版本使用完整的EarthManipulator
    osg::ref_ptr<EarthManipulator> manip = new EarthManipulator();
    EarthManipulator::Settings *settings = manip->getSettings();

    // 配置Google Earth风格的控制
    EarthManipulator::ActionOptions panOptions;
    panOptions.add(EarthManipulator::OPTION_CONTINUOUS, true);
    panOptions.add(EarthManipulator::OPTION_SCALE_X, 5.0);
    panOptions.add(EarthManipulator::OPTION_SCALE_Y, 5.0);
    settings->bindMouse(EarthManipulator::ACTION_PAN, EarthManipulator::MOUSE_LEFT_BUTTON, 0, panOptions);

    EarthManipulator::ActionOptions rotateOptions;
    rotateOptions.add(EarthManipulator::OPTION_CONTINUOUS, true);
    rotateOptions.add(EarthManipulator::OPTION_SCALE_X, 5.0);
    rotateOptions.add(EarthManipulator::OPTION_SCALE_Y, 5.0);
    settings->bindMouse(EarthManipulator::ACTION_ROTATE, EarthManipulator::MOUSE_MIDDLE_BUTTON, 0, rotateOptions);

    EarthManipulator::ActionOptions zoomOptions;
    zoomOptions.add(EarthManipulator::OPTION_CONTINUOUS, true);
    zoomOptions.add(EarthManipulator::OPTION_SCALE_X, 2.0);
    zoomOptions.add(EarthManipulator::OPTION_SCALE_Y, 2.0);
    settings->bindMouse(EarthManipulator::ACTION_ZOOM, EarthManipulator::MOUSE_RIGHT_BUTTON, 0, zoomOptions);

    // 滚轮缩放
    EarthManipulator::ActionOptions scrollInOptions;
    scrollInOptions.add(EarthManipulator::OPTION_SCALE_X, 1.5);
    scrollInOptions.add(EarthManipulator::OPTION_SCALE_Y, 1.5);
    settings->bindScroll(EarthManipulator::ACTION_ZOOM_IN, osgGA::GUIEventAdapter::SCROLL_UP, 0, scrollInOptions);

    EarthManipulator::ActionOptions scrollOutOptions;
    scrollOutOptions.add(EarthManipulator::OPTION_SCALE_X, 1.5);
    scrollOutOptions.add(EarthManipulator::OPTION_SCALE_Y, 1.5);
    settings->bindScroll(EarthManipulator::ACTION_ZOOM_OUT, osgGA::GUIEventAdapter::SCROLL_DOWN, 0, scrollOutOptions);

    settings->setMinMaxDistance(1000.0, 50000000.0);
    settings->setZoomToMouse(true);

    std::cout << LC << "Using EarthManipulator for desktop" << std::endl;
#endif

    g_viewer->setCameraManipulator(manip);

    // 禁用小特征剔除
    g_viewer->getCamera()->setSmallFeatureCullingPixelSize(-1.0f);

    // 创建地图
    osg::ref_ptr<Map> map = createSimpleMap();
    if (!map.valid())
    {
        std::cerr << LC << "Failed to create map" << std::endl;
        return 1;
    }

    // 创建地图节点
    try
    {
#ifdef __EMSCRIPTEN__
        // WebAssembly环境下跳过MapNode创建，使用简单的OSG场景
        std::cout << LC << "WebAssembly mode: Creating simple OSG scene instead of MapNode" << std::endl;

        // 创建一个简单的彩色立方体作为测试场景
        osg::ref_ptr<osg::Group> root = new osg::Group();
        osg::ref_ptr<osg::Geode> geode = new osg::Geode();

        // 创建一个简单的三角形，而不是使用ShapeDrawable
        osg::ref_ptr<osg::Geometry> geometry = new osg::Geometry();

        // 创建顶点数组
        osg::ref_ptr<osg::Vec3Array> vertices = new osg::Vec3Array();
        vertices->push_back(osg::Vec3(0.0f, 0.0f, 1.0f));   // 顶点
        vertices->push_back(osg::Vec3(-1.0f, 0.0f, -1.0f)); // 左下
        vertices->push_back(osg::Vec3(1.0f, 0.0f, -1.0f));  // 右下
        geometry->setVertexArray(vertices);

        // 创建颜色数组
        osg::ref_ptr<osg::Vec4Array> colors = new osg::Vec4Array();
        colors->push_back(osg::Vec4(1.0f, 0.0f, 0.0f, 1.0f)); // 红色
        geometry->setColorArray(colors);
        geometry->setColorBinding(osg::Geometry::BIND_OVERALL);

        // 创建法线数组
        osg::ref_ptr<osg::Vec3Array> normals = new osg::Vec3Array();
        normals->push_back(osg::Vec3(0.0f, 1.0f, 0.0f));
        geometry->setNormalArray(normals);
        geometry->setNormalBinding(osg::Geometry::BIND_OVERALL);

        // 添加图元
        geometry->addPrimitiveSet(new osg::DrawArrays(osg::PrimitiveSet::TRIANGLES, 0, 3));

        geode->addDrawable(geometry);
        root->addChild(geode);

        g_viewer->setSceneData(root);
        std::cout << LC << "Simple OSG scene created successfully" << std::endl;

        // 设置简单的相机位置 - 使用EarthManipulator
        osg::ref_ptr<EarthManipulator> earthManip =
            dynamic_cast<EarthManipulator *>(g_viewer->getCameraManipulator());
        if (earthManip)
        {
            // 设置一个简单的视点，俯视场景
            Viewpoint vp;
            vp.focalPoint() = GeoPoint(SpatialReference::get("wgs84"), 0.0, 0.0, 0.0);
            vp.range() = Distance(10000000.0, Units::METERS); // 1000万米高度
            vp.heading() = Angle(0.0, Units::DEGREES);
            vp.pitch() = Angle(-45.0, Units::DEGREES);

            earthManip->setViewpoint(vp);
            std::cout << LC << "EarthManipulator viewpoint set for simple scene" << std::endl;
        }

#else
        // 桌面版本使用完整的OSGEarth功能
        g_mapNode = new MapNode(map.get());
        if (!g_mapNode)
        {
            std::cerr << LC << "Failed to create MapNode" << std::endl;
            return 1;
        }
        std::cout << LC << "MapNode created successfully" << std::endl;

        // 设置场景数据
        g_viewer->setSceneData(g_mapNode);
        std::cout << LC << "Scene data set successfully" << std::endl;

        // 设置默认视点（中国中心）
        if (g_mapNode && manip)
        {
            double longitude = 104.0;     // 东经104度
            double latitude = 35.5;       // 北纬35.5度
            double altitude = 15000000.0; // 高度1500万米

            Viewpoint vp;
            vp.focalPoint() = GeoPoint(
                g_mapNode->getMapSRS(),
                longitude, latitude, 0.0,
                ALTMODE_ABSOLUTE);
            vp.range() = Distance(altitude, Units::METERS);
            vp.heading() = Angle(0.0, Units::DEGREES);
            vp.pitch() = Angle(-90.0, Units::DEGREES);

            osg::ref_ptr<EarthManipulator> earthManip =
                dynamic_cast<EarthManipulator *>(manip.get());
            if (earthManip)
            {
                earthManip->setViewpoint(vp);
                std::cout << LC << "Default viewpoint set to China (104°E, 35.5°N)" << std::endl;
            }
        }
#endif
    }
    catch (const std::exception &e)
    {
        std::cerr << LC << "Exception in initializeViewer: " << e.what() << std::endl;
        return -1;
    }

    // 实现viewer - 这是关键步骤，基于osg_texture_test的成功经验
    std::cout << LC << "Realizing OSG Viewer..." << std::endl;
    g_viewer->realize();

    // 验证viewer是否正确实现
    if (g_viewer->isRealized())
    {
        std::cout << LC << "OSG Viewer realized successfully" << std::endl;
    }
    else
    {
        std::cout << LC << "Warning: OSG Viewer realization may have failed" << std::endl;
    }

    std::cout << LC << "Scene initialization completed successfully" << std::endl;
    return 0;
}

// 主函数
int main(int argc, char **argv)
{
    try
    {
        std::cout << LC << "Starting OSGEarth WebAssembly application..." << std::endl;

#ifdef __EMSCRIPTEN__
        // WebAssembly环境下跳过osgEarth::initialize，避免潜在的段错误
        std::cout << LC << "WebAssembly mode: Skipping osgEarth::initialize to avoid crashes" << std::endl;

        // 只进行基本的参数解析
        osg::ArgumentParser arguments(&argc, argv);
        std::cout << LC << "Basic argument parsing completed" << std::endl;
#else
        // 桌面版本需要完整的osgEarth初始化
        osg::ArgumentParser arguments(&argc, argv);
        osgEarth::initialize(arguments);
        std::cout << LC << "OSGEarth initialized successfully" << std::endl;
#endif

        // 初始化查看器
        if (initializeViewer() != 0)
        {
            std::cerr << LC << "Failed to initialize viewer" << std::endl;
            return 1;
        }

        std::cout << LC << "Viewer initialized successfully" << std::endl;

#ifdef __EMSCRIPTEN__
        // 在WebAssembly环境中使用Emscripten的主循环
        std::cout << LC << "Starting Emscripten main loop..." << std::endl;

        // 先取消任何现有的主循环，避免冲突
        emscripten_cancel_main_loop();

        // 然后设置我们的主循环
        emscripten_set_main_loop(mainLoop, 0, 1);
#else
        // 在其他环境中使用标准主循环
        return g_viewer->run();
#endif

        return 0;
    }
    catch (const std::exception &e)
    {
        std::cerr << LC << "Exception in main: " << e.what() << std::endl;
        return 1;
    }
    catch (...)
    {
        std::cerr << LC << "Unknown exception in main" << std::endl;
        return 1;
    }
}

#ifdef __EMSCRIPTEN__
// 导出函数供JavaScript调用
extern "C"
{

    // 获取OSGEarth状态信息
    EMSCRIPTEN_KEEPALIVE
    const char *getOSGEarthStatus()
    {
        static std::string status;
        if (g_viewer && g_mapNode)
        {
            status = "OSGEarth initialized and running in headless mode";
        }
        else if (g_viewer)
        {
            status = "OSGEarth viewer created but no map node";
        }
        else
        {
            status = "OSGEarth not initialized";
        }
        return status.c_str();
    }

    // 获取地图信息
    EMSCRIPTEN_KEEPALIVE
    const char *getMapInfo()
    {
        static std::string info;
        if (g_mapNode && g_mapNode->getMap())
        {
            auto map = g_mapNode->getMap();
            info = "Map layers: " + std::to_string(map->getNumLayers());
        }
        else
        {
            info = "No map available";
        }
        return info.c_str();
    }

    // 检查是否正在运行
    EMSCRIPTEN_KEEPALIVE
    bool isRunning()
    {
        return g_viewer && !g_viewer->done();
    }

    // 强制更新一帧
    EMSCRIPTEN_KEEPALIVE
    void forceUpdate()
    {
        if (g_viewer && !g_viewer->done())
        {
            std::cout << LC << "Force update requested from JavaScript" << std::endl;
        }
    }
}
#endif
