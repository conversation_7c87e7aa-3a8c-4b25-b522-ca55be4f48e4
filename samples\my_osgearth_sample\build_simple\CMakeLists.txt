# 简单的 OSG WebAssembly 测试 - 基于成功的 my_osg_sample 模板
cmake_minimum_required(VERSION 3.10)
project(simple_osg_wasm)

set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

if(EMSCRIPTEN)
    include_directories(SYSTEM ${CMAKE_JS_INC_PATH})
endif()

# OSG 库路径
set(OSG_WASM_LIB_DIR "F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep")

# 包含头文件目录
include_directories(
    "${OSG_WASM_LIB_DIR}/include"
)

# 源文件
set(SOURCES
    simple_osg_wasm.cpp
)

# 创建可执行文件
add_executable(simple_osg_wasm ${SOURCES})

if(EMSCRIPTEN)
    # OSG WASM 静态库（包含 osgText 以解决 StatsHandler 依赖）
    set(OSG_LIBRARIES
        "${OSG_WASM_LIB_DIR}/lib/libosg.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgViewer.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgDB.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgGA.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgUtil.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgText.a"
        "${OSG_WASM_LIB_DIR}/lib/libOpenThreads.a"
    )
    
    # 基本第三方库（包含 freetype 以支持 osgText）
    set(THIRD_PARTY_LIBRARIES
        "${OSG_WASM_LIB_DIR}/lib/libz.a"
        "${OSG_WASM_LIB_DIR}/lib/libpng16.a"
        "${OSG_WASM_LIB_DIR}/lib/libjpeg.a"
        "${OSG_WASM_LIB_DIR}/lib/libfreetype.a"
    )
    
    # 链接库
    target_link_libraries(simple_osg_wasm
        ${OSG_LIBRARIES}
        ${THIRD_PARTY_LIBRARIES}
    )
    
    # 编译器标志（单线程版本）
    set(EM_COMPILE_FLAGS
        "-DOSG_GLSL_VERSION=300"
    )
    
    # 链接器标志（仅用于链接阶段）- 支持多线程和 SharedArrayBuffer
    set(EM_LINK_FLAGS_LIST 
        "-s USE_SDL=2"
        "-s USE_WEBGL2=1"
        "-s FULL_ES3=1"
        "-s WASM=1"
        "-s INITIAL_MEMORY=134217728"  # 128MB
        "-s ALLOW_MEMORY_GROWTH=1"
        "-s MAXIMUM_MEMORY=2147483648"  # 2GB
        "-s FETCH=1"
        "-s ASYNCIFY=1"
        "-s ASSERTIONS=1"
        "-s SAFE_HEAP=0"
        "-s STACK_OVERFLOW_CHECK=1"
        "-s EXPORTED_FUNCTIONS=['_main']"
        "-s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','HEAPU8']"
        "-s NO_EXIT_RUNTIME=1"
        "-s DISABLE_EXCEPTION_CATCHING=0"
        "-s FORCE_FILESYSTEM=1"
        "-s USE_PTHREADS=0"
        "-s PTHREAD_POOL_SIZE=0"
        "-O3"
    )
    string(REPLACE ";" " " EM_LINK_FLAGS "${EM_LINK_FLAGS_LIST}")

    # 设置编译器标志
    target_compile_options(simple_osg_wasm PRIVATE ${EM_COMPILE_FLAGS})
    
    # 设置链接器属性
    set_target_properties(simple_osg_wasm PROPERTIES
        SUFFIX ".html"
        LINK_FLAGS "${EM_LINK_FLAGS}"
    )

    message(STATUS "Simple OSG WebAssembly build configured (single-threaded)")
    message(STATUS "Output will be: simple_osg_wasm.html, simple_osg_wasm.js, simple_osg_wasm.wasm")
    
else()
    # 桌面版本配置
    find_package(OpenSceneGraph REQUIRED)
    
    target_link_libraries(simple_osg_wasm
        ${OPENSCENEGRAPH_LIBRARIES}
    )
endif()
