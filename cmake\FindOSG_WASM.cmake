# FindOSG.cmake - WebAssembly版本
# 查找OpenSceneGraph库，支持外部WASM依赖

# WebAssembly外部依赖支持
IF(USE_EXTERNAL_WASM_DEPENDS)
    message(STATUS "Using external WASM dependencies for OSG")
    
    # 设置OSG根目录
    if(NOT OSG_DIR)
        set(OSG_DIR "$ENV{OSG_DIR}")
        if(NOT OSG_DIR)
            message(FATAL_ERROR "OSG_DIR not set for external WASM dependencies")
        endif()
    endif()
    
    # 设置包含目录
    SET(OSG_INCLUDE_DIR "${OSG_DIR}/include")
    SET(OSG_GEN_INCLUDE_DIR "${OSG_DIR}/include")
    
    # 设置库文件路径 - 强制使用静态库
    SET(OSG_LIBRARY "${OSG_DIR}/lib/libosg.a")
    SET(OSGUTIL_LIBRARY "${OSG_DIR}/lib/libosgUtil.a")
    SET(OSGDB_LIBRARY "${OSG_DIR}/lib/libosgDB.a")
    SET(OSGGA_LIBRARY "${OSG_DIR}/lib/libosgGA.a")
    SET(OSGFX_LIBRARY "${OSG_DIR}/lib/libosgFX.a")
    SET(OSGSIM_LIBRARY "${OSG_DIR}/lib/libosgSim.a")
    SET(OSGTEXT_LIBRARY "${OSG_DIR}/lib/libosgText.a")
    SET(OSGVIEWER_LIBRARY "${OSG_DIR}/lib/libosgViewer.a")
    SET(OSGVOLUME_LIBRARY "${OSG_DIR}/lib/libosgVolume.a")
    SET(OSGWIDGET_LIBRARY "${OSG_DIR}/lib/libosgWidget.a")
    SET(OSGSHADOW_LIBRARY "${OSG_DIR}/lib/libosgShadow.a")
    SET(OSGANIMATION_LIBRARY "${OSG_DIR}/lib/libosgAnimation.a")
    SET(OSGPARTICLE_LIBRARY "${OSG_DIR}/lib/libosgParticle.a")
    SET(OSGTERRAIN_LIBRARY "${OSG_DIR}/lib/libosgTerrain.a")
    SET(OSGMANIPULATOR_LIBRARY "${OSG_DIR}/lib/libosgManipulator.a")
    
    # OpenThreads库
    SET(OPENTHREADS_LIBRARY "${OSG_DIR}/lib/libOpenThreads.a")
    SET(OPENTHREADS_INCLUDE_DIR "${OSG_DIR}/include")
    
    # 设置找到标志
    SET(OSG_FOUND TRUE)
    SET(OSGUTIL_FOUND TRUE)
    SET(OSGDB_FOUND TRUE)
    SET(OSGGA_FOUND TRUE)
    SET(OSGFX_FOUND TRUE)
    SET(OSGSIM_FOUND TRUE)
    SET(OSGTEXT_FOUND TRUE)
    SET(OSGVIEWER_FOUND TRUE)
    SET(OSGVOLUME_FOUND TRUE)
    SET(OSGWIDGET_FOUND TRUE)
    SET(OSGSHADOW_FOUND TRUE)
    SET(OSGANIMATION_FOUND TRUE)
    SET(OSGPARTICLE_FOUND TRUE)
    SET(OSGTERRAIN_FOUND TRUE)
    SET(OSGMANIPULATOR_FOUND TRUE)
    SET(OPENTHREADS_FOUND TRUE)
    
    # 设置版本信息
    SET(OSG_VERSION "3.6.5")  # 假设版本，实际应该从头文件读取
    
    message(STATUS "OSG WASM libraries configured:")
    message(STATUS "  OSG_DIR: ${OSG_DIR}")
    message(STATUS "  OSG_INCLUDE_DIR: ${OSG_INCLUDE_DIR}")
    message(STATUS "  OSG_LIBRARY: ${OSG_LIBRARY}")
    
ELSE()
    # 使用vcpkg查找OSG
    find_package(PkgConfig QUIET)
    
    # 查找OSG组件
    find_package(osg REQUIRED)
    find_package(osgUtil REQUIRED)
    find_package(osgDB REQUIRED)
    find_package(osgGA REQUIRED)
    find_package(osgFX REQUIRED)
    find_package(osgSim REQUIRED)
    find_package(osgText REQUIRED)
    find_package(osgViewer REQUIRED)
    find_package(osgVolume REQUIRED)
    find_package(osgWidget REQUIRED)
    find_package(osgShadow REQUIRED)
    find_package(osgAnimation REQUIRED)
    find_package(osgParticle REQUIRED)
    find_package(osgTerrain REQUIRED)
    find_package(osgManipulator REQUIRED)
    find_package(OpenThreads REQUIRED)
    
    # 设置变量以保持兼容性
    SET(OSG_INCLUDE_DIR ${osg_INCLUDE_DIRS})
    SET(OSG_LIBRARY ${osg_LIBRARIES})
    SET(OSGUTIL_LIBRARY ${osgUtil_LIBRARIES})
    SET(OSGDB_LIBRARY ${osgDB_LIBRARIES})
    SET(OSGGA_LIBRARY ${osgGA_LIBRARIES})
    SET(OSGFX_LIBRARY ${osgFX_LIBRARIES})
    SET(OSGSIM_LIBRARY ${osgSim_LIBRARIES})
    SET(OSGTEXT_LIBRARY ${osgText_LIBRARIES})
    SET(OSGVIEWER_LIBRARY ${osgViewer_LIBRARIES})
    SET(OSGVOLUME_LIBRARY ${osgVolume_LIBRARIES})
    SET(OSGWIDGET_LIBRARY ${osgWidget_LIBRARIES})
    SET(OSGSHADOW_LIBRARY ${osgShadow_LIBRARIES})
    SET(OSGANIMATION_LIBRARY ${osgAnimation_LIBRARIES})
    SET(OSGPARTICLE_LIBRARY ${osgParticle_LIBRARIES})
    SET(OSGTERRAIN_LIBRARY ${osgTerrain_LIBRARIES})
    SET(OSGMANIPULATOR_LIBRARY ${osgManipulator_LIBRARIES})
    SET(OPENTHREADS_LIBRARY ${OpenThreads_LIBRARIES})
    SET(OPENTHREADS_INCLUDE_DIR ${OpenThreads_INCLUDE_DIRS})
    
    # 设置找到标志
    SET(OSG_FOUND TRUE)
    SET(OSGUTIL_FOUND TRUE)
    SET(OSGDB_FOUND TRUE)
    SET(OSGGA_FOUND TRUE)
    SET(OSGFX_FOUND TRUE)
    SET(OSGSIM_FOUND TRUE)
    SET(OSGTEXT_FOUND TRUE)
    SET(OSGVIEWER_FOUND TRUE)
    SET(OSGVOLUME_FOUND TRUE)
    SET(OSGWIDGET_FOUND TRUE)
    SET(OSGSHADOW_FOUND TRUE)
    SET(OSGANIMATION_FOUND TRUE)
    SET(OSGPARTICLE_FOUND TRUE)
    SET(OSGTERRAIN_FOUND TRUE)
    SET(OSGMANIPULATOR_FOUND TRUE)
    SET(OPENTHREADS_FOUND TRUE)
    
ENDIF()

# 设置所有OSG库的列表
SET(OSG_LIBRARIES
    ${OSG_LIBRARY}
    ${OSGUTIL_LIBRARY}
    ${OSGDB_LIBRARY}
    ${OSGGA_LIBRARY}
    ${OSGFX_LIBRARY}
    ${OSGSIM_LIBRARY}
    ${OSGTEXT_LIBRARY}
    ${OSGVIEWER_LIBRARY}
    ${OSGVOLUME_LIBRARY}
    ${OSGWIDGET_LIBRARY}
    ${OSGSHADOW_LIBRARY}
    ${OSGANIMATION_LIBRARY}
    ${OSGPARTICLE_LIBRARY}
    ${OSGTERRAIN_LIBRARY}
    ${OSGMANIPULATOR_LIBRARY}
    ${OPENTHREADS_LIBRARY}
)

# 设置包含目录列表
SET(OSG_INCLUDE_DIRS ${OSG_INCLUDE_DIR} ${OPENTHREADS_INCLUDE_DIR})

# 处理标准参数
include(FindPackageHandleStandardArgs)
FIND_PACKAGE_HANDLE_STANDARD_ARGS(OSG DEFAULT_MSG OSG_LIBRARY OSG_INCLUDE_DIR)

# 标记为高级变量
MARK_AS_ADVANCED(
    OSG_INCLUDE_DIR
    OSG_LIBRARY
    OSGUTIL_LIBRARY
    OSGDB_LIBRARY
    OSGGA_LIBRARY
    OSGFX_LIBRARY
    OSGSIM_LIBRARY
    OSGTEXT_LIBRARY
    OSGVIEWER_LIBRARY
    OSGVOLUME_LIBRARY
    OSGWIDGET_LIBRARY
    OSGSHADOW_LIBRARY
    OSGANIMATION_LIBRARY
    OSGPARTICLE_LIBRARY
    OSGTERRAIN_LIBRARY
    OSGMANIPULATOR_LIBRARY
    OPENTHREADS_LIBRARY
    OPENTHREADS_INCLUDE_DIR
)
