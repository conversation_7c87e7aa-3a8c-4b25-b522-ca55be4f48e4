# osgEarth WebAssembly 多线程版本 - 修复黑屏和多线程问题
cmake_minimum_required(VERSION 3.10)
project(osgearth_wasm_fixed)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

if(EMSCRIPTEN)
    include_directories(SYSTEM ${CMAKE_JS_INC_PATH})
endif()

# OSG 和 osgEarth 库路径
set(OSG_WASM_LIB_DIR "F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep")
set(OSGEARTH_SOURCE_DIR "F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src")

# 包含头文件目录
include_directories(
    "${OSG_WASM_LIB_DIR}/include"
    "${OSGEARTH_SOURCE_DIR}"
)

# 源文件
set(SOURCES
    osgearth_wasm.cpp
)

# 创建可执行文件
add_executable(osgearth_wasm_fixed ${SOURCES})

if(EMSCRIPTEN)
    # osgEarth WASM 静态库
    set(OSGEARTH_LIBRARIES
        "${OSGEARTH_SOURCE_DIR}/../redist_wasm/libosgEarth.a"
    )

    # OSG WASM 静态库（包含所有必要的库）
    set(OSG_LIBRARIES
        "${OSG_WASM_LIB_DIR}/lib/libosg.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgViewer.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgDB.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgGA.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgUtil.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgText.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgShadow.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgSim.a"
        "${OSG_WASM_LIB_DIR}/lib/libOpenThreads.a"
    )
    
    # OSG 插件库已集成在 libosgDB.a 中
    
    # 第三方库
    set(THIRD_PARTY_LIBRARIES
        "${OSG_WASM_LIB_DIR}/lib/libgeos.a"
        "${OSG_WASM_LIB_DIR}/lib/libproj.a"
        "${OSG_WASM_LIB_DIR}/lib/libcurl.a"
        "${OSG_WASM_LIB_DIR}/lib/libGeographicLib.a"
        "${OSG_WASM_LIB_DIR}/lib/libz.a"
        "${OSG_WASM_LIB_DIR}/lib/libexpat.a"
        "${OSG_WASM_LIB_DIR}/lib/libpng16.a"
        "${OSG_WASM_LIB_DIR}/lib/libjpeg.a"
        "${OSG_WASM_LIB_DIR}/lib/libtiff.a"
        "${OSG_WASM_LIB_DIR}/lib/libfreetype.a"
        "${OSG_WASM_LIB_DIR}/lib/libfontconfig.a"
        "${OSG_WASM_LIB_DIR}/lib/libbz2.a"
        "${OSG_WASM_LIB_DIR}/lib/liblzma.a"
    )
    
    # 链接所有库
    target_link_libraries(osgearth_wasm_fixed
        ${OSGEARTH_LIBRARIES}
        ${OSG_LIBRARIES}
        ${THIRD_PARTY_LIBRARIES}
    )
    
    # 编译器标志（单线程版本，避免多线程问题）
    set(EM_COMPILE_FLAGS
        "-DOSG_GLSL_VERSION=300"
        "-DOSGEARTH_HAVE_GEOS=1"
        "-DOSGEARTH_HAVE_GDAL=0"
        "-DUSE_EXTERNAL_WASM_DEPENDS=ON"
        "-DOSGEARTH_DISABLE_THREADING=1"
        "-O3"
    )

    # 链接器标志 - 单线程版本（修复黑屏问题）
    set(EM_LINK_FLAGS_LIST
        "-s USE_SDL=2"
        "-s USE_WEBGL2=1"
        "-s FULL_ES3=1"
        "-s WASM=1"
        "-s INITIAL_MEMORY=268435456"  # 256MB
        "-s ALLOW_MEMORY_GROWTH=1"
        "-s MAXIMUM_MEMORY=2147483648"  # 2GB
        "-s FETCH=1"
        "-s ASYNCIFY=1"
        "-s ASSERTIONS=1"
        "-s SAFE_HEAP=0"
        "-s STACK_OVERFLOW_CHECK=1"
        "-s EXPORTED_FUNCTIONS=['_main']"
        "-s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','HEAPU8']"
        "-s NO_EXIT_RUNTIME=1"
        "-s DISABLE_EXCEPTION_CATCHING=0"
        "-s FORCE_FILESYSTEM=1"
        "-s USE_PTHREADS=0"
        "-s PTHREAD_POOL_SIZE=0"
        "-O3"
    )
    string(REPLACE ";" " " EM_LINK_FLAGS "${EM_LINK_FLAGS_LIST}")

    # 设置编译器标志
    target_compile_options(osgearth_wasm_fixed PRIVATE ${EM_COMPILE_FLAGS})
    
    # 设置链接器属性
    set_target_properties(osgearth_wasm_fixed PROPERTIES
        SUFFIX ".html"
        LINK_FLAGS "${EM_LINK_FLAGS}"
    )

    message(STATUS "osgEarth WebAssembly build configured (single-threaded, fixed version)")
    message(STATUS "Output will be: osgearth_wasm_fixed.html, osgearth_wasm_fixed.js, osgearth_wasm_fixed.wasm")
    
else()
    # 桌面版本配置
    find_package(OpenSceneGraph REQUIRED)
    find_package(osgEarth REQUIRED)
    
    target_link_libraries(osgearth_wasm_fixed
        ${OPENSCENEGRAPH_LIBRARIES}
        ${OSGEARTH_LIBRARIES}
    )
endif()
