/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#include "ShapefileReader.h"
#include <osgEarth/Notify>
#include <osgEarth/StringUtils>
#include <algorithm>
#include <cstring>

#define LC "[ShapefileReader] "

using namespace osgEarth;

//........................................................................
// ShapefileReader implementation

ShapefileReader::ShapefileReader() : _isOpen(false),
                                     _currentPos(0)
{
    memset(&_header, 0, sizeof(_header));
}

ShapefileReader::~ShapefileReader()
{
    close();
}

bool ShapefileReader::open(const std::string &filename)
{
    close();

    _file.open(filename, std::ios::binary);
    if (!_file.is_open())
    {
        OE_WARN << LC << "Cannot open shapefile: " << filename << std::endl;
        return false;
    }

    if (!readHeader())
    {
        OE_WARN << LC << "Failed to read shapefile header" << std::endl;
        close();
        return false;
    }

    _isOpen = true;
    return true;
}

void ShapefileReader::close()
{
    if (_file.is_open())
    {
        _file.close();
    }
    _isOpen = false;
}

bool ShapefileReader::readHeader()
{
    if (!_file.is_open())
        return false;

    _file.seekg(0, std::ios::beg);

    // Read file code (big endian)
    _header.fileCode = swapEndian32(readInt32());
    if (_header.fileCode != 9994)
    {
        OE_WARN << LC << "Invalid shapefile file code: " << _header.fileCode << std::endl;
        return false;
    }

    // Skip unused bytes
    _file.seekg(24, std::ios::beg);

    // Read file length (big endian, in 16-bit words)
    _header.fileLength = swapEndian32(readInt32()) * 2;

    // Read version (little endian)
    _header.version = readInt32();
    if (_header.version != 1000)
    {
        OE_WARN << LC << "Unsupported shapefile version: " << _header.version << std::endl;
        return false;
    }

    // Read shape type (little endian)
    _header.shapeType = readInt32();

    // Read bounding box (little endian)
    _header.xMin = readDouble();
    _header.yMin = readDouble();
    _header.xMax = readDouble();
    _header.yMax = readDouble();
    _header.zMin = readDouble();
    _header.zMax = readDouble();
    _header.mMin = readDouble();
    _header.mMax = readDouble();

    _currentPos = 100; // Header is 100 bytes
    return true;
}

bool ShapefileReader::readFeatures(std::vector<osg::ref_ptr<Feature>> &features)
{
    if (!_isOpen)
        return false;

    _file.seekg(100, std::ios::beg); // Skip header
    _currentPos = 100;

    while (_currentPos < _header.fileLength)
    {
        osg::ref_ptr<Feature> feature;
        if (readRecord(feature))
        {
            if (feature.valid())
            {
                features.push_back(feature);
            }
        }
        else
        {
            break;
        }
    }

    return true;
}

bool ShapefileReader::readRecord(osg::ref_ptr<Feature> &feature)
{
    if (!_file.is_open() || _currentPos >= _header.fileLength)
        return false;

    _file.seekg(_currentPos, std::ios::beg);

    // Read record header
    RecordHeader recordHeader;
    recordHeader.recordNumber = swapEndian32(readInt32());
    recordHeader.contentLength = swapEndian32(readInt32()) * 2; // Convert from 16-bit words to bytes

    if (_file.eof())
        return false;

    // Read shape type
    int shapeType = readInt32();

    Geometry *geometry = nullptr;

    switch (shapeType)
    {
    case SHAPE_NULL:
        // Null shape - create empty feature
        feature = new Feature(nullptr, SpatialReference::create("wgs84"));
        break;

    case SHAPE_POINT:
        geometry = readPointGeometry();
        break;

    case SHAPE_POLYLINE:
        geometry = readPolylineGeometry();
        break;

    case SHAPE_POLYGON:
        geometry = readPolygonGeometry();
        break;

    default:
        OE_WARN << LC << "Unsupported shape type: " << shapeType << std::endl;
        // Skip this record
        _currentPos += 8 + recordHeader.contentLength;
        return true;
    }

    if (geometry || shapeType == SHAPE_NULL)
    {
        feature = new Feature(geometry, SpatialReference::create("wgs84"));
        feature->setFID(recordHeader.recordNumber);
    }

    _currentPos += 8 + recordHeader.contentLength;
    return true;
}

Geometry *
ShapefileReader::readPointGeometry()
{
    double x = readDouble();
    double y = readDouble();

    Point *point = new Point();
    point->set(osg::Vec3d(x, y, 0.0));
    return point;
}

Geometry *
ShapefileReader::readPolylineGeometry()
{
    // Read bounding box (skip)
    readDouble();
    readDouble();
    readDouble();
    readDouble();

    int numParts = readInt32();
    int numPoints = readInt32();

    // Read part indices
    std::vector<int> parts(numParts);
    for (int i = 0; i < numParts; ++i)
    {
        parts[i] = readInt32();
    }

    // Read points
    std::vector<osg::Vec3d> points(numPoints);
    for (int i = 0; i < numPoints; ++i)
    {
        double x = readDouble();
        double y = readDouble();
        points[i] = osg::Vec3d(x, y, 0.0);
    }

    if (numParts == 1)
    {
        // Single linestring
        LineString *lineString = new LineString();
        for (const auto &point : points)
        {
            lineString->push_back(point);
        }
        return lineString;
    }
    else
    {
        // Multi-linestring
        MultiGeometry *multiGeom = new MultiGeometry();
        for (int i = 0; i < numParts; ++i)
        {
            int startIdx = parts[i];
            int endIdx = (i + 1 < numParts) ? parts[i + 1] : numPoints;

            LineString *lineString = new LineString();
            for (int j = startIdx; j < endIdx; ++j)
            {
                lineString->push_back(points[j]);
            }
            multiGeom->add(lineString);
        }
        return multiGeom;
    }
}

Geometry *
ShapefileReader::readPolygonGeometry()
{
    // Read bounding box (skip)
    readDouble();
    readDouble();
    readDouble();
    readDouble();

    int numParts = readInt32();
    int numPoints = readInt32();

    // Read part indices
    std::vector<int> parts(numParts);
    for (int i = 0; i < numParts; ++i)
    {
        parts[i] = readInt32();
    }

    // Read points
    std::vector<osg::Vec3d> points(numPoints);
    for (int i = 0; i < numPoints; ++i)
    {
        double x = readDouble();
        double y = readDouble();
        points[i] = osg::Vec3d(x, y, 0.0);
    }

    if (numParts == 1)
    {
        // Single polygon
        Polygon *polygon = new Polygon();
        int startIdx = parts[0];
        int endIdx = numPoints;

        // Copy points directly to polygon (which is a Ring)
        for (int j = startIdx; j < endIdx; ++j)
        {
            polygon->push_back(points[j]);
        }
        return polygon;
    }
    else
    {
        // Multi-polygon or polygon with holes
        Polygon *polygon = new Polygon();

        for (int i = 0; i < numParts; ++i)
        {
            int startIdx = parts[i];
            int endIdx = (i + 1 < numParts) ? parts[i + 1] : numPoints;

            if (i == 0)
            {
                // First part is the exterior ring (copy to polygon directly)
                for (int j = startIdx; j < endIdx; ++j)
                {
                    polygon->push_back(points[j]);
                }
            }
            else
            {
                // Additional parts are holes
                Ring *hole = new Ring();
                for (int j = startIdx; j < endIdx; ++j)
                {
                    hole->push_back(points[j]);
                }
                polygon->getHoles().push_back(hole);
            }
        }
        return polygon;
    }
}

void ShapefileReader::getBounds(double &xMin, double &yMin, double &xMax, double &yMax) const
{
    xMin = _header.xMin;
    yMin = _header.yMin;
    xMax = _header.xMax;
    yMax = _header.yMax;
}

// Utility functions
int ShapefileReader::readInt32()
{
    int value;
    _file.read(reinterpret_cast<char *>(&value), sizeof(value));
    return value;
}

double
ShapefileReader::readDouble()
{
    double value;
    _file.read(reinterpret_cast<char *>(&value), sizeof(value));
    return value;
}

void ShapefileReader::readBytes(char *buffer, int count)
{
    _file.read(buffer, count);
}

bool ShapefileReader::seekTo(std::streampos pos)
{
    _file.seekg(pos, std::ios::beg);
    return !_file.fail();
}

int ShapefileReader::swapEndian32(int value)
{
    return ((value & 0xFF000000) >> 24) |
           ((value & 0x00FF0000) >> 8) |
           ((value & 0x0000FF00) << 8) |
           ((value & 0x000000FF) << 24);
}

double
ShapefileReader::swapEndianDouble(double value)
{
    // For simplicity, assume little endian system
    // In a real implementation, you'd need proper endian handling
    return value;
}
