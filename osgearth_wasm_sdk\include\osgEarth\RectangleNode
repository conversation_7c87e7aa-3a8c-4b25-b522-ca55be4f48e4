/* osgEarth
* Copyright 2025 Pelican Mapping
* MIT License
*/

#ifndef OSGEARTH_ANNOTATION_RECTANGLE_NODE_H
#define OSGEARTH_ANNOTATION_RECTANGLE_NODE_H 1

#include <osgEarth/LocalGeometryNode>
#include <osgEarth/Style>
#include <osgEarth/MapNode>
#include <osgEarth/Units>

namespace osgEarth
{
    /**
     * Rectangle annotation.
     */
    class OSGEARTH_EXPORT RectangleNode : public LocalGeometryNode
    {
    public:
        enum Corner
        {
            CORNER_LOWER_LEFT,
            CORNER_LOWER_RIGHT,
            CORNER_UPPER_LEFT,
            CORNER_UPPER_RIGHT
        };

        META_AnnotationNode(osgEarth, RectangleNode );

        RectangleNode();

        /**
         * Constructs a new rectangle annotation.
         *
         * @param position    Location of the annotation, in map coordinates
         * @param width       Rectangle width
         * @param height      Rectangle height
         * @param style       Style defining how the annotation will look
         * @param draped      Whether to "drape" the annotation down on to the terrain         
         */
        RectangleNode(
            const GeoPoint&   position,
            const Linear&     width,
            const Linear&     height,
            const Style&      style );

        /**
         * Gets the width of this rectangle
         */
        const Linear& getWidth() const;

        /**
         * Gets the height of this rectangle
         */
        const Linear& getHeight() const;

        /**
         * Sets the width of this rectangle
         * @param width The width of the rectangle
         */
        void setWidth( const Linear& width );

        /**
         * Sets the height of this rectangle
         * @param height   The height of the rectangle
         */
        void setHeight( const Linear& height );

        /**
         * Sets the size of the rectangle
         * @param width         The width of the rectangle
         * @param height        The height of the rectangle
         */
        void setSize( const Linear& width, const Linear& height);

        /**
         * Gets the style
         */
        const Style& getStyle() const;

        /**
         * Sets the style
         */
        void setStyle( const Style& style );        

        GeoPoint getUpperLeft() const;
        void setUpperLeft( const GeoPoint& upperLeft );

        GeoPoint getUpperRight() const;
        void setUpperRight( const GeoPoint& upperRight );

        GeoPoint getLowerLeft() const;
        void setLowerLeft( const GeoPoint& lowerLeft );

        GeoPoint getLowerRight() const;
        void setLowerRight( const GeoPoint& lowerRight );

        GeoPoint getCorner( Corner corner ) const;
        void setCorner( Corner corner, const GeoPoint& location);

    public:

        RectangleNode(const Config& conf, const osgDB::Options* options);
        virtual Config getConfig() const;

    protected:        

        virtual ~RectangleNode() { }

    private:
        RectangleNode(const RectangleNode& rhs, const osg::CopyOp& op) { }

        void construct();
        void buildGeometry();

        Style  _style;
        Linear _width;
        Linear _height;
    };

}

#endif // OSGEARTH_ANNOTATION_RECTANGLE_NODE_H
