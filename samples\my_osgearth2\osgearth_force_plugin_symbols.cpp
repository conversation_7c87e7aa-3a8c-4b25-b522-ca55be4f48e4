// osgEarth 强制插件符号引用版本
// 通过强制引用插件中的符号来确保插件被正确链接和注册

#ifdef EMSCRIPTEN
#define _LIBCPP_HAS_NO_PRAGMA_SYSTEM_HEADER
#define _LIBCPP_DISABLE_AVAILABILITY
#endif

#ifdef _WIN32
#define _USE_MATH_DEFINES
#define NOMINMAX
#endif

#include <iostream>
#include <memory>
#include <string>

// OSG核心头文件
#include <osg/ref_ptr>
#include <osg/Group>
#include <osg/Geode>
#include <osg/ShapeDrawable>
#include <osg/Shape>
#include <osg/StateSet>
#include <osg/Material>
#include <osgDB/Registry>

// OSG Viewer
#include <osgViewer/Viewer>
#include <osgViewer/ViewerEventHandlers>
#include <osgGA/TrackballManipulator>
#include <osgGA/StateSetManipulator>

// osgEarth 头文件
#include <osgEarth/Registry>
#include <osgEarth/TerrainEngineNode>
#include <osgEarth/TerrainOptions>
#include <osgEarth/Map>
#include <osgEarth/MapNode>

// 平台相关头文件
#ifdef EMSCRIPTEN
#include <SDL2/SDL.h>
#include <emscripten.h>
#include <emscripten/html5.h>
#else
#include <SDL.h>
#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#endif
#endif

#define DEBUG_LOG(msg) std::cout << "[DEBUG] " << msg << std::endl
#define ERROR_LOG(msg) std::cerr << "[ERROR] " << msg << std::endl
#define INFO_LOG(msg) std::cout << "[INFO] " << msg << std::endl

// 强制引用插件符号
#ifdef EMSCRIPTEN
// 在 WebAssembly 环境中，我们需要强制引用插件中的符号
// 这样可以确保链接器不会优化掉插件代码

// 声明插件中的关键符号（这些符号名称可能需要调整）
extern "C" {
    // 尝试引用 REX 地形引擎插件中的符号
    // 这些符号名称是推测的，可能需要根据实际情况调整
    void* osgdb_osgearth_engine_rex_readerwriter();
    void* osgdb_osgearth_engine_rex_proxy();
}

// 强制引用函数
void forcePluginSymbolReferences()
{
    INFO_LOG("=== 强制引用插件符号 ===");
    
    try
    {
        // 强制引用插件符号，但不实际调用
        volatile void* ptr1 = (void*)&osgdb_osgearth_engine_rex_readerwriter;
        volatile void* ptr2 = (void*)&osgdb_osgearth_engine_rex_proxy;
        
        INFO_LOG("强制引用插件符号完成");
        INFO_LOG("  REX ReaderWriter 符号地址: " << ptr1);
        INFO_LOG("  REX Proxy 符号地址: " << ptr2);
    }
    catch (...)
    {
        ERROR_LOG("强制引用插件符号时发生异常");
    }
}
#else
void forcePluginSymbolReferences()
{
    INFO_LOG("桌面环境 - 跳过符号引用");
}
#endif

// 全局变量
struct AppContext
{
    osg::ref_ptr<osgViewer::Viewer> viewer;
    osg::ref_ptr<osg::Group> rootNode;
    osg::ref_ptr<osgEarth::MapNode> mapNode;
    
    SDL_Window *window;
    SDL_GLContext context;
    bool shouldExit;
    
    AppContext() : shouldExit(false) {}
};

static AppContext* g_appContext = nullptr;

/**
 * 创建简单的 OSG 球体
 */
osg::ref_ptr<osg::Node> createSimpleEarth()
{
    DEBUG_LOG("Creating simple OSG sphere...");
    
    osg::ref_ptr<osg::Geode> geode = new osg::Geode();
    osg::ref_ptr<osg::ShapeDrawable> sphere = new osg::ShapeDrawable(new osg::Sphere(osg::Vec3(0, 0, 0), 6378137.0f));
    
    // 设置材质
    osg::ref_ptr<osg::StateSet> stateSet = sphere->getOrCreateStateSet();
    osg::ref_ptr<osg::Material> material = new osg::Material();
    material->setDiffuse(osg::Material::FRONT_AND_BACK, osg::Vec4(0.2f, 0.5f, 0.8f, 1.0f));
    stateSet->setAttributeAndModes(material.get());
    
    geode->addDrawable(sphere.get());
    
    DEBUG_LOG("Simple OSG sphere created successfully");
    return geode.get();
}

/**
 * 测试插件注册状态
 */
void testPluginRegistration()
{
    INFO_LOG("=== 测试插件注册状态 ===");
    
    osgDB::Registry* registry = osgDB::Registry::instance();
    
    // 检查 REX 地形引擎插件
    INFO_LOG("检查 REX 地形引擎插件...");
    auto rw_rex = registry->getReaderWriterForExtension("osgearth_engine_rex");
    if (rw_rex)
    {
        INFO_LOG("✅ REX 地形引擎插件已注册: " << rw_rex->className());
    }
    else
    {
        ERROR_LOG("❌ REX 地形引擎插件未注册");
    }
    
    // 列出所有已注册的 ReaderWriter
    INFO_LOG("已注册的 ReaderWriter 插件:");
    auto& rwList = registry->getReaderWriterList();
    for (auto& rw : rwList)
    {
        INFO_LOG("  - " << rw->className());
    }
}

/**
 * 测试地形引擎创建
 */
bool testTerrainEngineCreation()
{
    INFO_LOG("=== 测试地形引擎创建 ===");
    
    try
    {
        // 创建默认的地形选项
        osgEarth::TerrainOptions options;
        INFO_LOG("地形选项创建成功");
        
        // 尝试创建地形引擎
        INFO_LOG("尝试创建地形引擎...");
        osg::ref_ptr<osgEarth::TerrainEngineNode> terrainEngine = osgEarth::TerrainEngineNode::create(options);
        
        if (terrainEngine.valid())
        {
            INFO_LOG("✅ 地形引擎创建成功");
            return true;
        }
        else
        {
            ERROR_LOG("❌ 地形引擎创建失败");
            return false;
        }
    }
    catch (const std::exception& e)
    {
        ERROR_LOG("❌ TerrainEngineNode 创建异常: " << e.what());
        return false;
    }
}

/**
 * 创建 osgEarth 地图
 */
osg::ref_ptr<osg::Node> createOsgEarthMap()
{
    INFO_LOG("=== 创建 osgEarth 地图 ===");
    
    try
    {
        // 创建地图
        osg::ref_ptr<osgEarth::Map> map = new osgEarth::Map();
        INFO_LOG("地图创建成功");
        
        // 创建地图节点
        INFO_LOG("尝试创建 MapNode...");
        osg::ref_ptr<osgEarth::MapNode> mapNode = new osgEarth::MapNode(map.get());
        
        if (mapNode.valid())
        {
            INFO_LOG("✅ MapNode 创建成功");
            
            // 检查地形引擎
            auto terrainEngine = mapNode->getTerrainEngine();
            if (terrainEngine)
            {
                INFO_LOG("✅ 地形引擎已附加");
                return mapNode.get();
            }
            else
            {
                ERROR_LOG("❌ 地形引擎未附加，使用简单球体作为后备");
                return createSimpleEarth();
            }
        }
        else
        {
            ERROR_LOG("❌ MapNode 创建失败，使用简单球体作为后备");
            return createSimpleEarth();
        }
    }
    catch (const std::exception& e)
    {
        ERROR_LOG("❌ osgEarth 地图创建异常: " << e.what());
        return createSimpleEarth();
    }
}

/**
 * 初始化SDL
 */
bool initializeSDL()
{
    DEBUG_LOG("Initializing SDL...");
    
    if (SDL_Init(SDL_INIT_VIDEO) < 0)
    {
        ERROR_LOG("SDL initialization failed: " << SDL_GetError());
        return false;
    }

    // 设置OpenGL属性
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 3);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 0);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_PROFILE_MASK, SDL_GL_CONTEXT_PROFILE_ES);
    SDL_GL_SetAttribute(SDL_GL_DOUBLEBUFFER, 1);
    SDL_GL_SetAttribute(SDL_GL_DEPTH_SIZE, 24);
    SDL_GL_SetAttribute(SDL_GL_STENCIL_SIZE, 8);

    // 创建窗口
    g_appContext->window = SDL_CreateWindow(
        "osgEarth Force Plugin Symbols Test",
        SDL_WINDOWPOS_CENTERED, SDL_WINDOWPOS_CENTERED,
        800, 600,
        SDL_WINDOW_OPENGL | SDL_WINDOW_SHOWN
    );

    if (!g_appContext->window)
    {
        ERROR_LOG("Window creation failed: " << SDL_GetError());
        return false;
    }

    // 创建OpenGL上下文
    g_appContext->context = SDL_GL_CreateContext(g_appContext->window);
    if (!g_appContext->context)
    {
        ERROR_LOG("OpenGL context creation failed: " << SDL_GetError());
        return false;
    }

    SDL_GL_MakeCurrent(g_appContext->window, g_appContext->context);
    SDL_GL_SetSwapInterval(1);

    DEBUG_LOG("SDL initialized successfully");
    return true;
}

/**
 * 初始化OSG
 */
bool initializeOSG()
{
    DEBUG_LOG("Initializing OSG...");
    
    // 创建viewer
    g_appContext->viewer = new osgViewer::Viewer();

    // 设置为嵌入式窗口
    int x, y, width, height;
    SDL_GetWindowPosition(g_appContext->window, &x, &y);
    SDL_GetWindowSize(g_appContext->window, &width, &height);

#ifdef EMSCRIPTEN
    // WebAssembly版本 - 使用与成功的 OSG 示例相同的设置
    g_appContext->viewer->setUpViewerAsEmbeddedInWindow(0, 0, width, height);
    g_appContext->viewer->setThreadingModel(osgViewer::Viewer::SingleThreaded);

    // WebGL兼容性设置
    osg::ref_ptr<osg::StateSet> globalStateSet = g_appContext->viewer->getCamera()->getOrCreateStateSet();
    globalStateSet->setMode(GL_ALPHA_TEST, osg::StateAttribute::OFF);
    globalStateSet->setMode(GL_CULL_FACE, osg::StateAttribute::ON);
    globalStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);

    // 强制使用VBO
    osg::DisplaySettings::instance()->setVertexBufferHint(osg::DisplaySettings::VERTEX_BUFFER_OBJECT);

    // 设置清除颜色
    g_appContext->viewer->getCamera()->setClearColor(osg::Vec4(0.1f, 0.1f, 0.3f, 1.0f));
#else
    // 桌面版本设置
    osg::ref_ptr<osg::GraphicsContext::Traits> traits = new osg::GraphicsContext::Traits();
    traits->x = x;
    traits->y = y;
    traits->width = width;
    traits->height = height;
    traits->windowDecoration = false;
    traits->doubleBuffer = true;
    traits->sharedContext = 0;

    osg::ref_ptr<osg::GraphicsContext> gc = osg::GraphicsContext::createGraphicsContext(traits.get());
    g_appContext->viewer->getCamera()->setGraphicsContext(gc.get());
#endif

    // 设置相机参数
    g_appContext->viewer->getCamera()->setViewport(new osg::Viewport(0, 0, width, height));
    g_appContext->viewer->getCamera()->setProjectionMatrixAsPerspective(30.0, (double)width / height, 1.0, 1000000000.0);

    // 添加操作器
    g_appContext->viewer->setCameraManipulator(new osgGA::TrackballManipulator());

    // 添加事件处理器
    g_appContext->viewer->addEventHandler(new osgGA::StateSetManipulator(g_appContext->viewer->getCamera()->getOrCreateStateSet()));
    g_appContext->viewer->addEventHandler(new osgViewer::StatsHandler());
    g_appContext->viewer->addEventHandler(new osgViewer::WindowSizeHandler());

    DEBUG_LOG("OSG initialized successfully");
    return true;
}

/**
 * 主循环
 */
void mainLoop()
{
    // 处理SDL事件
    SDL_Event event;
    while (SDL_PollEvent(&event))
    {
        if (event.type == SDL_QUIT)
        {
            g_appContext->shouldExit = true;
        }
    }

    // 渲染OSG场景
    if (g_appContext->viewer.valid())
    {
        g_appContext->viewer->frame();
    }

#ifndef EMSCRIPTEN
    // 桌面版本需要交换缓冲区
    SDL_GL_SwapWindow(g_appContext->window);
#endif
}

/**
 * 主函数
 */
int main(int argc, char *argv[])
{
#ifndef EMSCRIPTEN
    // 对于桌面版本，分配控制台用于调试输出
    if (AllocConsole())
    {
        freopen_s((FILE **)stdout, "CONOUT$", "w", stdout);
        freopen_s((FILE **)stderr, "CONOUT$", "w", stderr);
        freopen_s((FILE **)stdin, "CONIN$", "r", stdin);
        SetConsoleTitle("osgEarth Force Plugin Symbols Test - Debug Output");
    }
#endif

    std::cout << "========================================" << std::endl;
    std::cout << "osgEarth 强制插件符号引用测试" << std::endl;
    std::cout << "========================================" << std::endl;

    // 创建应用上下文
    g_appContext = new AppContext();

    // 1. 强制引用插件符号
    forcePluginSymbolReferences();
    
    std::cout << std::endl;

    // 2. 测试插件注册状态
    testPluginRegistration();
    
    std::cout << std::endl;

    // 3. 初始化SDL
    if (!initializeSDL())
    {
        delete g_appContext;
        return -1;
    }

    // 4. 初始化OSG
    if (!initializeOSG())
    {
        delete g_appContext;
        return -1;
    }

    // 5. 测试地形引擎创建
    bool terrainEngineWorking = testTerrainEngineCreation();
    
    std::cout << std::endl;

    // 6. 创建根节点
    g_appContext->rootNode = new osg::Group();

    // 7. 创建场景
    osg::ref_ptr<osg::Node> sceneNode;
    if (terrainEngineWorking)
    {
        INFO_LOG("地形引擎工作正常，创建 osgEarth 地图");
        sceneNode = createOsgEarthMap();
    }
    else
    {
        INFO_LOG("地形引擎不工作，使用简单球体");
        sceneNode = createSimpleEarth();
    }

    if (sceneNode.valid())
    {
        g_appContext->rootNode->addChild(sceneNode.get());
    }

    // 8. 设置场景
    g_appContext->viewer->setSceneData(g_appContext->rootNode.get());

    INFO_LOG("Starting main loop...");

    // 9. 启动主循环
#ifdef EMSCRIPTEN
    emscripten_set_main_loop(mainLoop, 60, 1);
#else
    while (!g_appContext->shouldExit)
    {
        mainLoop();
        std::this_thread::sleep_for(std::chrono::milliseconds(16)); // ~60 FPS
    }
#endif

    // 清理资源
    INFO_LOG("Cleaning up...");
    delete g_appContext;
    SDL_Quit();

    return 0;
}
