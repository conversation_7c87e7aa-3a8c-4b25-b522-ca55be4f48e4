/**
 * 测试XYZ图层集成到WebGL渲染引擎
 */

#include <iostream>
#include <memory>

// osgEarth 核心头文件
#include <osgEarth/Map>
#include <osgEarth/MapNode>
#include <osgEarth/XYZ>
#include <osgEarth/Registry>

// WebAssembly图像解码支持
#include "STBImageDecoder.h"
#include "WebAssemblyHTTPClient.h"

// OSG 核心头文件
#include <osg/Group>
#include <osg/Matrix>
#include <osg/Material>
#include <osgDB/Registry>

// SDL 和平台相关
#ifdef EMSCRIPTEN
#include <SDL2/SDL.h>
#include <emscripten.h>
#include <emscripten/html5.h>
#else
#include <SDL.h>
#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#endif
#endif

using namespace osgEarth;

// 应用程序上下文
struct AppContext
{
    osg::ref_ptr<osg::Group> rootNode;
    osg::ref_ptr<osgEarth::MapNode> mapNode;
    osg::ref_ptr<osgEarth::XYZImageLayer> xyzLayer;

    SDL_Window *window = nullptr;
    SDL_GLContext context = nullptr;
    bool shouldExit = false;
};

static AppContext *g_appContext = nullptr;

// 鼠标交互状态
struct MouseState
{
    bool leftButtonDown = false;
    bool rightButtonDown = false;
    int lastX = 0, lastY = 0;
    float cameraDistance = 3.0f;
    float cameraRotationX = 0.0f;
    float cameraRotationY = 0.0f;
} g_mouseState;

// 日志宏
#define INFO_LOG(msg) std::cout << "[INFO] " << msg << std::endl
#define ERROR_LOG(msg) std::cerr << "[ERROR] " << msg << std::endl

/**
 * 初始化 SDL 和 OpenGL 上下文
 */
bool initializeSDL()
{
    if (SDL_Init(SDL_INIT_VIDEO) < 0)
    {
        ERROR_LOG("SDL 初始化失败: " << SDL_GetError());
        return false;
    }

    // 设置 OpenGL 属性
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 3);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 0);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_PROFILE_MASK, SDL_GL_CONTEXT_PROFILE_ES);
    SDL_GL_SetAttribute(SDL_GL_DOUBLEBUFFER, 1);
    SDL_GL_SetAttribute(SDL_GL_DEPTH_SIZE, 24);

    // 创建窗口
    g_appContext->window = SDL_CreateWindow(
        "osgEarth XYZ Integration Test",
        SDL_WINDOWPOS_CENTERED, SDL_WINDOWPOS_CENTERED,
        800, 600,
        SDL_WINDOW_OPENGL | SDL_WINDOW_SHOWN);

    if (!g_appContext->window)
    {
        ERROR_LOG("窗口创建失败: " << SDL_GetError());
        return false;
    }

    // 创建 OpenGL 上下文
    g_appContext->context = SDL_GL_CreateContext(g_appContext->window);
    if (!g_appContext->context)
    {
        ERROR_LOG("OpenGL 上下文创建失败: " << SDL_GetError());
        return false;
    }

    SDL_GL_SetSwapInterval(1); // 启用垂直同步
    return true;
}

/**
 * 创建带有XYZ图层的osgEarth场景
 */
bool createXYZScene()
{
    try
    {
        // 创建地图
        osg::ref_ptr<osgEarth::Map> map = new osgEarth::Map();
        if (!map.valid())
        {
            ERROR_LOG("地图创建失败");
            return false;
        }

        // 创建谷歌地图XYZ瓦片图层
        osgEarth::XYZImageLayer::Options xyzOptions;
        xyzOptions.url() = osgEarth::URI("https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}");
        xyzOptions.name() = "Google Satellite";
        xyzOptions.minLevel() = 0;
        xyzOptions.maxLevel() = 18;

        g_appContext->xyzLayer = new osgEarth::XYZImageLayer(xyzOptions);
        if (!g_appContext->xyzLayer.valid())
        {
            ERROR_LOG("XYZ图层创建失败");
            return false;
        }

        // 设置球面墨卡托投影
        g_appContext->xyzLayer->setProfile(osgEarth::Profile::create(osgEarth::Profile::SPHERICAL_MERCATOR));

        // 添加图层到地图
        map->addLayer(g_appContext->xyzLayer.get());

        // 设置地形选项（WebGL 兼容）
        osgEarth::TerrainOptions terrainOptions;
#ifdef EMSCRIPTEN
        terrainOptions.enableBlending() = false;
        terrainOptions.gpuTessellation() = false;
        terrainOptions.morphImagery() = false;
        terrainOptions.morphTerrain() = false;
        terrainOptions.enableLighting() = false;
        terrainOptions.castShadows() = false;
        terrainOptions.normalizeEdges() = false;
        terrainOptions.maxLOD() = 18;
#endif

        // 创建 MapNode
        g_appContext->mapNode = new osgEarth::MapNode(map.get(), terrainOptions);
        if (!g_appContext->mapNode.valid())
        {
            ERROR_LOG("MapNode 创建失败");
            return false;
        }

        // 创建根节点
        g_appContext->rootNode = new osg::Group();
        g_appContext->rootNode->addChild(g_appContext->mapNode.get());

        INFO_LOG("✅ XYZ图层场景创建成功");
        return true;
    }
    catch (const std::exception &e)
    {
        ERROR_LOG("XYZ场景创建异常: " << e.what());
        return false;
    }
}

/**
 * 测试XYZ图层功能
 */
void testXYZLayer()
{
    if (!g_appContext->xyzLayer.valid())
    {
        ERROR_LOG("XYZ图层无效");
        return;
    }

    try
    {
        // 测试图层状态
        INFO_LOG("XYZ图层名称: " << g_appContext->xyzLayer->getName());
        INFO_LOG("XYZ图层URL: " << g_appContext->xyzLayer->options().url().get().full());

        // 测试Profile
        const osgEarth::Profile *profile = g_appContext->xyzLayer->getProfile();
        if (profile)
        {
            INFO_LOG("XYZ图层Profile: " << profile->toString());
        }

        // 测试创建一个瓦片图像
        osgEarth::TileKey testKey(1, 0, 0, profile);
        INFO_LOG("测试瓦片键: " << testKey.str());

        // 测试WebAssembly图像加载器
        std::string testUrl = "https://mt1.google.com/vt/lyrs=s&x=0&y=0&z=1";
        INFO_LOG("测试WebAssembly图像下载: " << testUrl);

        osgEarth::WebAssemblyHTTPClient::downloadImageAsync(testUrl,
                                                            [](osg::ref_ptr<osg::Image> image, const std::string &error)
                                                            {
                                                                if (image.valid())
                                                                {
                                                                    INFO_LOG("✅ WebAssembly成功下载并解码图像: " << image->s() << "x" << image->t()
                                                                                                                  << " 通道数: " << image->getPixelFormat());
                                                                }
                                                                else
                                                                {
                                                                    INFO_LOG("❌ WebAssembly图像下载失败: " << error);
                                                                }
                                                            });

        // 尝试创建图像（这可能需要网络请求）
        INFO_LOG("测试osgEarth XYZ图层图像创建...");
        osgEarth::GeoImage geoImage = g_appContext->xyzLayer->createImageImplementation(testKey, nullptr);
        if (geoImage.valid())
        {
            const osg::Image *image = geoImage.getImage();
            if (image)
            {
                INFO_LOG("✅ osgEarth成功创建瓦片图像: " << image->s() << "x" << image->t());
            }
            else
            {
                INFO_LOG("⚠️ osgEarth瓦片图像为空");
            }
        }
        else
        {
            INFO_LOG("⚠️ osgEarth无法创建瓦片图像，可能需要STB_Image解码器支持");
        }
    }
    catch (const std::exception &e)
    {
        ERROR_LOG("XYZ图层测试异常: " << e.what());
    }
}

/**
 * 处理鼠标事件
 */
void handleMouseEvent(SDL_Event &event)
{
    switch (event.type)
    {
    case SDL_MOUSEBUTTONDOWN:
        if (event.button.button == SDL_BUTTON_LEFT)
        {
            g_mouseState.leftButtonDown = true;
            g_mouseState.lastX = event.button.x;
            g_mouseState.lastY = event.button.y;
        }
        break;

    case SDL_MOUSEBUTTONUP:
        if (event.button.button == SDL_BUTTON_LEFT)
            g_mouseState.leftButtonDown = false;
        break;

    case SDL_MOUSEMOTION:
        if (g_mouseState.leftButtonDown)
        {
            int deltaX = event.motion.x - g_mouseState.lastX;
            int deltaY = event.motion.y - g_mouseState.lastY;

            g_mouseState.cameraRotationY += deltaX * 0.005f;
            g_mouseState.cameraRotationX -= deltaY * 0.005f;
            g_mouseState.cameraRotationX = std::max(-1.4f, std::min(1.4f, g_mouseState.cameraRotationX));
        }

        g_mouseState.lastX = event.motion.x;
        g_mouseState.lastY = event.motion.y;
        break;

    case SDL_MOUSEWHEEL:
        g_mouseState.cameraDistance -= event.wheel.y * 0.3f;
        g_mouseState.cameraDistance = std::max(1.2f, std::min(15.0f, g_mouseState.cameraDistance));
        break;
    }
}

/**
 * 主循环
 */
void mainLoop()
{
    // 处理事件
    SDL_Event event;
    while (SDL_PollEvent(&event))
    {
        if (event.type == SDL_QUIT)
        {
            g_appContext->shouldExit = true;
        }
        else
        {
            handleMouseEvent(event);
        }
    }

    // 简单的清屏渲染
    glClearColor(0.2f, 0.3f, 0.4f, 1.0f);
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

    SDL_GL_SwapWindow(g_appContext->window);
}

/**
 * 主函数
 */
int main()
{
#ifndef EMSCRIPTEN
    // 桌面版本控制台分配
    if (AllocConsole())
    {
        freopen_s((FILE **)stdout, "CONOUT$", "w", stdout);
        freopen_s((FILE **)stderr, "CONOUT$", "w", stderr);
        freopen_s((FILE **)stdin, "CONIN$", "r", stdin);
        SetConsoleTitle("osgEarth XYZ Integration Test - Debug Output");
    }
#endif

    INFO_LOG("osgEarth XYZ图层集成测试启动中...");

    // 创建应用上下文
    g_appContext = new AppContext();

    // 注册WebAssembly图像解码器
    osgEarth::registerWebAssemblyHTTPClient();

    // 初始化 osgEarth 和 SDL
    // osgEarth::initialize(); // 在WebAssembly中可能不需要显式初始化
    if (!initializeSDL())
    {
        delete g_appContext;
        return -1;
    }

    // 创建XYZ场景
    if (!createXYZScene())
    {
        ERROR_LOG("XYZ场景创建失败");
        delete g_appContext;
        return -1;
    }

    // 测试XYZ图层功能
    testXYZLayer();

    // 启动主循环
#ifdef EMSCRIPTEN
    emscripten_set_main_loop(mainLoop, 60, 1);
#else
    while (!g_appContext->shouldExit)
    {
        mainLoop();
        std::this_thread::sleep_for(std::chrono::milliseconds(16));
    }
#endif

    // 清理资源
    if (g_appContext->context)
        SDL_GL_DeleteContext(g_appContext->context);
    if (g_appContext->window)
        SDL_DestroyWindow(g_appContext->window);
    delete g_appContext;

    INFO_LOG("XYZ图层集成测试完成");
    return 0;
}
