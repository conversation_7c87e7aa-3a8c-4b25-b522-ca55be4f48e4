/**
 * STB_Image解码器 - 用于WebAssembly环境的图像解码
 * 替代外部图像库（libjpeg、libpng等）
 */

#ifndef STBIMAGE_DECODER_H
#define STBIMAGE_DECODER_H

#include <osg/Image>
#include <osgDB/ReaderWriter>
#include <osgDB/Registry>
#include <iostream>
#include <vector>

#ifdef EMSCRIPTEN
// 定义STB_Image实现
#define STB_IMAGE_IMPLEMENTATION
#define STBI_NO_STDIO
#define STBI_NO_FAILURE_STRINGS
#define STBI_ONLY_PNG
#define STBI_ONLY_JPEG
#define STBI_MALLOC(sz)           malloc(sz)
#define STBI_REALLOC(p,newsz)     realloc(p,newsz)
#define STBI_FREE(p)              free(p)

// 包含STB_Image头文件
#include "../../src/third_party/tinygltf/stb_image.h"
#endif

namespace osgEarth
{

/**
 * STB_Image解码器类
 */
class STBImageDecoder : public osgDB::ReaderWriter
{
public:
    STBImageDecoder()
    {
        supportsExtension("png", "PNG image format via STB_Image");
        supportsExtension("jpg", "JPEG image format via STB_Image");
        supportsExtension("jpeg", "JPEG image format via STB_Image");
    }

    virtual const char* className() const override
    {
        return "STB_Image Decoder for WebAssembly";
    }

    virtual ReadResult readImage(std::istream& fin, const osgDB::Options* options = nullptr) const override
    {
#ifdef EMSCRIPTEN
        // 读取流数据到内存
        fin.seekg(0, std::ios::end);
        size_t size = fin.tellg();
        fin.seekg(0, std::ios::beg);

        if (size == 0)
        {
            return ReadResult("Empty image stream");
        }

        std::vector<unsigned char> buffer(size);
        fin.read(reinterpret_cast<char*>(buffer.data()), size);

        if (fin.gcount() != static_cast<std::streamsize>(size))
        {
            return ReadResult("Failed to read image stream");
        }

        return decodeImage(buffer.data(), size);
#else
        return ReadResult::FILE_NOT_HANDLED;
#endif
    }

    virtual ReadResult readImage(const std::string& fileName, const osgDB::Options* options = nullptr) const override
    {
#ifdef EMSCRIPTEN
        std::string ext = osgDB::getLowerCaseFileExtension(fileName);
        if (!acceptsExtension(ext))
        {
            return ReadResult::FILE_NOT_HANDLED;
        }

        // 对于WebAssembly，文件读取通常通过HTTP完成
        // 这里主要处理内存中的数据
        return ReadResult("File reading not supported in WebAssembly, use stream reading");
#else
        return ReadResult::FILE_NOT_HANDLED;
#endif
    }

private:
    ReadResult decodeImage(const unsigned char* data, size_t size) const
    {
#ifdef EMSCRIPTEN
        int width, height, channels;
        
        // 使用STB_Image解码图像
        unsigned char* pixels = stbi_load_from_memory(
            data, static_cast<int>(size),
            &width, &height, &channels, 0);

        if (!pixels)
        {
            return ReadResult("STB_Image failed to decode image");
        }

        // 创建OSG图像对象
        osg::ref_ptr<osg::Image> image = new osg::Image();
        
        // 确定像素格式
        GLenum pixelFormat;
        GLenum internalFormat;
        
        switch (channels)
        {
        case 1:
            pixelFormat = GL_LUMINANCE;
            internalFormat = GL_LUMINANCE;
            break;
        case 2:
            pixelFormat = GL_LUMINANCE_ALPHA;
            internalFormat = GL_LUMINANCE_ALPHA;
            break;
        case 3:
            pixelFormat = GL_RGB;
            internalFormat = GL_RGB;
            break;
        case 4:
            pixelFormat = GL_RGBA;
            internalFormat = GL_RGBA;
            break;
        default:
            stbi_image_free(pixels);
            return ReadResult("Unsupported number of channels: " + std::to_string(channels));
        }

        // 计算数据大小
        size_t dataSize = width * height * channels;
        
        // 分配OSG图像数据
        unsigned char* imageData = new unsigned char[dataSize];
        memcpy(imageData, pixels, dataSize);
        
        // 释放STB_Image分配的内存
        stbi_image_free(pixels);

        // 设置图像属性
        image->setImage(
            width, height, 1,           // 宽度、高度、深度
            internalFormat,             // 内部格式
            pixelFormat,                // 像素格式
            GL_UNSIGNED_BYTE,           // 数据类型
            imageData,                  // 数据指针
            osg::Image::USE_NEW_DELETE  // 内存管理方式
        );

        // 翻转图像（STB_Image默认是上下翻转的）
        image->flipVertical();

        return ReadResult(image.release());
#else
        return ReadResult("STB_Image decoder only available in WebAssembly");
#endif
    }
};

/**
 * 注册STB_Image解码器
 */
inline void registerSTBImageDecoder()
{
#ifdef EMSCRIPTEN
    static bool registered = false;
    if (!registered)
    {
        osgDB::Registry::instance()->addReaderWriter(new STBImageDecoder());
        registered = true;
        std::cout << "[STBImageDecoder] ✅ STB_Image解码器已注册" << std::endl;
    }
#endif
}

} // namespace osgEarth

#endif // STBIMAGE_DECODER_H
