# Welcome to osgEarth!

osgEarth is a 3D mapping SDK for OpenSceneGraph.

* Get a 2D or 3D map up and running quickly and easily using one simple XML file

* Visualize massive amounts of imagery, elevation data, and vector features

* Use open-standards map data services like WMS, TMS, and GeoTIFF

* Work locally or over the Internet

* Develop applications using a full-featured C++17 API

  

Let's get started!

* [Install the SDK](install.md)
* [Build the SDK yourself](build.md)
* [Using an Earth File](earthfile.md)
* [Working with Data](data.md)
* [Layer Reference](layers.md)
* [Environment Variables](envvars.md)
* [FAQ](faq.md)
* [Release Notes](releasenotes.md)
* [Upgrade Guide: from 2.x to 3.x](3.0_upgrade_guide.md)
* [Getting Support](support.md)
* [License](license.md)



osgEarth is maintained by your friends at [Pelican Mapping](http://pelicanmapping.com).