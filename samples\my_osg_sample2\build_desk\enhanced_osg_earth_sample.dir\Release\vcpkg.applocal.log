
C:\GeminiCLI\my_osg_sample\build_desk\Release\SDL2.dll
C:\GeminiCLI\my_osg_sample\build_desk\Release\osg.dll
C:\GeminiCLI\my_osg_sample\build_desk\Release\OpenThreads.dll
C:\GeminiCLI\my_osg_sample\build_desk\Release\osgViewer.dll
C:\GeminiCLI\my_osg_sample\build_desk\Release\osgGA.dll
C:\GeminiCLI\my_osg_sample\build_desk\Release\osgDB.dll
C:\GeminiCLI\my_osg_sample\build_desk\Release\osgUtil.dll
C:\GeminiCLI\my_osg_sample\build_desk\Release\zlib1.dll
C:\GeminiCLI\my_osg_sample\build_desk\Release\osgText.dll
C:\GeminiCLI\my_osg_sample\build_desk\Release\fontconfig-1.dll
C:\GeminiCLI\my_osg_sample\build_desk\Release\freetype.dll
C:\GeminiCLI\my_osg_sample\build_desk\Release\bz2.dll
C:\GeminiCLI\my_osg_sample\build_desk\Release\libpng16.dll
C:\GeminiCLI\my_osg_sample\build_desk\Release\brotlidec.dll
C:\GeminiCLI\my_osg_sample\build_desk\Release\brotlicommon.dll
C:\GeminiCLI\my_osg_sample\build_desk\Release\libexpat.dll
