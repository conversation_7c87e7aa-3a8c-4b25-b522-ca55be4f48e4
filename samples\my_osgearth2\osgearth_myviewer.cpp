/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */

#include <osgViewer/Viewer>
#include <osgViewer/ViewerEventHandlers>
#include <osgEarth/EarthManipulator>
#include <osgEarth/ExampleResources>
#include <osgEarth/MapNode>
#include <osgEarth/PhongLightingEffect>
#include <osgEarth/Map>
#include <osgEarth/TerrainEngineNode>
#include <osgEarth/XYZ>
#include <osgEarth/TileKey>
#include <osgEarth/ImageLayer>
#include <osgEarth/GeodeticGraticule>
#include <osgEarth/Sky>
#include <osgEarth/FeatureModelLayer>
#include <osgEarth/GeosFeatureSource>
#include <osgEarth/Style>
#include <osgEarth/StyleSheet>
#include <osgEarth/LineSymbol>
#include <osgEarth/AltitudeSymbol>
#include <osgEarth/RenderSymbol>
#include <osgEarth/Stroke>
#include <osgEarth/AnnotationLayer>
#include <osgEarth/LocalGeometryNode>
#include <osgEarth/Geometry>
#include <osgEarth/HTTPClient>
#include <osgEarth/Cache>
#include <osgEarth/CachePolicy>
#include <osgGA/TrackballManipulator>
#include <osgDB/ReadFile>
#include <osgDB/FileUtils>
#include <iostream>
#include <fstream>
#include <thread>
#include <chrono>

#include <osgEarth/Metrics>
#include <osgEarth/FeatureModelLayer>
#include <osgEarth/JsonUtils>
#include <osgEarth/FeatureSource>
#include <osgEarth/StyleSheet>

// 添加天空效果相关头文件
#include <osgEarth/Sky>
#include <osgEarth/DateTime>
#include <osgEarth/Ephemeris>
#include <osgEarth/Shadowing>

#define LC "[myviewer] "

using namespace osgEarth;
using namespace osgEarth::Util;

// Function declarations
void addSimpleChinaBoundary(Map *map);

// Configure HTTP proxy and SSL settings
void configureNetworking()
{
#ifdef DEBUG_MYVIEWER
    std::cout << LC << "Configuring networking and proxy settings..." << std::endl;
#endif

    // Set proxy using environment variables (more reliable)
    _putenv("OSG_CURL_PROXY=127.0.0.1");
    _putenv("OSG_CURL_PROXYPORT=10809");
    _putenv("HTTP_PROXY=http://127.0.0.1:10809");
    _putenv("HTTPS_PROXY=http://127.0.0.1:10809");

    _putenv("FONTCONFIG_PATH=C:\\dev\\vcpkg\\installed\\x64-windows\\etc\\fonts");
    _putenv("OE_LOG_LEVEL=WARN");
    _putenv("OSG_NOTIFY_LEVEL=WARN");
    _putenv("OSGEARTH_NOTIFY_LEVEL=WARN");

    // Enable HTTP debugging to see detailed error messages
    _putenv("OSGEARTH_HTTP_DEBUG=1");

    // Also try the osgEarth API method
    ProxySettings proxy("127.0.0.1", 10809);
    HTTPClient::setProxySettings(proxy);

    // Set timeout for HTTP requests
    HTTPClient::setTimeout(30);        // 30 seconds timeout
    HTTPClient::setConnectTimeout(10); // 10 seconds connect timeout

    // Set global HTTP headers to simulate Chrome browser
    HTTPClient::setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");

#ifdef DEBUG_MYVIEWER
    std::cout << LC << "Proxy configured via environment variables and API" << std::endl;
    std::cout << LC << "HTTP timeout: 30s, connect timeout: 10s" << std::endl;
    std::cout << LC << "User-Agent set to Chrome browser simulation" << std::endl;
#endif

#ifdef DEBUG_MYVIEWER
    // Test a simple HTTP request to verify proxy with detailed logging
    std::cout << LC << "Testing network connectivity..." << std::endl;

    // Create request with Chrome-like headers
    HTTPRequest request("https://mt1.google.com/vt/lyrs=s&x=0&y=0&z=1");
    request.addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
    request.addHeader("Accept", "image/webp,image/apng,image/*,*/*;q=0.8");
    request.addHeader("Accept-Language", "en-US,en;q=0.9");
    request.addHeader("Accept-Encoding", "gzip, deflate, br");
    request.addHeader("Connection", "keep-alive");
    request.addHeader("Referer", "https://maps.google.com/");

    std::cout << LC << "Sending test request to: " << request.getURL() << std::endl;
    std::cout << LC << "Request headers configured (Chrome simulation)" << std::endl;

    HTTPResponse response = HTTPClient::get(request);
    std::cout << LC << "Response code: " << response.getCode() << std::endl;
    std::cout << LC << "Response message: " << response.getMessage() << std::endl;

    if (response.isOK())
    {
        std::cout << LC << "Network test successful - proxy is working" << std::endl;
        std::cout << LC << "Content-Type: " << response.getMimeType() << std::endl;

        // Check response size
        std::cout << LC << "Response received successfully" << std::endl;
    }
    else
    {
        std::cout << LC << "Network test failed: " << response.getCode() << " - " << response.getMessage() << std::endl;

        // Try a simpler test with HTTP instead of HTTPS
        std::cout << LC << "Trying HTTP test..." << std::endl;
        HTTPRequest httpRequest("http://www.google.com");
        HTTPResponse httpResponse = HTTPClient::get(httpRequest);
        if (httpResponse.isOK())
        {
            std::cout << LC << "HTTP test successful" << std::endl;
        }
        else
        {
            std::cout << LC << "HTTP test also failed: " << httpResponse.getCode() << " - " << httpResponse.getMessage() << std::endl;
        }
    }
#endif
}

// Setup cache directory and global cache settings
void setupCache()
{
#ifdef DEBUG_MYVIEWER
    std::cout << LC << "Setting up cache directory..." << std::endl;
#endif

    // Create cache directory if it doesn't exist
    std::string cacheDir = "cache";
    if (!osgDB::fileExists(cacheDir))
    {
        osgDB::makeDirectory(cacheDir);
#ifdef DEBUG_MYVIEWER
        std::cout << LC << "Created cache directory: " << cacheDir << std::endl;
#endif
    }
#ifdef DEBUG_MYVIEWER
    else
    {
        std::cout << LC << "Cache directory exists: " << cacheDir << std::endl;
    }
#endif

    // Create subdirectories for different tile types
    std::string googleCacheDir = cacheDir + "/google_satellite";
    std::string elevationCacheDir = cacheDir + "/aws_terrarium";

    if (!osgDB::fileExists(googleCacheDir))
    {
        osgDB::makeDirectory(googleCacheDir);
#ifdef DEBUG_MYVIEWER
        std::cout << LC << "Created Google satellite cache: " << googleCacheDir << std::endl;
#endif
    }

    if (!osgDB::fileExists(elevationCacheDir))
    {
        osgDB::makeDirectory(elevationCacheDir);
#ifdef DEBUG_MYVIEWER
        std::cout << LC << "Created elevation cache: " << elevationCacheDir << std::endl;
#endif
    }

#ifdef DEBUG_MYVIEWER
    // Configure global cache settings
    std::cout << LC << "Configured cache directories for tile storage" << std::endl;
#endif
}

#ifdef DEBUG_MYVIEWER
// Monitor tile downloads and cache status
void monitorTileDownloads()
{
    std::cout << LC << "=== Tile Download Monitoring ===" << std::endl;

    // Check cache directories for files
    std::string googleCacheDir = "cache/google_satellite";
    std::string elevationCacheDir = "cache/aws_terrarium";

    // Count files in Google cache
    int googleFiles = 0;
    if (osgDB::fileExists(googleCacheDir))
    {
        osgDB::DirectoryContents googleContents = osgDB::getDirectoryContents(googleCacheDir);
        googleFiles = googleContents.size() - 2; // Exclude . and ..
        std::cout << LC << "Google satellite cache files: " << googleFiles << std::endl;

        // List first few files for debugging
        int count = 0;
        for (const auto &file : googleContents)
        {
            if (file != "." && file != ".." && count < 5)
            {
                std::cout << LC << "  - " << file << std::endl;
                count++;
            }
        }
    }

    // Count files in elevation cache
    int elevationFiles = 0;
    if (osgDB::fileExists(elevationCacheDir))
    {
        osgDB::DirectoryContents elevationContents = osgDB::getDirectoryContents(elevationCacheDir);
        elevationFiles = elevationContents.size() - 2; // Exclude . and ..
        std::cout << LC << "Elevation cache files: " << elevationFiles << std::endl;

        // List first few files for debugging
        int count = 0;
        for (const auto &file : elevationContents)
        {
            if (file != "." && file != ".." && count < 5)
            {
                std::cout << LC << "  - " << file << std::endl;
                count++;
            }
        }
    }

    std::cout << LC << "Total cached tiles: " << (googleFiles + elevationFiles) << std::endl;
    std::cout << LC << "=================================" << std::endl;
}
#endif

// Apply downloaded tiles directly to earth surface
void applyTilesToEarth(osgEarth::MapNode *mapNode);

#ifdef DEBUG_MYVIEWER
// Force tile downloads by making direct HTTP requests
void forceTileDownloads()
{
    std::cout << LC << "=== Forcing Tile Downloads ===" << std::endl;

    // Download more tiles for better coverage
    std::vector<std::string> testURLs;

    // Google satellite tiles for zoom level 2 (better resolution)
    for (int x = 0; x < 4; ++x)
    {
        for (int y = 0; y < 4; ++y)
        {
            std::ostringstream url;
            url << "https://mt" << (x % 4) << ".google.com/vt/lyrs=s&x=" << x << "&y=" << y << "&z=2";
            testURLs.push_back(url.str());
        }
    }

    // Add some elevation tiles
    testURLs.push_back("https://s3.amazonaws.com/elevation-tiles-prod/terrarium/2/0/0.png");
    testURLs.push_back("https://s3.amazonaws.com/elevation-tiles-prod/terrarium/2/1/0.png");
    testURLs.push_back("https://s3.amazonaws.com/elevation-tiles-prod/terrarium/2/0/1.png");
    testURLs.push_back("https://s3.amazonaws.com/elevation-tiles-prod/terrarium/2/1/1.png");

    std::cout << LC << "Will download " << testURLs.size() << " tiles..." << std::endl;

    for (const auto &url : testURLs)
    {
        std::cout << LC << "Testing download: " << url << std::endl;

        HTTPRequest request(url);
        request.addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        request.addHeader("Referer", "https://maps.google.com/");

        HTTPResponse response = HTTPClient::get(request);

        if (response.isOK())
        {
            std::cout << LC << "  SUCCESS - Code: " << response.getCode() << ", Type: " << response.getMimeType() << std::endl;

            // Save to cache manually for testing
            std::string filename = url.substr(url.find_last_of('/') + 1);
            if (filename.empty())
                filename = "tile.dat";

            std::string cachePath;
            if (url.find("google.com") != std::string::npos)
            {
                cachePath = "cache/google_satellite/" + filename;
            }
            else
            {
                cachePath = "cache/aws_terrarium/" + filename;
            }

            // Try to save the response data
            std::string data = response.getPartAsString(0);
            if (!data.empty())
            {
                std::ofstream file(cachePath, std::ios::binary);
                if (file.is_open())
                {
                    file.write(data.c_str(), data.length());
                    file.close();
                    std::cout << LC << "  SAVED: " << cachePath << " (" << data.length() << " bytes)" << std::endl;
                }
                else
                {
                    std::cout << LC << "  FAILED to save: " << cachePath << std::endl;
                }
            }
            else
            {
                std::cout << LC << "  No data to save" << std::endl;
            }
        }
        else
        {
            std::cout << LC << "  FAILED - Code: " << response.getCode() << ", Message: " << response.getMessage() << std::endl;
        }
    }

    std::cout << LC << "===============================" << std::endl;
}
#endif

#ifdef DEBUG_MYVIEWER
// Test XYZ layers by manually requesting tiles
void testXYZLayers(osgEarth::MapNode *mapNode)
{
    if (!mapNode)
        return;

    std::cout << LC << "=== Testing XYZ Layer Functionality ===" << std::endl;

    auto map = mapNode->getMap();
    LayerVector layers;
    map->getLayers(layers);

    for (auto layer : layers)
    {
        auto xyzLayer = dynamic_cast<XYZImageLayer *>(layer.get());
        if (xyzLayer)
        {
            std::cout << LC << "Found XYZ Image Layer: " << xyzLayer->getName() << std::endl;

            // Create a test tile key
            const Profile *profile = xyzLayer->getProfile();
            if (profile)
            {
                TileKey testKey(1, 0, 0, profile); // Level 1, tile (0,0)
                std::cout << LC << "Testing tile key: " << testKey.str() << std::endl;

                // Try to create multiple images to force XYZ driver calls
                for (int level = 0; level <= 3; ++level)
                {
                    for (int x = 0; x < (1 << level); ++x)
                    {
                        for (int y = 0; y < (1 << level); ++y)
                        {
                            TileKey key(level, x, y, profile);
                            std::cout << LC << "Forcing tile creation: " << key.str() << std::endl;

                            // Force call to createImageImplementation which calls XYZ::Driver
                            std::cout << LC << "About to call createImageImplementation for " << key.str() << std::endl;
                            GeoImage geoImage = xyzLayer->createImageImplementation(key, nullptr);
                            std::cout << LC << "Finished calling createImageImplementation for " << key.str() << std::endl;
                            if (geoImage.valid())
                            {
                                std::cout << LC << "SUCCESS: Created image " << geoImage.getImage()->s() << "x" << geoImage.getImage()->t() << std::endl;
                            }
                            else
                            {
                                std::cout << LC << "FAILED: Could not create image for " << key.str() << std::endl;
                            }

                            // Only test a few tiles to avoid spam
                            if (level >= 2)
                                break;
                        }
                        if (level >= 2)
                            break;
                    }
                }
            }
        }
    }

    std::cout << LC << "=== XYZ Layer Test Complete ===" << std::endl;
}
#endif

#ifdef DEBUG_MYVIEWER
// Apply downloaded tiles directly to earth surface
void applyTilesToEarth(osgEarth::MapNode *mapNode)
{
    if (!mapNode)
    {
        std::cout << LC << "MapNode is null, cannot apply tiles" << std::endl;
        return;
    }

    std::cout << LC << "=== Applying Tiles to Earth Surface ===" << std::endl;

    // Try to force tile loading by accessing the map layers
    auto map = mapNode->getMap();
    if (map)
    {
        LayerVector layers;
        map->getLayers(layers);

        for (auto layer : layers)
        {
            std::string layerName = layer->getName();
            if (layerName == "Google Satellite" || layerName == "AWS Terrarium")
            {
                std::cout << LC << "Forcing update for layer: " << layerName << std::endl;

                // Multiple refresh attempts
                layer->setEnabled(false);
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                layer->setEnabled(true);

                // Force layer to refresh
                layer->dirty();

                // Force cache refresh
                layer->setCachePolicy(CachePolicy::USAGE_NO_CACHE);
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                layer->setCachePolicy(CachePolicy::USAGE_READ_WRITE);

                std::cout << LC << "Layer " << layerName << " refresh completed" << std::endl;
            }
        }
    }

    std::cout << LC << "Tile application completed" << std::endl;
}
#endif

int usage(const char *name)
{
    std::cout
        << "Custom osgEarth viewer with Google Maps and China boundaries" << std::endl
        << "Usage: " << name << " [config.earth]" << std::endl
        << "If no config file is provided, uses built-in configuration" << std::endl
        << "Features:" << std::endl
        << "  - Google Maps satellite imagery" << std::endl
        << "  - AWS Terrarium elevation data" << std::endl
        << "  - Atmospheric effects" << std::endl
        << "  - Geodetic graticule" << std::endl
        << "  - China boundaries in red" << std::endl;

    return 0;
}

osg::ref_ptr<Map> createMap()
{
    // Create the map
    auto map = new Map();

#ifdef DEBUG_MYVIEWER
    std::cout << LC << "Creating map layers..." << std::endl;

    // Add Google Maps satellite imagery with detailed logging
    std::cout << LC << "Creating Google Maps satellite imagery layer..." << std::endl;
#endif
    auto googleImagery = new XYZImageLayer();
    googleImagery->setName("Google Satellite");

    // Set URL with logging - try different Google Maps servers
    std::string googleURL = "https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}";
    URI googleURI(googleURL);
    googleImagery->setURL(googleURI);
#ifdef DEBUG_MYVIEWER
    std::cout << LC << "Google URL template: " << googleURL << std::endl;
    std::cout << LC << "Google URI full: " << googleURI.full() << std::endl;
    std::cout << LC << "Google URI base: " << googleURI.base() << std::endl;
#endif

    // Set additional options for better compatibility
    googleImagery->setFormat("jpg");
#ifdef DEBUG_MYVIEWER
    std::cout << LC << "Google layer format: jpg" << std::endl;

    // Test the actual URL that will be used
    std::string testURL = "https://mt0.google.com/vt/lyrs=s&x=0&y=0&z=1";
    std::cout << LC << "Test URL example: " << testURL << std::endl;
#endif

    // Use global-mercator profile for Google Maps
    const Profile *profile = Profile::create("global-mercator");
    googleImagery->setProfile(profile);
    googleImagery->setMinLevel(0);
    googleImagery->setMaxLevel(18); // Set reasonable max level to avoid warning
#ifdef DEBUG_MYVIEWER
    std::cout << LC << "Google layer profile: global-mercator" << std::endl;
#endif
    googleImagery->setEnabled(true);
    googleImagery->setVisible(true);

    // Add cache settings for better performance
    // CachePolicy cachePolicy(CachePolicy::USAGE_READ_WRITE);
    // googleImagery->setCachePolicy(cachePolicy);

#ifdef DEBUG_MYVIEWER
    // std::cout << LC << "Google layer cache policy: READ_WRITE" << std::endl;
#endif

    // Note: HTTP headers will be set globally via HTTPClient configuration

    map->addLayer(googleImagery);
#ifdef DEBUG_MYVIEWER
    std::cout << LC << "Added Google Maps satellite imagery layer (levels 0-18)" << std::endl;
    std::cout << LC << "Google layer enabled: " << googleImagery->getEnabled() << ", visible: " << googleImagery->getVisible() << std::endl;

    // Check if layer opened successfully
    Status layerStatus = googleImagery->getStatus();
    std::cout << LC << "Google layer status: " << layerStatus.toString() << std::endl;
    if (layerStatus.isError())
    {
        std::cout << LC << "ERROR: Google layer failed to open: " << layerStatus.message() << std::endl;
    }

    // Force layer to open by calling open() explicitly
    std::cout << LC << "Forcing Google layer to open..." << std::endl;
    Status openStatus = googleImagery->open();
    std::cout << LC << "Google layer open result: " << openStatus.toString() << std::endl;
#endif

#ifdef DEBUG_MYVIEWER
    // Add AWS Terrarium elevation with detailed logging
    std::cout << LC << "Creating AWS Terrarium elevation layer..." << std::endl;
#endif
    auto elevation = new XYZElevationLayer();
    elevation->setName("AWS Terrarium");

    // Set URL with logging
    std::string elevationURL = "https://s3.amazonaws.com/elevation-tiles-prod/terrarium/{z}/{x}/{y}.png";
    elevation->setURL(elevationURL);
#ifdef DEBUG_MYVIEWER
    std::cout << LC << "Elevation URL template: " << elevationURL << std::endl;
#endif

    // Use global-mercator profile for AWS Terrarium
    const Profile *elevationProfile = Profile::create("global-mercator");
    elevation->setProfile(elevationProfile);
    elevation->setMinLevel(0);
    elevation->setMaxLevel(13); // Set reasonable max level to avoid warning
#ifdef DEBUG_MYVIEWER
    std::cout << LC << "Elevation layer profile: global-mercator" << std::endl;
#endif
    elevation->setEnabled(true);
    elevation->setVisible(true);

    // Add cache settings for elevation
    // CachePolicy elevationCachePolicy(CachePolicy::USAGE_READ_WRITE);
    // elevation->setCachePolicy(elevationCachePolicy);
#ifdef DEBUG_MYVIEWER
    // std::cout << LC << "Elevation layer cache policy: READ_WRITE" << std::endl;
#endif

    map->addLayer(elevation);
#ifdef DEBUG_MYVIEWER
    std::cout << LC << "Added AWS Terrarium elevation layer (levels 0-13)" << std::endl;
    std::cout << LC << "Elevation layer enabled: " << elevation->getEnabled() << ", visible: " << elevation->getVisible() << std::endl;
#endif

    // Add geodetic graticule to match original osgviewer style
    auto graticule = new GeodeticGraticule();
    graticule->setName("Geodetic Graticule");
    graticule->setColor(Color(0.8f, 0.8f, 0.0f, 0.8f)); // Dimmer yellow, semi-transparent
    graticule->setLineWidth(1.0f);                      // Thinner lines like original
    graticule->setGridLinesVisible(true);
    graticule->setGridLabelsVisible(true);
    graticule->setEdgeLabelsVisible(true);
    graticule->setVisible(true);
    graticule->setEnabled(true);
    map->addLayer(graticule);
#ifdef DEBUG_MYVIEWER
    std::cout << LC << "Added geodetic graticule layer" << std::endl;
#endif

    return map;
}

void addChinaBoundaries(Map *map)
{
#ifdef DEBUG_MYVIEWER
    std::cout << LC << "Adding China boundaries..." << std::endl;
#endif

    // For now, use the simplified boundary implementation
    // TODO: Implement proper GeoJSON loading when API is clarified
    addSimpleChinaBoundary(map);
}

void addSimpleChinaBoundary(Map *map)
{
#ifdef DEBUG_MYVIEWER
    std::cout << LC << "Adding simplified China boundary as fallback..." << std::endl;
#endif

    // Create a simple annotation layer for China boundaries
    auto annotationLayer = new AnnotationLayer();
    annotationLayer->setName("China Boundaries (Simplified)");

    // Create a simple rectangular boundary for China (approximate)
    auto lineString = new LineString();
    lineString->push_back(osg::Vec3d(73.5, 53.5, 0));  // Northwest corner
    lineString->push_back(osg::Vec3d(134.8, 53.5, 0)); // Northeast corner
    lineString->push_back(osg::Vec3d(134.8, 18.2, 0)); // Southeast corner
    lineString->push_back(osg::Vec3d(73.5, 18.2, 0));  // Southwest corner
    lineString->push_back(osg::Vec3d(73.5, 53.5, 0));  // Close the rectangle

    // Create style for red 3-pixel boundaries
    Style lineStyle;
    lineStyle.getOrCreate<LineSymbol>()->stroke().mutable_value().color() = Color::Red;
    lineStyle.getOrCreate<LineSymbol>()->stroke().mutable_value().width() = Distance(3.0f, Units::PIXELS);
    lineStyle.getOrCreate<AltitudeSymbol>()->clamping() = AltitudeSymbol::CLAMP_TO_TERRAIN;
    lineStyle.getOrCreate<AltitudeSymbol>()->technique() = AltitudeSymbol::TECHNIQUE_DRAPE;

    // Create LocalGeometryNode with the line geometry
    auto geometryNode = new LocalGeometryNode(lineString, lineStyle);
    geometryNode->setPosition(GeoPoint(SpatialReference::get("wgs84"), 0, 0, 0));

    // Add to annotation layer
    annotationLayer->addChild(geometryNode);
    annotationLayer->setEnabled(true);
    annotationLayer->setVisible(true);

    // Add to map
    map->addLayer(annotationLayer);

#ifdef DEBUG_MYVIEWER
    std::cout << LC << "Added simplified China boundaries layer" << std::endl;
#endif
}

void addSkyEffects(MapNode *mapNode)
{
    // Add atmospheric sky effects using the factory method
    auto sky = SkyNode::create("simple");
    if (sky)
    {
        // Set to daytime for proper sun lighting and atmosphere
        sky->setDateTime(DateTime(2025, 1, 1, 12.0)); // Noon for proper lighting

        // Configure for maximum texture clarity - no shadows, uniform lighting
        sky->getSunLight()->setAmbient(osg::Vec4(1.0f, 1.0f, 1.0f, 1.0f));  // Full ambient light
        sky->getSunLight()->setDiffuse(osg::Vec4(0.0f, 0.0f, 0.0f, 1.0f));  // No diffuse = no shadows
        sky->getSunLight()->setSpecular(osg::Vec4(0.0f, 0.0f, 0.0f, 1.0f)); // No specular = no highlights

        // Enable atmosphere for blue atmospheric glow
        sky->setAtmosphereVisible(true);
        sky->setStarsVisible(true);
        sky->setSunVisible(false);  // Hide sun disc for space view
        sky->setMoonVisible(false); // Hide moon for cleaner view

        mapNode->addChild(sky);
#ifdef DEBUG_MYVIEWER
        std::cout << LC << "Added atmospheric sky effects (realistic atmosphere)" << std::endl;
#endif
    }
#ifdef DEBUG_MYVIEWER
    else
    {
        std::cout << LC << "Warning: Failed to create sky node" << std::endl;
    }
#endif
}

// to set up the manipulator when the viewer is realized
struct SetRealizeCallback : public osg::Operation
{
    SetRealizeCallback() {}

    virtual void operator()(osg::Object *object)
    {
        osgViewer::Viewer *viewer = dynamic_cast<osgViewer::Viewer *>(object);
        if (viewer)
        {
            // 获取地图节点和操控器
            MapNode *mapNode = MapNode::findMapNode(viewer->getSceneData());
            EarthManipulator *manip = dynamic_cast<EarthManipulator *>(viewer->getCameraManipulator());

            if (mapNode && manip)
            {
                // 设置一些操控器的默认参数
                manip->getSettings()->setMinMaxPitch(osg::DegreesToRadians(-90.0), osg::DegreesToRadians(-10.0));
            }
        }
    }
};

// 窗口大小改变事件处理器，确保地球椭球比例正确
class ResizeHandler : public osgGA::GUIEventHandler
{
public:
    virtual bool handle(const osgGA::GUIEventAdapter &ea, osgGA::GUIActionAdapter &aa)
    {
        if (ea.getEventType() == osgGA::GUIEventAdapter::RESIZE)
        {
            osgViewer::Viewer *viewer = dynamic_cast<osgViewer::Viewer *>(&aa);
            if (viewer)
            {
                double fovy = 45.0;
                double aspectRatio = double(ea.getWindowWidth()) / double(ea.getWindowHeight());
                double zNear = 1.0;
                double zFar = 1e12;

                viewer->getCamera()->setProjectionMatrixAsPerspective(fovy, aspectRatio, zNear, zFar);
                viewer->getCamera()->setViewport(0, 0, ea.getWindowWidth(), ea.getWindowHeight());
            }
        }
        return false;
    }
};

// LOD监控回调
class LODMonitorCallback : public osg::NodeCallback
{
public:
    LODMonitorCallback(MapNode *mapNode) : _mapNode(mapNode), _frameCount(0) {}

    virtual void operator()(osg::Node *node, osg::NodeVisitor *nv)
    {
        _frameCount++;
        if (_frameCount % 60 == 0) // 每60帧输出一次
        {
            if (_mapNode.valid())
            {
                osgViewer::Viewer *viewer = dynamic_cast<osgViewer::Viewer *>(nv->getUserData());
                if (viewer)
                {
                    EarthManipulator *manip = dynamic_cast<EarthManipulator *>(viewer->getCameraManipulator());
                    if (manip)
                    {
                        double distance = manip->getDistance();
                        std::cout << "[LOD Monitor] Frame: " << _frameCount << ", Distance: " << distance << " meters" << std::endl;
                    }
                }
            }
        }
        traverse(node, nv);
    }

protected:
    osg::observer_ptr<MapNode> _mapNode;
    unsigned int _frameCount;
};

// LOD监控和强制更新处理器
class LODUpdateHandler : public osgGA::GUIEventHandler
{
public:
    LODUpdateHandler(MapNode *mapNode) : _mapNode(mapNode), _lastDistance(-1.0) {}

    virtual bool handle(const osgGA::GUIEventAdapter &ea, osgGA::GUIActionAdapter &aa)
    {
        if (ea.getEventType() == osgGA::GUIEventAdapter::SCROLL)
        {
            // 滚轮事件时强制更新LOD
            if (_mapNode.valid())
            {
                osgViewer::Viewer *viewer = dynamic_cast<osgViewer::Viewer *>(&aa);
                if (viewer)
                {
                    EarthManipulator *manip = dynamic_cast<EarthManipulator *>(viewer->getCameraManipulator());
                    if (manip)
                    {
                        double distance = manip->getDistance();
                        std::cout << "[LOD] Scroll event - Distance: " << distance << " meters" << std::endl;

                        // 计算当前应该的LOD级别
                        int expectedLOD = (int)(log2(40075000.0 / distance)) + 1;
                        if (expectedLOD < 0)
                            expectedLOD = 0;
                        if (expectedLOD > 18)
                            expectedLOD = 18;
                        std::cout << "[LOD] Expected LOD level: " << expectedLOD << std::endl;

                        // 强制地形引擎更新 - 更激进的方法
                        TerrainEngine *terrain = _mapNode->getTerrainEngine();
                        if (terrain)
                        {
                            // 使用正确的API强制更新整个地球
                            terrain->invalidateRegion(_mapNode->getMap()->getProfile()->getExtent(), 0, 18);
                            std::cout << "[LOD] Forced terrain update for all levels 0-18" << std::endl;

                            // 强制所有图层无效化
                            LayerVector layers;
                            _mapNode->getMap()->getLayers(layers);
                            for (auto layer : layers)
                            {
                                if (layer->isOpen())
                                {
                                    std::cout << "[LOD] Invalidating layer: " << layer->getName() << std::endl;
                                    terrain->invalidateRegion({layer.get()}, _mapNode->getMap()->getProfile()->getExtent(), 0, 18);
                                }
                            }

                            // 额外强制请求重绘
                            viewer->requestRedraw();
                            viewer->requestContinuousUpdate(true); // 强制连续更新
                            std::cout << "[LOD] Requested viewer redraw and continuous update" << std::endl;
                        }

                        _lastDistance = distance;
                    }
                }
            }
        }
        return false;
    }

protected:
    osg::observer_ptr<MapNode> _mapNode;
    double _lastDistance;
};

// 时间控制和键盘交互处理器
class TimeControlHandler : public osgGA::GUIEventHandler
{
public:
    TimeControlHandler(SkyNode *skyNode, ShadowCaster *shadowCaster = nullptr)
        : _skyNode(skyNode), _shadowCaster(shadowCaster), _hour(12.0f), _animateTime(false), _lastTime(0.0)
    {
        _startTime = osg::Timer::instance()->time_s(); // 初始化开始时间
    }

    virtual bool handle(const osgGA::GUIEventAdapter &ea, osgGA::GUIActionAdapter &aa)
    {
        if (ea.getEventType() == osgGA::GUIEventAdapter::KEYDOWN)
        {
            switch (ea.getKey())
            {
            case 't':
            case 'T':
                // 切换时间动画
                _animateTime = !_animateTime;
                std::cout << "Time animation: " << (_animateTime ? "ON" : "OFF") << std::endl;
                aa.requestRedraw();
                return true;

            case '+':
            case '=':
                // 增加时间
                _hour += 0.5f;
                if (_hour >= 24.0f)
                    _hour = 0.0f;
                updateTime();
                aa.requestRedraw();
                return true;

            case '-':
            case '_':
                // 减少时间
                _hour -= 0.5f;
                if (_hour < 0.0f)
                    _hour = 23.5f;
                updateTime();
                aa.requestRedraw();
                return true;

            case '1':
                // 日出 (6:00)
                _hour = 6.0f;
                updateTime();
                aa.requestRedraw();
                return true;

            case '2':
                // 正午 (12:00)
                _hour = 12.0f;
                updateTime();
                aa.requestRedraw();
                return true;

            case '3':
                // 日落 (18:00)
                _hour = 18.0f;
                updateTime();
                aa.requestRedraw();
                return true;

            case '4':
                // 午夜 (0:00)
                _hour = 0.0f;
                updateTime();
                aa.requestRedraw();
                return true;

            case 's':
            case 'S':
                // 切换阴影
                if (_shadowCaster != nullptr)
                {
                    bool enabled = _shadowCaster->getEnabled();
                    _shadowCaster->setEnabled(!enabled);
                    std::cout << "Shadows: " << (!enabled ? "ON" : "OFF") << std::endl;
                    aa.requestRedraw();
                }
                return true;

            default:
                break;
            }
        }
        else if (ea.getEventType() == osgGA::GUIEventAdapter::FRAME && _animateTime)
        {
            // 自动时间动画
            double currentTime = osg::Timer::instance()->time_s();
            double elapsed = currentTime - _startTime;

            // 每24秒循环一天（1秒 = 1小时）
            _hour = fmod(elapsed, 24.0);
            updateTime();
            aa.requestRedraw();
        }

        return false;
    }

    virtual void getUsage(osg::ApplicationUsage &usage) const
    {
        usage.addKeyboardMouseBinding("t", "Toggle time animation");
        usage.addKeyboardMouseBinding("+/-", "Increase/Decrease time");
        usage.addKeyboardMouseBinding("1", "Set time to sunrise (6:00)");
        usage.addKeyboardMouseBinding("2", "Set time to noon (12:00)");
        usage.addKeyboardMouseBinding("3", "Set time to sunset (18:00)");
        usage.addKeyboardMouseBinding("4", "Set time to midnight (0:00)");
        usage.addKeyboardMouseBinding("s", "Toggle shadows on/off");
    }

protected:
    void updateTime()
    {
        if (_skyNode.valid())
        {
            DateTime currentDate = _skyNode->getDateTime();
            DateTime newTime(currentDate.year(), currentDate.month(), currentDate.day(), _hour);
            _skyNode->setDateTime(newTime);

            std::cout << "Time set to: " << std::fixed << std::setprecision(1) << _hour << ":00" << std::endl;
        }
    }

    osg::observer_ptr<SkyNode> _skyNode;
    osg::observer_ptr<ShadowCaster> _shadowCaster;
    float _hour;
    bool _animateTime;
    double _startTime;
    double _lastTime;
};

int main(int argc, char **argv)
{
    // Configure networking and cache before creating map
    configureNetworking();

    // Set notify level based on environment variables, defaulting to WARN
    const char *osg_notify_level = getenv("OSG_NOTIFY_LEVEL");

    osg::NotifySeverity level = osg::WARN; // Default to WARN

    if (osg_notify_level)
    {
        std::string levelStr = osg_notify_level;
        if (levelStr == "DEBUG")
            level = osg::DEBUG_INFO;
        else if (levelStr == "INFO")
            level = osg::INFO;
        else if (levelStr == "NOTICE")
            level = osg::NOTICE;
        else if (levelStr == "WARN")
            level = osg::WARN;
        else if (levelStr == "FATAL")
            level = osg::FATAL;
    }

    osg::setNotifyLevel(level);

    osg::ArgumentParser arguments(&argc, argv);
    if (arguments.read("--help"))
        return usage(argv[0]);

    // start up osgEarth
    osgEarth::initialize(arguments);

    // create a simple view but DON'T pass the arguments, since that can
    // trigger unwanted auto-configuration (like going fullscreen).
    osgViewer::Viewer viewer;

    // Apply global defaults to the camera's stateset to ensure lines and text render properly.
    GLUtils::setGlobalDefaults(viewer.getCamera()->getOrCreateStateSet());

    // Create a graphic context and window
    osg::ref_ptr<osg::GraphicsContext::Traits> traits = new osg::GraphicsContext::Traits;
    traits->x = 100;
    traits->y = 100;
    traits->width = 1980;
    traits->height = 1024;
    traits->windowDecoration = true;
    traits->doubleBuffer = true;
    traits->sharedContext = 0;

    osg::ref_ptr<osg::GraphicsContext> gc = osg::GraphicsContext::createGraphicsContext(traits.get());
    if (gc.valid())
    {
        viewer.getCamera()->setGraphicsContext(gc.get());
        viewer.getCamera()->setViewport(new osg::Viewport(0, 0, traits->width, traits->height));

        // 设置投影矩阵，确保地球椭球比例正确，不受窗口宽高比影响
        double fovy = 45.0; // 视场角
        double aspectRatio = double(traits->width) / double(traits->height);
        double zNear = 1.0;
        double zFar = 1e12; // 足够大的远平面以显示整个地球

        // 使用透视投影，但确保椭球体不会因窗口比例而变形
        viewer.getCamera()->setProjectionMatrixAsPerspective(fovy, aspectRatio, zNear, zFar);

        // 重要：设置计算近远平面的回调，确保地球几何体正确显示
        viewer.getCamera()->setComputeNearFarMode(osg::CullSettings::COMPUTE_NEAR_FAR_USING_BOUNDING_VOLUMES);
    }
    else
    {
        osg::notify(osg::FATAL) << "Unable to create graphics context." << std::endl;
        return 1;
    }

    // install our default manipulator (do this before calling load)
    osg::ref_ptr<EarthManipulator> manip = new EarthManipulator();
    EarthManipulator::Settings *settings = manip->getSettings();

    // Set up Google Earth-like controls
    EarthManipulator::ActionOptions panOptions;
    panOptions.add(EarthManipulator::OPTION_CONTINUOUS, true);
    panOptions.add(EarthManipulator::OPTION_SCALE_X, 5.0);
    panOptions.add(EarthManipulator::OPTION_SCALE_Y, 5.0);
    settings->bindMouse(EarthManipulator::ACTION_PAN, EarthManipulator::MOUSE_LEFT_BUTTON, 0, panOptions);

    EarthManipulator::ActionOptions rotateOptions;
    rotateOptions.add(EarthManipulator::OPTION_CONTINUOUS, true);
    rotateOptions.add(EarthManipulator::OPTION_SCALE_X, 5.0);
    rotateOptions.add(EarthManipulator::OPTION_SCALE_Y, 5.0);
    settings->bindMouse(EarthManipulator::ACTION_ROTATE, EarthManipulator::MOUSE_MIDDLE_BUTTON, 0, rotateOptions);

    EarthManipulator::ActionOptions zoomOptions;
    zoomOptions.add(EarthManipulator::OPTION_CONTINUOUS, true);
    zoomOptions.add(EarthManipulator::OPTION_SCALE_X, 2.0); // 增加缩放敏感度
    zoomOptions.add(EarthManipulator::OPTION_SCALE_Y, 2.0);
    settings->bindMouse(EarthManipulator::ACTION_ZOOM, EarthManipulator::MOUSE_RIGHT_BUTTON, 0, zoomOptions);

    // Scroll wheel forward: Zoom in - 增加敏感度
    EarthManipulator::ActionOptions scrollInOptions;
    scrollInOptions.add(EarthManipulator::OPTION_SCALE_X, 1.5); // 增加滚轮敏感度
    scrollInOptions.add(EarthManipulator::OPTION_SCALE_Y, 1.5);
    settings->bindScroll(EarthManipulator::ACTION_ZOOM_IN, osgGA::GUIEventAdapter::SCROLL_UP, 0, scrollInOptions);

    // Scroll wheel backward: Zoom out - 增加敏感度
    EarthManipulator::ActionOptions scrollOutOptions;
    scrollOutOptions.add(EarthManipulator::OPTION_SCALE_X, 1.5);
    scrollOutOptions.add(EarthManipulator::OPTION_SCALE_Y, 1.5);
    settings->bindScroll(EarthManipulator::ACTION_ZOOM_OUT, osgGA::GUIEventAdapter::SCROLL_DOWN, 0, scrollOutOptions);

    // 配置LOD敏感度
    settings->setMinMaxDistance(1000.0, 50000000.0); // 设置缩放范围
    settings->setZoomToMouse(true);                  // 启用鼠标位置缩放

    viewer.setCameraManipulator(manip);

    // disable the small-feature culling; necessary for some feature rendering
    viewer.getCamera()->setSmallFeatureCullingPixelSize(-1.0f);

    // 添加窗口大小改变事件处理器，确保地球椭球比例正确
    viewer.addEventHandler(new ResizeHandler());

    // install the handler:
    viewer.setRealizeOperation(new SetRealizeCallback);

    auto node = MapNodeHelper().load(arguments, &viewer);
    if (node.valid())
    {
        std::cout << "Scene loaded successfully" << std::endl;
        viewer.setSceneData(node);

        // 在场景加载后设置默认视点位置
        MapNode *mapNode = MapNode::get(node);
        EarthManipulator *manip = dynamic_cast<EarthManipulator *>(viewer.getCameraManipulator());

        // 调试输出
        std::cout << "DEBUG: mapNode = " << (mapNode ? "valid" : "null") << std::endl;
        std::cout << "DEBUG: manip = " << (manip ? "valid" : "null") << std::endl;

        // 添加LOD更新处理器
        if (mapNode)
        {
            viewer.addEventHandler(new LODUpdateHandler(mapNode));
            std::cout << "LOD update handler added" << std::endl;

            // 调试：检查所有图层的状态
            LayerVector layers;
            mapNode->getMap()->getLayers(layers);
            std::cout << "\n=== LAYER DEBUG INFO ===" << std::endl;
            for (auto layer : layers)
            {
                std::cout << "Layer: " << layer->getName()
                          << ", Type: " << typeid(*layer).name()
                          << ", Open: " << (layer->isOpen() ? "YES" : "NO")
                          << ", Node: " << (layer->getNode() ? "YES" : "NO") << std::endl;

                // 特别检查FeatureModelLayer
                auto fml = dynamic_cast<FeatureModelLayer *>(layer.get());
                if (fml)
                {
                    std::cout << "  -> FeatureModelLayer detected!" << std::endl;
                    std::cout << "  -> FeatureSource: " << (fml->getFeatureSource() ? "YES" : "NO") << std::endl;
                    std::cout << "  -> StyleSheet: " << (fml->getStyleSheet() ? "YES" : "NO") << std::endl;
                    std::cout << "  -> Status: " << fml->getStatus().toString() << std::endl;

                    // 检查样式表内容
                    if (fml->getStyleSheet())
                    {
                        auto styleSheet = fml->getStyleSheet();
                        std::cout << "  -> StyleSheet has " << styleSheet->getStyles().size() << " styles" << std::endl;
                        for (const auto &style : styleSheet->getStyles())
                        {
                            std::cout << "  -> Style: " << style.first << std::endl;

                            // 打印所有符号
                            const SymbolList &symbols = style.second.symbols();
                            std::cout << "    -> Total symbols: " << symbols.size() << std::endl;
                            for (const auto &symbol : symbols)
                            {
                                std::cout << "    -> Symbol type: " << symbol->className() << std::endl;
                            }

                            // 检查LineSymbol
                            const LineSymbol *lineSymbol = style.second.get<LineSymbol>();
                            if (lineSymbol)
                            {
                                std::cout << "    -> LineSymbol found" << std::endl;
                                if (lineSymbol->stroke().isSet())
                                {
                                    const Stroke &stroke = lineSymbol->stroke().get();
                                    std::cout << "    -> Stroke color: " << stroke.color().r() << ","
                                              << stroke.color().g() << "," << stroke.color().b() << ","
                                              << stroke.color().a() << std::endl;
                                    if (stroke.width().isSet())
                                    {
                                        std::cout << "    -> Stroke width: " << stroke.width().get().literal().asParseableString() << std::endl;
                                    }
                                }
                            }
                            else
                            {
                                std::cout << "    -> No LineSymbol found" << std::endl;
                            }
                        }
                    }

                    // 检查FeatureSource的详细状态
                    auto fs = fml->getFeatureSource();
                    if (fs)
                    {
                        std::cout << "  -> FeatureSource Status: " << fs->getStatus().toString() << std::endl;
                        std::cout << "  -> FeatureSource Open: " << (fs->isOpen() ? "YES" : "NO") << std::endl;
                    }
                    else
                    {
                        std::cout << "  -> FeatureSource is NULL - checking configuration..." << std::endl;

                        // 测试直接解析GeoJSON文件
                        std::cout << "  -> Testing direct GeoJSON parsing..." << std::endl;
                        std::ifstream testFile("F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/redist_desk/china_boundaries.geojson");
                        if (testFile.is_open())
                        {
                            std::string content((std::istreambuf_iterator<char>(testFile)),
                                                (std::istreambuf_iterator<char>()));
                            testFile.close();

                            std::cout << "  -> File size: " << content.size() << " bytes" << std::endl;

                            // Test first 100 characters
                            std::string preview = content.substr(0, 100);
                            std::cout << "  -> File preview: " << preview << "..." << std::endl;

                            // Test JSON parsing
                            osgEarth::Util::Json::Value testRoot;
                            osgEarth::Util::Json::Reader testReader;

                            if (testReader.parse(content, testRoot))
                            {
                                std::cout << "  -> JSON parsing SUCCESS!" << std::endl;
                                std::cout << "  -> Root type: " << testRoot.get("type", "").asString() << std::endl;
                                if (testRoot.isMember("features") && testRoot["features"].isArray())
                                {
                                    std::cout << "  -> Features count: " << testRoot["features"].size() << std::endl;

                                    // Test first feature
                                    if (testRoot["features"].size() > 0)
                                    {
                                        const osgEarth::Util::Json::Value &firstFeature = testRoot["features"][(unsigned int)0];
                                        std::cout << "  -> First feature type: " << firstFeature.get("type", "").asString() << std::endl;

                                        if (firstFeature.isMember("geometry"))
                                        {
                                            const osgEarth::Util::Json::Value &geom = firstFeature["geometry"];
                                            std::cout << "  -> Geometry type: " << geom.get("type", "").asString() << std::endl;

                                            if (geom.isMember("coordinates") && geom["coordinates"].isArray())
                                            {
                                                std::cout << "  -> Coordinates array size: " << geom["coordinates"].size() << std::endl;
                                            }
                                        }
                                    }
                                }
                            }
                            else
                            {
                                std::cout << "  -> JSON parsing FAILED!" << std::endl;
                                std::cout << "  -> Error: " << testReader.getFormatedErrorMessages() << std::endl;
                            }
                        }
                        else
                        {
                            std::cout << "  -> Cannot open GeoJSON file!" << std::endl;
                        }

                        // 尝试手动创建GeosFeatureSource来测试
                        osg::ref_ptr<GeosFeatureSource> geosFS = new GeosFeatureSource();
                        geosFS->setURL(URI("F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/redist_desk/china_boundaries.geojson"));
                        geosFS->options().format() = "geojson";

                        Status testStatus = geosFS->open();
                        std::cout << "  -> Manual GeosFeatureSource test: " << testStatus.toString() << std::endl;

                        if (testStatus.isOK())
                        {
                            std::cout << "  -> GeosFeatureSource can be created manually!" << std::endl;
                            std::cout << "  -> Feature count: " << geosFS->getFeatureCount() << std::endl;
                        }
                    }
                }
            }
            std::cout << "=========================" << std::endl;
        }

        // 添加类似谷歌地球的渐变大气效果
        std::cout << "Adding Google-like gradual atmospheric effects..." << std::endl;

        // 简化天空节点配置，修正白圈问题
        osg::ref_ptr<SkyNode> skyNode = SkyNode::create("simple");
        if (skyNode.valid())
        {
            // 设置基本的天空参数
            skyNode->setEphemeris(new Ephemeris);
            skyNode->setDateTime(DateTime(2024, 6, 15, 12.0));

            // 配置天空可见性 - 关键修正：隐藏太阳和月亮避免白圈
            skyNode->setSunVisible(false);       // 隐藏太阳圆盘避免白圈
            skyNode->setMoonVisible(false);      // 隐藏月亮
            skyNode->setStarsVisible(true);      // 显示星空
            skyNode->setAtmosphereVisible(true); // 显示大气层 - 这是蓝色效果的关键

            // 调整太阳光参数 - 优化纹理清晰度
            osg::Light *sunLight = skyNode->getSunLight();
            if (sunLight)
            {
                // 高环境光确保纹理清晰，低漫反射减少阴影
                sunLight->setAmbient(osg::Vec4(0.8f, 0.8f, 0.8f, 1.0f));  // 高环境光
                sunLight->setDiffuse(osg::Vec4(0.3f, 0.3f, 0.3f, 1.0f));  // 低漫反射
                sunLight->setSpecular(osg::Vec4(0.1f, 0.1f, 0.1f, 1.0f)); // 最小镜面反射
            }

            // 使用默认的大气散射参数，避免复杂配置导致的白圈问题
            // 让osgEarth使用内置的大气渲染算法

            // 简化场景图，不使用阴影系统确保纹理清晰
            mapNode->addChild(skyNode);

            // 将天空的光源附加到视口
            skyNode->attach(&viewer, 0);

            // 创建时间控制器（不使用阴影）
            TimeControlHandler *timeControlHandler = new TimeControlHandler(skyNode.get(), nullptr);
            viewer.addEventHandler(timeControlHandler);

            // 直接使用mapNode作为场景根节点
            viewer.setSceneData(mapNode);

            std::cout << "Blue atmospheric effects added successfully (no white circle)." << std::endl;
            std::cout << "\nPress 'T' to animate time." << std::endl;
        }
        else
        {
            std::cout << "Failed to create sky node, using basic scene" << std::endl;
        }

        // 在启动主循环之前设置视点
        double longitude = 104.0;    // 东经104度
        double latitude = 35.5;      // 北纬35.5度
        double altitude = 5000000.0; // 高度500万米，可以看到整个中国

        // 调试：输出视点参数
        std::cout << "DEBUG: Setting viewpoint..." << std::endl;
        std::cout << "DEBUG: Longitude: " << longitude << std::endl;
        std::cout << "DEBUG: Latitude: " << latitude << std::endl;
        std::cout << "DEBUG: Altitude: " << altitude << std::endl;

        // 创建视点位置
        Viewpoint vp;
        vp.focalPoint() = GeoPoint(
            mapNode->getMapSRS(),
            longitude, latitude, 0.0, // 焦点在地面
            ALTMODE_ABSOLUTE);
        vp.range() = Distance(altitude, Units::METERS); // 观察距离
        vp.heading() = Angle(0.0, Units::DEGREES);      // 朝向北方
        vp.pitch() = Angle(-90.0, Units::DEGREES);      // 俯视角度（垂直向下看）

        std::cout << "DEBUG: Viewpoint range: " << vp.range()->as(Units::METERS) << " meters" << std::endl;

        // 应用视点设置
        manip->setViewpoint(vp);
        std::cout << "DEBUG: setViewpoint() called" << std::endl;

        // 等待一帧让视点设置生效
        viewer.frame();

        // 验证视点是否设置成功
        Viewpoint currentVP = manip->getViewpoint();
        std::cout << "DEBUG: Current viewpoint after setting:" << std::endl;
        std::cout << "DEBUG: Current focal point: " << currentVP.focalPoint()->x() << ", " << currentVP.focalPoint()->y() << std::endl;
        std::cout << "DEBUG: Current range: " << currentVP.range()->as(Units::METERS) << " meters" << std::endl;

        std::cout << "Default viewpoint set to China (104°E, 35.5°N)" << std::endl;
        std::cout << "Initial distance: " << manip->getDistance() << " meters" << std::endl;

        // 输出地形引擎信息
        TerrainEngine *terrain = mapNode->getTerrainEngine();
        if (terrain)
        {
            std::cout << "Terrain engine active and ready for LOD updates" << std::endl;
        }
    }
    else
    {
        std::cout << "Failed to get MapNode or EarthManipulator" << std::endl;
    }

    return viewer.run();
}
else
{
    std::cout << "Failed to load scene" << std::endl;
}

return usage(argv[0]);
}
