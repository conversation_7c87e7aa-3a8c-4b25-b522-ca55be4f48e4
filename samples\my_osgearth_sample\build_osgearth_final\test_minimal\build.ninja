# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.26

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: osgearth_minimal_test
# Configurations: Release
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Release
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/build_osgearth_final/test_minimal/
# =============================================================================
# Object build statements for EXECUTABLE target osgearth_minimal_test


#############################################
# Order-only phony target for osgearth_minimal_test

build cmake_object_order_depends_target_osgearth_minimal_test: phony || CMakeFiles/osgearth_minimal_test.dir

build CMakeFiles/osgearth_minimal_test.dir/osgearth_minimal_test.cpp.o: CXX_COMPILER__osgearth_minimal_test_unscanned_Release F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/build_osgearth_final/test_minimal/osgearth_minimal_test.cpp || cmake_object_order_depends_target_osgearth_minimal_test
  DEP_FILE = CMakeFiles\osgearth_minimal_test.dir\osgearth_minimal_test.cpp.o.d
  FLAGS = -O3 -DNDEBUG -std=gnu++17 -DOSG_GLSL_VERSION=300 -O3
  INCLUDES = -IF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include -IF:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src
  OBJECT_DIR = CMakeFiles\osgearth_minimal_test.dir
  OBJECT_FILE_DIR = CMakeFiles\osgearth_minimal_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target osgearth_minimal_test


#############################################
# Link the executable osgearth_minimal_test.html

build osgearth_minimal_test.html: CXX_EXECUTABLE_LINKER__osgearth_minimal_test_Release CMakeFiles/osgearth_minimal_test.dir/osgearth_minimal_test.cpp.o | F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosg.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgViewer.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgDB.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgGA.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgUtil.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libOpenThreads.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libz.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libexpat.a
  FLAGS = -O3 -DNDEBUG
  LINK_FLAGS = -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s WASM=1 -s INITIAL_MEMORY=134217728 -s ALLOW_MEMORY_GROWTH=1 -s MAXIMUM_MEMORY=536870912 -s ASSERTIONS=1 -s SAFE_HEAP=0 -s STACK_OVERFLOW_CHECK=1 -s EXPORTED_FUNCTIONS=['_main'] -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap'] -s NO_EXIT_RUNTIME=1 -s DISABLE_EXCEPTION_CATCHING=0 -s USE_PTHREADS=0 -s PTHREAD_POOL_SIZE=0 -s PROXY_TO_PTHREAD=0 -s OFFSCREENCANVAS_SUPPORT=0 -O3
  LINK_LIBRARIES = F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosg.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgViewer.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgDB.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgGA.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgUtil.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libOpenThreads.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libz.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libexpat.a
  OBJECT_DIR = CMakeFiles\osgearth_minimal_test.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = osgearth_minimal_test.html
  TARGET_PDB = osgearth_minimal_test.html.dbg

# =============================================================================
# Object build statements for EXECUTABLE target osgearth_webgl_fixed


#############################################
# Order-only phony target for osgearth_webgl_fixed

build cmake_object_order_depends_target_osgearth_webgl_fixed: phony || CMakeFiles/osgearth_webgl_fixed.dir

build CMakeFiles/osgearth_webgl_fixed.dir/osgearth_webgl_fixed.cpp.o: CXX_COMPILER__osgearth_webgl_fixed_unscanned_Release F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/build_osgearth_final/test_minimal/osgearth_webgl_fixed.cpp || cmake_object_order_depends_target_osgearth_webgl_fixed
  DEP_FILE = CMakeFiles\osgearth_webgl_fixed.dir\osgearth_webgl_fixed.cpp.o.d
  FLAGS = -O3 -DNDEBUG -std=gnu++17 -DOSG_GLSL_VERSION=300 -O3
  INCLUDES = -IF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include -IF:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src
  OBJECT_DIR = CMakeFiles\osgearth_webgl_fixed.dir
  OBJECT_FILE_DIR = CMakeFiles\osgearth_webgl_fixed.dir


# =============================================================================
# Link build statements for EXECUTABLE target osgearth_webgl_fixed


#############################################
# Link the executable osgearth_webgl_fixed.html

build osgearth_webgl_fixed.html: CXX_EXECUTABLE_LINKER__osgearth_webgl_fixed_Release CMakeFiles/osgearth_webgl_fixed.dir/osgearth_webgl_fixed.cpp.o | F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosg.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgViewer.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgDB.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgGA.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgUtil.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libOpenThreads.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libz.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libexpat.a
  FLAGS = -O3 -DNDEBUG
  LINK_FLAGS = -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s WASM=1 -s INITIAL_MEMORY=134217728 -s ALLOW_MEMORY_GROWTH=1 -s MAXIMUM_MEMORY=536870912 -s ASSERTIONS=1 -s SAFE_HEAP=0 -s STACK_OVERFLOW_CHECK=1 -s EXPORTED_FUNCTIONS=['_main'] -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap'] -s NO_EXIT_RUNTIME=1 -s DISABLE_EXCEPTION_CATCHING=0 -s USE_PTHREADS=0 -s PTHREAD_POOL_SIZE=0 -s PROXY_TO_PTHREAD=0 -s OFFSCREENCANVAS_SUPPORT=0 -O3
  LINK_LIBRARIES = F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosg.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgViewer.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgDB.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgGA.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgUtil.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libOpenThreads.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libz.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libexpat.a
  OBJECT_DIR = CMakeFiles\osgearth_webgl_fixed.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = osgearth_webgl_fixed.html
  TARGET_PDB = osgearth_webgl_fixed.html.dbg

# =============================================================================
# Object build statements for EXECUTABLE target osgearth_pure_vertex


#############################################
# Order-only phony target for osgearth_pure_vertex

build cmake_object_order_depends_target_osgearth_pure_vertex: phony || CMakeFiles/osgearth_pure_vertex.dir

build CMakeFiles/osgearth_pure_vertex.dir/osgearth_pure_vertex.cpp.o: CXX_COMPILER__osgearth_pure_vertex_unscanned_Release F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/build_osgearth_final/test_minimal/osgearth_pure_vertex.cpp || cmake_object_order_depends_target_osgearth_pure_vertex
  DEP_FILE = CMakeFiles\osgearth_pure_vertex.dir\osgearth_pure_vertex.cpp.o.d
  FLAGS = -O3 -DNDEBUG -std=gnu++17 -DOSG_GLSL_VERSION=300 -O3
  INCLUDES = -IF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include -IF:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src
  OBJECT_DIR = CMakeFiles\osgearth_pure_vertex.dir
  OBJECT_FILE_DIR = CMakeFiles\osgearth_pure_vertex.dir


# =============================================================================
# Link build statements for EXECUTABLE target osgearth_pure_vertex


#############################################
# Link the executable osgearth_pure_vertex.html

build osgearth_pure_vertex.html: CXX_EXECUTABLE_LINKER__osgearth_pure_vertex_Release CMakeFiles/osgearth_pure_vertex.dir/osgearth_pure_vertex.cpp.o | F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosg.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgViewer.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgDB.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgGA.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgUtil.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libOpenThreads.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libz.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libexpat.a
  FLAGS = -O3 -DNDEBUG
  LINK_FLAGS = -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s WASM=1 -s INITIAL_MEMORY=134217728 -s ALLOW_MEMORY_GROWTH=1 -s MAXIMUM_MEMORY=536870912 -s ASSERTIONS=1 -s SAFE_HEAP=0 -s STACK_OVERFLOW_CHECK=1 -s EXPORTED_FUNCTIONS=['_main'] -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap'] -s NO_EXIT_RUNTIME=1 -s DISABLE_EXCEPTION_CATCHING=0 -s USE_PTHREADS=0 -s PTHREAD_POOL_SIZE=0 -s PROXY_TO_PTHREAD=0 -s OFFSCREENCANVAS_SUPPORT=0 -O3
  LINK_LIBRARIES = F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosg.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgViewer.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgDB.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgGA.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgUtil.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libOpenThreads.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libz.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libexpat.a
  OBJECT_DIR = CMakeFiles\osgearth_pure_vertex.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = osgearth_pure_vertex.html
  TARGET_PDB = osgearth_pure_vertex.html.dbg

# =============================================================================
# Object build statements for EXECUTABLE target osgearth_final_solution


#############################################
# Order-only phony target for osgearth_final_solution

build cmake_object_order_depends_target_osgearth_final_solution: phony || CMakeFiles/osgearth_final_solution.dir

build CMakeFiles/osgearth_final_solution.dir/osgearth_final_solution.cpp.o: CXX_COMPILER__osgearth_final_solution_unscanned_Release F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/build_osgearth_final/test_minimal/osgearth_final_solution.cpp || cmake_object_order_depends_target_osgearth_final_solution
  DEP_FILE = CMakeFiles\osgearth_final_solution.dir\osgearth_final_solution.cpp.o.d
  FLAGS = -O3 -DNDEBUG -std=gnu++17 -DOSG_GLSL_VERSION=300 -O3
  INCLUDES = -IF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include -IF:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src
  OBJECT_DIR = CMakeFiles\osgearth_final_solution.dir
  OBJECT_FILE_DIR = CMakeFiles\osgearth_final_solution.dir


# =============================================================================
# Link build statements for EXECUTABLE target osgearth_final_solution


#############################################
# Link the executable osgearth_final_solution.html

build osgearth_final_solution.html: CXX_EXECUTABLE_LINKER__osgearth_final_solution_Release CMakeFiles/osgearth_final_solution.dir/osgearth_final_solution.cpp.o | F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/redist_wasm/libosgEarth.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosg.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgViewer.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgDB.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgGA.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgUtil.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libOpenThreads.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libz.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libexpat.a
  FLAGS = -O3 -DNDEBUG
  LINK_FLAGS = -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s WASM=1 -s INITIAL_MEMORY=134217728 -s ALLOW_MEMORY_GROWTH=1 -s MAXIMUM_MEMORY=536870912 -s ASSERTIONS=1 -s SAFE_HEAP=0 -s STACK_OVERFLOW_CHECK=1 -s EXPORTED_FUNCTIONS=['_main'] -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap'] -s NO_EXIT_RUNTIME=1 -s DISABLE_EXCEPTION_CATCHING=0 -s USE_PTHREADS=0 -s PTHREAD_POOL_SIZE=0 -s PROXY_TO_PTHREAD=0 -s OFFSCREENCANVAS_SUPPORT=0 -O3
  LINK_LIBRARIES = F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/redist_wasm/libosgEarth.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosg.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgViewer.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgDB.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgGA.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgUtil.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libOpenThreads.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libz.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libexpat.a
  OBJECT_DIR = CMakeFiles\osgearth_final_solution.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = osgearth_final_solution.html
  TARGET_PDB = osgearth_final_solution.html.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\src\applications\my_osgearth_sample\build_osgearth_final\test_minimal && "C:\Program Files\CMake\bin\cmake-gui.exe" -SF:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\src\applications\my_osgearth_sample\build_osgearth_final\test_minimal -BF:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\src\applications\my_osgearth_sample\build_osgearth_final\test_minimal"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\src\applications\my_osgearth_sample\build_osgearth_final\test_minimal && "C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -SF:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\src\applications\my_osgearth_sample\build_osgearth_final\test_minimal -BF:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\src\applications\my_osgearth_sample\build_osgearth_final\test_minimal"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build osgearth_final_solution: phony osgearth_final_solution.html

build osgearth_minimal_test: phony osgearth_minimal_test.html

build osgearth_pure_vertex: phony osgearth_pure_vertex.html

build osgearth_webgl_fixed: phony osgearth_webgl_fixed.html

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/build_osgearth_final/test_minimal

build all: phony osgearth_minimal_test.html osgearth_webgl_fixed.html osgearth_pure_vertex.html osgearth_final_solution.html

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCXXInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCommonLanguageInclude.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeGenericSystem.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeInitializeConfigs.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeLanguageInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInitialize.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-C.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-CXX.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/GNU.cmake C$:/dev/emsdk/upstream/emscripten/cmake/Modules/Platform/Emscripten.cmake CMakeCache.txt CMakeFiles/3.26.4/CMakeCCompiler.cmake CMakeFiles/3.26.4/CMakeCXXCompiler.cmake CMakeFiles/3.26.4/CMakeSystem.cmake CMakeLists.txt
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCXXInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCommonLanguageInclude.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeGenericSystem.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeInitializeConfigs.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeLanguageInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInitialize.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-C.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-CXX.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/GNU.cmake C$:/dev/emsdk/upstream/emscripten/cmake/Modules/Platform/Emscripten.cmake CMakeCache.txt CMakeFiles/3.26.4/CMakeCCompiler.cmake CMakeFiles/3.26.4/CMakeCXXCompiler.cmake CMakeFiles/3.26.4/CMakeSystem.cmake CMakeLists.txt: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
