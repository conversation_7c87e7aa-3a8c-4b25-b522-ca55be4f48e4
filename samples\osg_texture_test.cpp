#include <osg/Geode>
#include <osg/Geometry>
#include <osg/Texture2D>
#include <osg/Image>
#include <osg/StateSet>
#include <osg/StateAttribute>
#include <osg/TexEnv>
#include <osg/Material>
#include <osg/LightModel>
#include <osg/Program>
#include <osg/Shader>
#include <osg/Uniform>
#include <osgViewer/Viewer>
#include <osg/GraphicsContext>
#include <iostream>

#ifdef __EMSCRIPTEN__
#include <emscripten.h>
#include <emscripten/html5.h>
#include <GL/gl.h>
#endif

// 全局变量
static osgViewer::Viewer *g_viewer = nullptr;
static bool g_running = true;

#ifdef __EMSCRIPTEN__
// WebGL上下文管理
static EMSCRIPTEN_WEBGL_CONTEXT_HANDLE g_webgl_context = 0;
static bool g_webgl_initialized = false;

// 简单的Emscripten GraphicsContext
class EmscriptenGraphicsContext : public osg::GraphicsContext
{
public:
  EmscriptenGraphicsContext(Traits *traits) : osg::GraphicsContext()
  {
    _traits = traits;
    _valid = true;
    setState(new osg::State);
    getState()->setGraphicsContext(this);

    std::cout << "[GC] EmscriptenGraphicsContext created" << std::endl;
  }

  virtual ~EmscriptenGraphicsContext()
  {
    std::cout << "[GC] EmscriptenGraphicsContext destroyed" << std::endl;
  }

  virtual bool valid() const override { return _valid; }

  virtual bool realizeImplementation() override
  {
    std::cout << "[GC] realizeImplementation called" << std::endl;

    // 确保WebGL上下文是当前的
    if (g_webgl_context > 0)
    {
      EMSCRIPTEN_RESULT result = emscripten_webgl_make_context_current(g_webgl_context);
      if (result == EMSCRIPTEN_RESULT_SUCCESS)
      {
        std::cout << "[GC] WebGL context made current successfully" << std::endl;
        _realized = true;
        return true;
      }
      else
      {
        std::cout << "[GC] Failed to make WebGL context current: " << result << std::endl;
      }
    }

    return false;
  }

  virtual bool isRealizedImplementation() const override
  {
    return _realized;
  }

  virtual void closeImplementation() override
  {
    std::cout << "[GC] closeImplementation called" << std::endl;
    _realized = false;
  }

  virtual bool makeCurrentImplementation() override
  {
    if (g_webgl_context > 0)
    {
      EMSCRIPTEN_RESULT result = emscripten_webgl_make_context_current(g_webgl_context);
      return (result == EMSCRIPTEN_RESULT_SUCCESS);
    }
    return false;
  }

  virtual bool makeContextCurrentImplementation(GraphicsContext *readContext) override
  {
    // 对于WebGL，读取上下文和绘制上下文是同一个
    return makeCurrentImplementation();
  }

  virtual bool releaseContextImplementation() override
  {
    // WebGL上下文不需要释放
    return true;
  }

  virtual void swapBuffersImplementation() override
  {
    // WebGL自动处理缓冲区交换
  }

  virtual void bindPBufferToTextureImplementation(GLenum buffer) override
  {
    // WebGL不支持PBuffer
  }

private:
  bool _valid = false;
  bool _realized = false;
};

// 简单的Emscripten WindowingSystemInterface
class EmscriptenWindowingSystemInterface : public osg::GraphicsContext::WindowingSystemInterface
{
public:
  EmscriptenWindowingSystemInterface()
  {
    std::cout << "[WSI] Creating Emscripten WindowingSystemInterface" << std::endl;
  }

  virtual ~EmscriptenWindowingSystemInterface()
  {
    std::cout << "[WSI] Destroying Emscripten WindowingSystemInterface" << std::endl;
  }

  virtual unsigned int getNumScreens(const osg::GraphicsContext::ScreenIdentifier &screenIdentifier = osg::GraphicsContext::ScreenIdentifier()) override
  {
    return 1;
  }

  virtual void getScreenSettings(const osg::GraphicsContext::ScreenIdentifier &screenIdentifier, osg::GraphicsContext::ScreenSettings &resolution) override
  {
    resolution.width = 800;
    resolution.height = 600;
    resolution.refreshRate = 60.0f;
    resolution.colorDepth = 24;
    std::cout << "[WSI] getScreenSettings: " << resolution.width << "x" << resolution.height << std::endl;
  }

  virtual void enumerateScreenSettings(const osg::GraphicsContext::ScreenIdentifier &screenIdentifier, osg::GraphicsContext::ScreenSettingsList &resolutionList) override
  {
    osg::GraphicsContext::ScreenSettings settings;
    settings.width = 800;
    settings.height = 600;
    settings.refreshRate = 60.0f;
    settings.colorDepth = 24;
    resolutionList.push_back(settings);
    std::cout << "[WSI] enumerateScreenSettings: Added 800x600" << std::endl;
  }

  virtual osg::GraphicsContext *createGraphicsContext(osg::GraphicsContext::Traits *traits) override
  {
    std::cout << "[WSI] createGraphicsContext called" << std::endl;

    if (!traits)
    {
      std::cout << "[WSI] No traits provided" << std::endl;
      return nullptr;
    }

    std::cout << "[WSI] Creating graphics context for " << traits->width << "x" << traits->height << std::endl;

    // 创建一个简单的GraphicsContext
    // 我们已经有了WebGL上下文，所以这里主要是告诉OSG我们有一个有效的上下文
    osg::ref_ptr<EmscriptenGraphicsContext> context = new EmscriptenGraphicsContext(traits);

    return context.release();
  }
};

// 初始化WebGL上下文
bool initializeWebGL()
{
  if (g_webgl_initialized)
    return true;

  std::cout << "[WebGL] Initializing WebGL context..." << std::endl;

  // 创建WebGL上下文属性
  EmscriptenWebGLContextAttributes attrs;
  emscripten_webgl_init_context_attributes(&attrs);

  attrs.alpha = 1;
  attrs.depth = 1;
  attrs.stencil = 0;
  attrs.antialias = 1;
  attrs.premultipliedAlpha = 0;
  attrs.preserveDrawingBuffer = 0;
  attrs.powerPreference = EM_WEBGL_POWER_PREFERENCE_DEFAULT;
  attrs.failIfMajorPerformanceCaveat = 0;
  attrs.majorVersion = 2;
  attrs.minorVersion = 0;
  attrs.enableExtensionsByDefault = 1;

  // 尝试创建WebGL 2.0上下文
  g_webgl_context = emscripten_webgl_create_context("#canvas", &attrs);

  if (g_webgl_context <= 0)
  {
    std::cout << "[WebGL] WebGL2 failed, trying WebGL1..." << std::endl;
    attrs.majorVersion = 1;
    attrs.minorVersion = 0;
    g_webgl_context = emscripten_webgl_create_context("#canvas", &attrs);
  }

  if (g_webgl_context > 0)
  {
    EMSCRIPTEN_RESULT result = emscripten_webgl_make_context_current(g_webgl_context);
    if (result == EMSCRIPTEN_RESULT_SUCCESS)
    {
      g_webgl_initialized = true;
      std::cout << "[WebGL] WebGL context initialized successfully" << std::endl;

      // 基本WebGL设置
      glEnable(GL_DEPTH_TEST);
      glDepthFunc(GL_LEQUAL);
      glClearColor(0.2f, 0.2f, 0.2f, 1.0f);

      // 设置视口
      int canvas_width, canvas_height;
      emscripten_get_canvas_element_size("#canvas", &canvas_width, &canvas_height);
      glViewport(0, 0, canvas_width, canvas_height);

      std::cout << "[WebGL] Canvas size: " << canvas_width << "x" << canvas_height << std::endl;
      return true;
    }
    else
    {
      std::cout << "[WebGL] Failed to make WebGL context current: " << result << std::endl;
    }
  }

  std::cout << "[WebGL] Failed to create WebGL context" << std::endl;
  return false;
}

// 初始化WindowingSystemInterface
void initializeWindowingSystem()
{
  std::cout << "[WSI] Initializing Emscripten WindowingSystemInterface..." << std::endl;

  // 创建并注册我们的WindowingSystemInterface
  osg::ref_ptr<EmscriptenWindowingSystemInterface> wsi = new EmscriptenWindowingSystemInterface();

  // 添加到WindowingSystemInterfaces
  osg::GraphicsContext::getWindowingSystemInterfaces()->addWindowingSystemInterface(wsi.get());

  std::cout << "[WSI] WindowingSystemInterface registered successfully" << std::endl;
}
#endif

// 创建简单的测试纹理
osg::ref_ptr<osg::Texture2D> createSimpleTestTexture()
{
  std::cout << "[DEBUG] Creating simple test texture..." << std::endl;

  // 创建32x32的简单纹理
  const int width = 32;
  const int height = 32;

  osg::ref_ptr<osg::Image> image = new osg::Image();
  image->allocateImage(width, height, 1, GL_RGB, GL_UNSIGNED_BYTE);

  unsigned char *data = image->data();

  // 创建红绿棋盘格纹理
  for (int y = 0; y < height; ++y)
  {
    for (int x = 0; x < width; ++x)
    {
      int index = (y * width + x) * 3;

      if ((x / 8 + y / 8) % 2 == 0)
      {
        // 红色方块
        data[index + 0] = 255; // R
        data[index + 1] = 0;   // G
        data[index + 2] = 0;   // B
      }
      else
      {
        // 绿色方块
        data[index + 0] = 0;   // R
        data[index + 1] = 255; // G
        data[index + 2] = 0;   // B
      }
    }
  }

  osg::ref_ptr<osg::Texture2D> texture = new osg::Texture2D();
  texture->setImage(image);

  // WebGL兼容的纹理参数设置
  std::cout << "[DEBUG] Setting WebGL-compatible texture parameters..." << std::endl;

  try
  {
    // 使用WebGL支持的纹理参数
    texture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
    texture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
    texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR);
    texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);

    // 禁用不支持的纹理参数
    texture->setUnRefImageDataAfterApply(false);

    std::cout << "[DEBUG] Texture parameters set successfully" << std::endl;
  }
  catch (const std::exception &e)
  {
    std::cout << "[ERROR] Exception setting texture parameters: " << e.what() << std::endl;
  }

  return texture;
}

// 创建简单的纹理四边形
osg::ref_ptr<osg::Geode> createTexturedQuad()
{
  std::cout << "[DEBUG] Creating textured quad geometry..." << std::endl;

  osg::ref_ptr<osg::Geode> geode = new osg::Geode();
  osg::ref_ptr<osg::Geometry> geometry = new osg::Geometry();

  // 顶点数组 (简单四边形)
  osg::ref_ptr<osg::Vec3Array> vertices = new osg::Vec3Array();
  vertices->push_back(osg::Vec3(-1.0f, -1.0f, 0.0f)); // 左下
  vertices->push_back(osg::Vec3(1.0f, -1.0f, 0.0f));  // 右下
  vertices->push_back(osg::Vec3(1.0f, 1.0f, 0.0f));   // 右上
  vertices->push_back(osg::Vec3(-1.0f, 1.0f, 0.0f));  // 左上
  geometry->setVertexArray(vertices);

  // 纹理坐标数组
  osg::ref_ptr<osg::Vec2Array> texCoords = new osg::Vec2Array();
  texCoords->push_back(osg::Vec2(0.0f, 0.0f)); // 左下
  texCoords->push_back(osg::Vec2(1.0f, 0.0f)); // 右下
  texCoords->push_back(osg::Vec2(1.0f, 1.0f)); // 右上
  texCoords->push_back(osg::Vec2(0.0f, 1.0f)); // 左上
  geometry->setTexCoordArray(0, texCoords);

  // 法线数组
  osg::ref_ptr<osg::Vec3Array> normals = new osg::Vec3Array();
  normals->push_back(osg::Vec3(0.0f, 0.0f, 1.0f));
  geometry->setNormalArray(normals);
  geometry->setNormalBinding(osg::Geometry::BIND_OVERALL);

  // 颜色数组 (添加白色顶点颜色作为备用)
  osg::ref_ptr<osg::Vec4Array> colors = new osg::Vec4Array();
  colors->push_back(osg::Vec4(1.0f, 1.0f, 1.0f, 1.0f)); // 白色
  geometry->setColorArray(colors);
  geometry->setColorBinding(osg::Geometry::BIND_OVERALL);

  // 索引数组
  osg::ref_ptr<osg::DrawElementsUInt> indices = new osg::DrawElementsUInt(osg::PrimitiveSet::TRIANGLES, 0);
  indices->push_back(0);
  indices->push_back(1);
  indices->push_back(2); // 第一个三角形
  indices->push_back(0);
  indices->push_back(2);
  indices->push_back(3); // 第二个三角形
  geometry->addPrimitiveSet(indices);

  // 创建并应用纹理
  osg::ref_ptr<osg::Texture2D> texture = createSimpleTestTexture();

  osg::ref_ptr<osg::StateSet> stateSet = geometry->getOrCreateStateSet();

  // 禁用可能导致问题的OpenGL状态
  stateSet->setMode(GL_LIGHTING, osg::StateAttribute::OFF | osg::StateAttribute::PROTECTED);
  stateSet->setMode(GL_CULL_FACE, osg::StateAttribute::OFF);
  stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);

  // 应用纹理
  std::cout << "[DEBUG] Applying texture to geometry..." << std::endl;
  stateSet->setTextureAttributeAndModes(0, texture, osg::StateAttribute::ON);

#ifdef __EMSCRIPTEN__
  // WebGL环境：添加简单的着色器程序
  std::cout << "[DEBUG] Creating simple shader program for WebGL..." << std::endl;

  // 顶点着色器 - 纯WebGL 1.0兼容语法
  const char *vertexShaderSource = R"(
precision highp float;

attribute vec3 osg_Vertex;
attribute vec2 osg_MultiTexCoord0;
attribute vec4 osg_Color;

uniform mat4 osg_ModelViewProjectionMatrix;

varying vec2 texCoord;
varying vec4 vertexColor;

void main()
{
    gl_Position = osg_ModelViewProjectionMatrix * vec4(osg_Vertex, 1.0);
    texCoord = osg_MultiTexCoord0;
    vertexColor = osg_Color;
}
)";

  // 片段着色器 - 简化版本，确保WebGL 1.0兼容
  const char *fragmentShaderSource = R"(
precision mediump float;

varying vec2 texCoord;
varying vec4 vertexColor;

uniform sampler2D texture0;

void main()
{
    vec4 texColor = texture2D(texture0, texCoord);
    
    // 简单的棋盘格纹理效果，如果纹理加载失败则显示
    float checker = mod(floor(texCoord.x * 8.0) + floor(texCoord.y * 8.0), 2.0);
    vec4 checkerColor = mix(vec4(0.8, 0.8, 0.8, 1.0), vec4(0.2, 0.2, 0.2, 1.0), checker);
    
    // 混合纹理和棋盘格
    gl_FragColor = mix(checkerColor, texColor, 0.5) * vertexColor;
}
)";

  osg::ref_ptr<osg::Program> program = new osg::Program();
  program->addShader(new osg::Shader(osg::Shader::VERTEX, vertexShaderSource));
  program->addShader(new osg::Shader(osg::Shader::FRAGMENT, fragmentShaderSource));

  stateSet->setAttributeAndModes(program, osg::StateAttribute::ON);

  // 绑定纹理到uniform
  stateSet->addUniform(new osg::Uniform("texture0", 0));

  std::cout << "[DEBUG] Shader program created and applied" << std::endl;
#endif

  // 不使用TexEnv和Material，避免WebGL兼容性问题
  std::cout << "[DEBUG] Skipping TexEnv and Material settings for WebGL compatibility" << std::endl;

  // 强制使用VBO
  geometry->setUseVertexBufferObjects(true);
  geometry->setUseDisplayList(false);
  geometry->setDataVariance(osg::Object::STATIC);

  geode->addDrawable(geometry);

  std::cout << "[DEBUG] Textured quad created successfully" << std::endl;
  std::cout << "[DEBUG] Vertices: " << vertices->size() << ", TexCoords: " << texCoords->size() << std::endl;

  return geode;
}

// 主循环函数
void mainLoop()
{
  if (!g_running || !g_viewer)
  {
    return;
  }

#ifdef __EMSCRIPTEN__
  // 确保WebGL上下文仍然有效
  if (g_webgl_context > 0)
  {
    emscripten_webgl_make_context_current(g_webgl_context);
  }
#endif

  // 渲染一帧
  g_viewer->frame();
}

// 主函数
int main()
{
  std::cout << "=== OSG Texture Test for WebAssembly ===" << std::endl;
  std::cout << "[INFO] Testing OSG texture system compatibility with WebGL" << std::endl;

  try
  {
#ifdef __EMSCRIPTEN__
    // 首先初始化WebGL上下文
    if (!initializeWebGL())
    {
      std::cout << "[ERROR] Failed to initialize WebGL context" << std::endl;
      return -1;
    }

    // 初始化WindowingSystemInterface
    initializeWindowingSystem();
#endif

    // 创建OSG Viewer
    g_viewer = new osgViewer::Viewer();

#ifdef __EMSCRIPTEN__
    // 为WebAssembly环境创建专用的GraphicsContext
    osg::ref_ptr<osg::GraphicsContext::Traits> traits = new osg::GraphicsContext::Traits();
    traits->x = 0;
    traits->y = 0;
    traits->width = 800;
    traits->height = 600;
    traits->red = 8;
    traits->green = 8;
    traits->blue = 8;
    traits->alpha = 8;
    traits->depth = 24;
    traits->stencil = 0;
    traits->windowDecoration = false;
    traits->doubleBuffer = true;
    traits->sharedContext = nullptr;
    traits->setInheritedWindowPixelFormat = false;

    std::cout << "[INFO] Creating EmscriptenGraphicsContext..." << std::endl;
    osg::ref_ptr<EmscriptenGraphicsContext> gc = new EmscriptenGraphicsContext(traits.get());

    if (gc.valid())
    {
      std::cout << "[INFO] EmscriptenGraphicsContext created successfully" << std::endl;

      // 设置相机使用我们的GraphicsContext
      osg::Camera *camera = g_viewer->getCamera();
      camera->setGraphicsContext(gc.get());
      camera->setViewport(new osg::Viewport(0, 0, 800, 600));
      camera->setProjectionMatrixAsPerspective(45.0, 800.0 / 600.0, 0.1, 100.0);
      camera->setViewMatrixAsLookAt(osg::Vec3(0, 0, 3), osg::Vec3(0, 0, 0), osg::Vec3(0, 1, 0));

      std::cout << "[INFO] Camera configured with EmscriptenGraphicsContext" << std::endl;
    }
    else
    {
      std::cout << "[ERROR] Failed to create EmscriptenGraphicsContext" << std::endl;
      return -1;
    }
#else
    // 桌面环境：使用默认设置
    g_viewer->setUpViewerAsEmbeddedInWindow(0, 0, 800, 600);

    // 设置相机
    osg::Camera *camera = g_viewer->getCamera();
    camera->setViewport(0, 0, 800, 600);
    camera->setProjectionMatrixAsPerspective(45.0, 800.0 / 600.0, 0.1, 100.0);
    camera->setViewMatrixAsLookAt(osg::Vec3(0, 0, 3), osg::Vec3(0, 0, 0), osg::Vec3(0, 1, 0));
#endif

    // 创建场景
    osg::ref_ptr<osg::Group> root = new osg::Group();
    osg::ref_ptr<osg::Geode> texturedQuad = createTexturedQuad();
    root->addChild(texturedQuad);

    // 设置场景数据
    g_viewer->setSceneData(root);

    // 初始化viewer
    std::cout << "[INFO] Realizing OSG Viewer..." << std::endl;
    g_viewer->realize();

    std::cout << "[INFO] OSG Viewer initialized successfully" << std::endl;

#ifdef __EMSCRIPTEN__
    // WebAssembly环境：设置主循环 (使用requestAnimationFrame)
    emscripten_set_main_loop(mainLoop, 0, 1);
#else
    // 桌面环境：运行循环
    while (!g_viewer->done())
    {
      g_viewer->frame();
    }
#endif
  }
  catch (const std::exception &e)
  {
    std::cout << "[ERROR] Exception in main: " << e.what() << std::endl;
    return -1;
  }

  std::cout << "[INFO] OSG Texture Test completed" << std::endl;
  return 0;
}