# Enhanced OSG Earth Sample - 桌面版构建脚本
# PowerShell脚本，用于自动化桌面版的构建过程

param(
    [string]$BuildType = "Release",
    [switch]$Clean = $false,
    [switch]$Install = $false,
    [switch]$Run = $false,
    [switch]$Help = $false
)

# 显示帮助信息
if ($Help) {
    Write-Host "Enhanced OSG Earth Sample - 桌面版构建脚本" -ForegroundColor Green
    Write-Host ""
    Write-Host "用法: .\build_desktop.ps1 [选项]"
    Write-Host ""
    Write-Host "选项:"
    Write-Host "  -BuildType <类型>  构建类型 (Debug/Release) [默认: Release]"
    Write-Host "  -Clean             清理构建目录"
    Write-Host "  -Install           安装到redist_desk目录"
    Write-Host "  -Run               构建完成后运行程序"
    Write-Host "  -Help              显示此帮助信息"
    Write-Host ""
    Write-Host "示例:"
    Write-Host "  .\build_desktop.ps1                    # 标准构建"
    Write-Host "  .\build_desktop.ps1 -Clean -Install   # 清理构建并安装"
    Write-Host "  .\build_desktop.ps1 -BuildType Debug  # 调试构建"
    Write-Host "  .\build_desktop.ps1 -Run              # 构建并运行"
    exit 0
}

# 脚本配置
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = (Get-Item $ScriptDir).Parent.Parent.Parent.FullName
$BuildDir = Join-Path $ScriptDir "build_desk"
$RedistDir = Join-Path $ProjectRoot "redist_desk"

# 环境检查
Write-Host "Enhanced OSG Earth Sample - 桌面版构建" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green
Write-Host "项目目录: $ScriptDir"
Write-Host "项目根目录: $ProjectRoot"
Write-Host "构建目录: $BuildDir"
Write-Host "构建类型: $BuildType"
Write-Host ""

# 检查必要工具
Write-Host "检查构建环境..." -ForegroundColor Yellow

# 检查CMake
try {
    $cmakeVersion = & cmake --version 2>$null | Select-Object -First 1
    Write-Host "✓ CMake: $cmakeVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ CMake 未找到，请安装CMake" -ForegroundColor Red
    exit 1
}

# 检查编译器
$compiler = ""
if (Get-Command "cl.exe" -ErrorAction SilentlyContinue) {
    $compiler = "MSVC"
    Write-Host "✓ 编译器: Microsoft Visual C++" -ForegroundColor Green
} elseif (Get-Command "g++.exe" -ErrorAction SilentlyContinue) {
    $compiler = "GCC"
    Write-Host "✓ 编译器: GNU GCC" -ForegroundColor Green
} elseif (Get-Command "clang++.exe" -ErrorAction SilentlyContinue) {
    $compiler = "Clang"
    Write-Host "✓ 编译器: Clang" -ForegroundColor Green
} else {
    Write-Host "✗ 未找到C++编译器" -ForegroundColor Red
    exit 1
}

# 检查VCPKG（如果在Windows上）
if ($IsWindows -or ($env:OS -eq "Windows_NT")) {
    if ($env:VCPKG_ROOT) {
        Write-Host "✓ VCPKG: $env:VCPKG_ROOT" -ForegroundColor Green
        $env:CMAKE_TOOLCHAIN_FILE = "$env:VCPKG_ROOT\scripts\buildsystems\vcpkg.cmake"
    } else {
        Write-Host "⚠ VCPKG_ROOT 环境变量未设置，可能无法找到依赖库" -ForegroundColor Yellow
    }
}

Write-Host ""

# 清理构建目录
if ($Clean) {
    Write-Host "清理构建目录..." -ForegroundColor Yellow
    if (Test-Path $BuildDir) {
        Remove-Item $BuildDir -Recurse -Force
        Write-Host "✓ 构建目录已清理" -ForegroundColor Green
    }
}

# 创建构建目录
if (-not (Test-Path $BuildDir)) {
    New-Item -Path $BuildDir -ItemType Directory -Force | Out-Null
    Write-Host "✓ 创建构建目录: $BuildDir" -ForegroundColor Green
}

# 进入构建目录
Push-Location $BuildDir

try {
    # CMake配置
    Write-Host "配置CMake..." -ForegroundColor Yellow
    
    $cmakeArgs = @(
        ".."
        "-DCMAKE_BUILD_TYPE=$BuildType"
        "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON"
    )
    
    # 添加VCPKG工具链文件
    if ($env:CMAKE_TOOLCHAIN_FILE) {
        $cmakeArgs += "-DCMAKE_TOOLCHAIN_FILE=$env:CMAKE_TOOLCHAIN_FILE"
    }
    
    # 生成器选择
    if ($compiler -eq "MSVC") {
        $cmakeArgs += "-G", "Visual Studio 17 2022"
        $cmakeArgs += "-A", "x64"
    }
    
    Write-Host "CMake命令: cmake $($cmakeArgs -join ' ')" -ForegroundColor Gray
    
    $configResult = & cmake @cmakeArgs
    if ($LASTEXITCODE -ne 0) {
        Write-Host "✗ CMake配置失败" -ForegroundColor Red
        Write-Host $configResult
        exit 1
    }
    Write-Host "✓ CMake配置成功" -ForegroundColor Green
    
    # 构建项目
    Write-Host "构建项目..." -ForegroundColor Yellow
    
    $buildArgs = @(
        "--build", "."
        "--config", $BuildType
    )
    
    # 添加并行构建
    if ($compiler -eq "MSVC") {
        $buildArgs += "--parallel"
    } else {
        $buildArgs += "--parallel", [Environment]::ProcessorCount
    }
    
    Write-Host "构建命令: cmake $($buildArgs -join ' ')" -ForegroundColor Gray
    
    $buildResult = & cmake @buildArgs
    if ($LASTEXITCODE -ne 0) {
        Write-Host "✗ 构建失败" -ForegroundColor Red
        Write-Host $buildResult
        exit 1
    }
    Write-Host "✓ 构建成功" -ForegroundColor Green
    
    # 查找生成的可执行文件
    $executableName = "enhanced_osg_earth_sample"
    if ($IsWindows -or ($env:OS -eq "Windows_NT")) {
        $executableName += ".exe"
    }
    
    $executablePath = Get-ChildItem -Path . -Name $executableName -Recurse | Select-Object -First 1
    if ($executablePath) {
        $fullExecutablePath = Join-Path $BuildDir $executablePath
        Write-Host "✓ 可执行文件: $fullExecutablePath" -ForegroundColor Green
        
        # 获取文件信息
        $fileInfo = Get-Item $fullExecutablePath
        Write-Host "  文件大小: $([math]::Round($fileInfo.Length / 1MB, 2)) MB" -ForegroundColor Gray
        Write-Host "  修改时间: $($fileInfo.LastWriteTime)" -ForegroundColor Gray
    }
    
    # 部署到redist_desk目录
    if ($Install) {
        Write-Host "部署到redist_desk目录..." -ForegroundColor Yellow
        
        $deployResult = & cmake --build . --target deploy_desk --config $BuildType
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ 部署成功" -ForegroundColor Green
            Write-Host "  部署目录: $RedistDir" -ForegroundColor Gray
        } else {
            Write-Host "⚠ 部署失败，手动复制文件" -ForegroundColor Yellow
            
            # 手动复制
            $redistBinDir = Join-Path $RedistDir "bin"
            if (-not (Test-Path $redistBinDir)) {
                New-Item -Path $redistBinDir -ItemType Directory -Force | Out-Null
            }
            
            if ($executablePath) {
                Copy-Item $fullExecutablePath $redistBinDir -Force
                Write-Host "✓ 手动复制完成" -ForegroundColor Green
            }
        }
    }
    
} finally {
    # 回到原目录
    Pop-Location
}

# 显示构建总结
Write-Host ""
Write-Host "构建总结:" -ForegroundColor Green
Write-Host "=========" -ForegroundColor Green
Write-Host "构建类型: $BuildType"
Write-Host "编译器: $compiler"
Write-Host "构建目录: $BuildDir"
if ($Install) {
    Write-Host "部署目录: $RedistDir"
}

# 运行程序
if ($Run -and $Install) {
    Write-Host ""
    Write-Host "运行程序..." -ForegroundColor Yellow
    
    $redistExecutable = Join-Path $RedistDir "bin" $executableName
    if (Test-Path $redistExecutable) {
        Write-Host "启动: $redistExecutable" -ForegroundColor Gray
        Start-Process $redistExecutable
    } else {
        Write-Host "✗ 无法找到可执行文件: $redistExecutable" -ForegroundColor Red
    }
} elseif ($Run) {
    Write-Host ""
    Write-Host "⚠ 要运行程序，请添加 -Install 参数" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "构建完成!" -ForegroundColor Green 