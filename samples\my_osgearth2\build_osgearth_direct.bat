@echo off
chcp 65001
echo ========================================
echo OSGEarth WebAssembly Direct Build
echo ========================================

call C:\dev\emsdk\emsdk_env.bat

set THIRD_PARTY_DIR=E:\project\my-earth202507\thirid_party\wasm_dep
set OSGEARTH_SRC_DIR=..\..\src
set OUTPUT_DIR=redist_wasm

echo Checking dependencies...
if not exist "%THIRD_PARTY_DIR%\lib\libosg.a" (
    echo Error: OSG library not found
    pause
    exit /b 1
)
echo Dependencies found!

if not exist %OUTPUT_DIR% mkdir %OUTPUT_DIR%

echo Compiling OSGEarth WebAssembly project...
C:\dev\emsdk\python\3.13.3_64bit\python.exe C:\dev\emsdk\upstream\emscripten\emcc.py osgearth_myviewer_wasm.cpp -o %OUTPUT_DIR%\osgearth_myviewer_wasm.html -I%THIRD_PARTY_DIR%\include -I%OSGEARTH_SRC_DIR% -L%THIRD_PARTY_DIR%\lib -losg -losgViewer -losgGA -losgDB -losgUtil -lOpenThreads -losgText -losgSim -losgShadow -lGeographicLib -s WASM=1 -s USE_WEBGL2=1 -s FULL_ES3=1 -s USE_PTHREADS=1 -s PTHREAD_POOL_SIZE=8 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=268435456 -s SHARED_MEMORY=1 -s FETCH=1 -s NO_EXIT_RUNTIME=1 -s EXPORTED_FUNCTIONS="['_main']" -s EXPORTED_RUNTIME_METHODS="['ccall','cwrap']" -s DISABLE_EXCEPTION_CATCHING=0 -std=c++20 -pthread -DOSG_THREADING_ENABLED

if %ERRORLEVEL% neq 0 (
    echo Compilation failed
    pause
    exit /b 1
)

echo OSGEarth WebAssembly project compiled successfully!
echo Files generated:
dir %OUTPUT_DIR%\osgearth_myviewer_wasm.*

echo.
echo To test, run a local server:
echo   cd %OUTPUT_DIR%
echo   python -m http.server 8080
echo   Then open http://localhost:8080/osgearth_myviewer_wasm.html

pause
