<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>OSG WebGL 简单测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #000;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            font-size: 24px;
            color: #4CAF50;
        }
        
        .container {
            display: flex;
            gap: 20px;
        }
        
        .canvas-container {
            flex: 1;
            background-color: #111;
            border: 2px solid #333;
            border-radius: 8px;
            overflow: hidden;
        }
        
        #canvas {
            width: 100%;
            height: 600px;
            display: block;
            background-color: #000;
        }
        
        .info {
            width: 300px;
            background-color: #222;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #333;
        }
        
        .status {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #333;
            border-radius: 5px;
        }
        
        .status-label {
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 5px;
        }
        
        .status-value {
            font-family: monospace;
            font-size: 12px;
            color: #ccc;
        }
        
        #output {
            width: 100%;
            height: 150px;
            background-color: #000;
            color: #0f0;
            font-family: monospace;
            font-size: 11px;
            border: 1px solid #333;
            padding: 5px;
            overflow-y: auto;
            resize: vertical;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #ff9800;
        }
    </style>
</head>
<body>
    <div class="header">OSG WebGL 简单测试 - 验证基本渲染</div>
    
    <div class="container">
        <div class="canvas-container">
            <canvas id="canvas" oncontextmenu="event.preventDefault()"></canvas>
        </div>
        
        <div class="info">
            <div class="status">
                <div class="status-label">加载状态</div>
                <div id="status" class="status-value">初始化中...</div>
            </div>
            
            <div class="status">
                <div class="status-label">渲染状态</div>
                <div id="render-status" class="status-value">等待中...</div>
            </div>
            
            <div class="status">
                <div class="status-label">操作说明</div>
                <div class="status-value">
                    • 鼠标拖拽: 旋转地球<br>
                    • 滚轮: 缩放<br>
                    • 预期: 蓝色地球球体
                </div>
            </div>
            
            <div class="status">
                <div class="status-label">控制台输出</div>
                <textarea id="output" readonly></textarea>
            </div>
        </div>
    </div>

    <script>
        // 获取元素
        var statusElement = document.getElementById("status");
        var renderStatusElement = document.getElementById("render-status");
        var canvasElement = document.getElementById("canvas");
        var outputElement = document.getElementById("output");
        
        // 输出函数
        function addOutput(text) {
            console.log(text);
            if (outputElement) {
                outputElement.value += text + '\n';
                outputElement.scrollTop = outputElement.scrollHeight;
            }
        }
        
        // 状态监控
        function startMonitoring() {
            setInterval(function() {
                if (window.Module) {
                    if (typeof window.Module.getRenderStatus === 'function') {
                        try {
                            var status = window.Module.getRenderStatus();
                            renderStatusElement.textContent = status;
                        } catch (e) {
                            renderStatusElement.textContent = '状态获取失败';
                        }
                    } else {
                        renderStatusElement.textContent = 'OSG 渲染中...';
                    }
                }
            }, 1000);
        }
        
        // Module 配置
        var Module = {
            canvas: canvasElement,
            
            print: function(text) {
                addOutput('[OSG] ' + text);
            },
            
            printErr: function(text) {
                addOutput('[ERROR] ' + text);
            },
            
            setStatus: function(text) {
                addOutput('[STATUS] ' + text);
                statusElement.textContent = text;
            },
            
            onRuntimeInitialized: function() {
                addOutput('[INFO] OSG WebGL 运行时初始化完成');
                statusElement.textContent = '运行时就绪';
                startMonitoring();
            },
            
            postRun: [function() {
                addOutput('[INFO] OSG WebGL 模块 postRun 完成');
                statusElement.textContent = '加载完成';
            }],
            
            totalDependencies: 0,
            
            monitorRunDependencies: function(left) {
                this.totalDependencies = Math.max(this.totalDependencies, left);
                Module.setStatus(left ? 
                    'Preparing... (' + (this.totalDependencies - left) + '/' + this.totalDependencies + ')' : 
                    'All downloads complete.');
            }
        };
        
        // 设置初始状态
        Module.setStatus('Downloading...');
        
        // 错误处理
        window.onerror = function(event) {
            addOutput('[JS ERROR] ' + event);
            Module.setStatus('Exception thrown, see console');
        };
        
        // Canvas 事件
        canvasElement.addEventListener("webglcontextlost", function(e) {
            addOutput('[WEBGL] Context lost - page reload required');
            alert('WebGL context lost. You will need to reload the page.');
            e.preventDefault();
        }, false);
        
        addOutput('[INFO] 页面初始化完成，开始加载 OSG WebGL 模块...');
    </script>
    
    <!-- 加载 WebAssembly 模块 -->
    <script async src="simple_webgl_test.js"></script>
</body>
</html>
