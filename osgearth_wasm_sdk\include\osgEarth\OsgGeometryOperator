/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/Common>
#include <osgEarth/Stroke>
#include <osg/Array>
#include <osg/Geometry>

// Interface for operators that lists of vectors and normals and turn
// them into polygonized geometry.

namespace osgEarth { namespace Util {
    class OSGEARTH_EXPORT OsgGeometryOperator : public osg::Referenced
    {
    public:
        struct Callback {
            virtual void operator()(unsigned i) = 0;
        };
                /**
         * Run the polygonizer.
         *
         * @param[in ] verts    Line string geometry to polygonize. The polygonizer
         *                      will add this array to the resulting geometry.
         * @param[in ] normals  Localized normals associated with the input verts.
         *                      Used to determine the plane in which to polygonize each
         *                      line segment. Optional; can be NULL
         * @param[in ] callback Called for each new point added to the polygonized line
         *                      with the source line index. Optional, call be NULL.
         * @param[in ] twosided Generate polygons on both sides of the center line.
         *
         * @return Triangulated geometry, including primitive set
         */
        virtual osg::Geometry* operator()(osg::Vec3Array* verts, osg::Vec3Array* normals, float width, Callback* callback =0L, bool twosided =true) const = 0;
        /**
         * Installs a shader on a stateset.
         */
        virtual void installShaders(osg::Node* node) const = 0;
    };
}}
