<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OSGEarth Digital Earth WebGL Demo - Fixed</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            overflow: hidden;
        }

        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            z-index: 1000;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .controls {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .btn {
            padding: 8px 16px;
            background: rgba(0, 212, 255, 0.2);
            border: 1px solid #00d4ff;
            border-radius: 6px;
            color: #00d4ff;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn:hover {
            background: rgba(0, 212, 255, 0.3);
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        .status {
            font-size: 12px;
            color: #888;
        }

        .canvas-container {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            background: radial-gradient(circle at center, #001122 0%, #000000 100%);
        }

        #canvas {
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.2);
            background: #000;
        }

        .info-panel {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            max-width: 300px;
        }

        .info-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #00d4ff;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 12px;
        }

        .loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 2000;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(0, 212, 255, 0.3);
            border-top: 3px solid #00d4ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 18px;
            color: #00d4ff;
        }

        .console {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 400px;
            height: 200px;
            background: rgba(0, 0, 0, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            overflow-y: auto;
            display: none;
        }

        .console.show {
            display: block;
        }

        .console-line {
            margin-bottom: 2px;
            color: #ccc;
        }

        .console-line.error {
            color: #ff6666;
        }

        .console-line.success {
            color: #66ff66;
        }

        .console-line.info {
            color: #66ccff;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🌍 OSGEarth Digital Earth - Fixed</div>
        <div class="controls">
            <div class="status" id="status">初始化中...</div>
            <button class="btn" onclick="toggleConsole()">控制台</button>
            <button class="btn" onclick="resetView()">重置视角</button>
            <button class="btn" onclick="toggleFullscreen()">全屏</button>
        </div>
    </div>

    <div class="canvas-container">
        <canvas id="canvas" width="800" height="600"></canvas>
    </div>

    <div class="info-panel">
        <div class="info-title">系统信息</div>
        <div class="info-item">
            <span>WebGL版本:</span>
            <span id="webgl-version">检测中...</span>
        </div>
        <div class="info-item">
            <span>渲染器:</span>
            <span id="renderer">检测中...</span>
        </div>
        <div class="info-item">
            <span>帧率:</span>
            <span id="fps">0 FPS</span>
        </div>
        <div class="info-item">
            <span>内存使用:</span>
            <span id="memory">0 MB</span>
        </div>
        <div class="info-item">
            <span>状态:</span>
            <span id="app-status">加载中</span>
        </div>
    </div>

    <div class="loading" id="loading">
        <div class="spinner"></div>
        <div class="loading-text">正在加载数字地球...</div>
        <div id="loading-progress" style="margin-top: 10px; font-size: 14px; color: #888;"></div>
    </div>

    <div class="console" id="console">
        <div style="color: #00d4ff; margin-bottom: 10px; border-bottom: 1px solid #333; padding-bottom: 5px;">
            🖥️ 系统控制台 - Fixed Version
        </div>
        <div id="console-content"></div>
    </div>

    <!-- 在加载WebAssembly之前设置所有JavaScript函数 -->
    <script>
        let startTime = Date.now();
        let frameCount = 0;
        let lastFpsUpdate = Date.now();

        // 控制台管理
        function addConsoleLog(message, type = 'info') {
            const console = document.getElementById('console-content');
            const line = document.createElement('div');
            line.className = `console-line ${type}`;
            line.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            console.appendChild(line);
            console.scrollTop = console.scrollHeight;
        }

        function toggleConsole() {
            const console = document.getElementById('console');
            console.classList.toggle('show');
        }

        // WebGL信息检测
        function detectWebGLInfo() {
            try {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
                if (gl) {
                    const version = gl.getParameter(gl.VERSION);
                    const renderer = gl.getParameter(gl.RENDERER);
                    document.getElementById('webgl-version').textContent = version.includes('WebGL 2') ? 'WebGL 2.0' : 'WebGL 1.0';
                    document.getElementById('renderer').textContent = renderer;
                    addConsoleLog(`WebGL检测成功: ${version}`, 'success');
                    addConsoleLog(`渲染器: ${renderer}`, 'info');
                } else {
                    throw new Error('WebGL不支持');
                }
            } catch (e) {
                document.getElementById('webgl-version').textContent = '不支持';
                document.getElementById('renderer').textContent = '未知';
                addConsoleLog(`WebGL检测失败: ${e.message}`, 'error');
            }
        }

        // 性能监控
        function updatePerformanceInfo() {
            frameCount++;
            const now = Date.now();
            
            if (now - lastFpsUpdate > 1000) {
                const fps = Math.round(frameCount * 1000 / (now - lastFpsUpdate));
                document.getElementById('fps').textContent = `${fps} FPS`;
                frameCount = 0;
                lastFpsUpdate = now;
            }

            // 内存使用情况
            if (performance.memory) {
                const memoryMB = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
                document.getElementById('memory').textContent = `${memoryMB} MB`;
            }

            requestAnimationFrame(updatePerformanceInfo);
        }

        // 应用控制
        function resetView() {
            addConsoleLog('重置视角', 'info');
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
                addConsoleLog('进入全屏模式', 'info');
            } else {
                document.exitFullscreen();
                addConsoleLog('退出全屏模式', 'info');
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            addConsoleLog('页面加载完成 - Fixed Version', 'success');
            detectWebGLInfo();
            updatePerformanceInfo();
            
            // 更新状态
            document.getElementById('status').textContent = '准备就绪';
            document.getElementById('app-status').textContent = '运行中';
            
            // 隐藏加载界面
            setTimeout(() => {
                document.getElementById('loading').style.display = 'none';
                addConsoleLog('数字地球加载完成', 'success');
            }, 2000);
        });

        // WebAssembly模块配置 - 在全局作用域中设置
        var Module = {
            canvas: (function() {
                return document.getElementById('canvas');
            })(),
            print: function(text) {
                console.log('[WASM] ' + text);
                if (typeof addConsoleLog === 'function') {
                    addConsoleLog(text, 'info');
                }
            },
            printErr: function(text) {
                console.error('[WASM] ' + text);
                if (typeof addConsoleLog === 'function') {
                    addConsoleLog(text, 'error');
                }
            },
            onRuntimeInitialized: function() {
                console.log('[WASM] Runtime initialized');
                if (typeof addConsoleLog === 'function') {
                    addConsoleLog('WebAssembly运行时初始化完成', 'success');
                    document.getElementById('app-status').textContent = '已就绪';
                }
            },
            onAbort: function(what) {
                console.error('[WASM] Aborted: ' + what);
                if (typeof addConsoleLog === 'function') {
                    addConsoleLog('WebAssembly运行时中止: ' + what, 'error');
                }
            }
        };

        // 确保canvas在Module设置时就存在
        if (!Module.canvas) {
            console.warn('Canvas not found during Module setup, will retry...');
        }
    </script>

    <!-- 加载WebAssembly模块 -->
    <script src="osgearth_digital_earth_webgl_fixed.js"></script>
</body>
</html>
