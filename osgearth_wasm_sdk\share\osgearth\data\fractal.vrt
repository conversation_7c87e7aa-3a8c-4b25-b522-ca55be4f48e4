<VRTDataset rasterXSize="300" rasterYSize="225">
  <SRS dataAxisToSRSAxisMapping="2,1">GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223563,AUTH<PERSON>IT<PERSON>["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PR<PERSON><PERSON>["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.0174532925199433,AUTHORITY["EPSG","9122"]],AXIS["Latitude",NORTH],AXIS["Longitude",EAST],AUTHORITY["EPSG","4326"]]</SRS>
  <GeoTransform> -1.8000000000000000e+02,  1.2000000000000000e+00,  0.0000000000000000e+00,  9.0000000000000000e+01,  0.0000000000000000e+00, -8.0000000000000004e-01</GeoTransform>
  <Metadata>
    <MDI key="Software">paint.net 4.1</MDI>
  </Metadata>
  <VRTRasterBand dataType="Byte" band="1">
    <NoDataValue>255</NoDataValue>
    <ColorInterp>Palette</ColorInterp>
    <ColorTable>
      <Entry c1="0" c2="0" c3="0" c4="255" />
      <Entry c1="23" c2="0" c3="0" c4="255" />
      <Entry c1="23" c2="16" c3="13" c4="255" />
      <Entry c1="26" c2="24" c3="24" c4="255" />
      <Entry c1="43" c2="6" c3="4" c4="255" />
      <Entry c1="41" c2="33" c3="29" c4="255" />
      <Entry c1="55" c2="32" c3="14" c4="255" />
      <Entry c1="56" c2="35" c3="26" c4="255" />
      <Entry c1="41" c2="40" c3="40" c4="255" />
      <Entry c1="54" c2="42" c3="38" c4="255" />
      <Entry c1="56" c2="49" c3="44" c4="255" />
      <Entry c1="55" c2="55" c3="55" c4="255" />
      <Entry c1="80" c2="14" c3="6" c4="255" />
      <Entry c1="76" c2="36" c3="14" c4="255" />
      <Entry c1="72" c2="39" c3="25" c4="255" />
      <Entry c1="76" c2="49" c3="27" c4="255" />
      <Entry c1="90" c2="34" c3="13" c4="255" />
      <Entry c1="87" c2="40" c3="22" c4="255" />
      <Entry c1="90" c2="49" c3="28" c4="255" />
      <Entry c1="69" c2="44" c3="34" c4="255" />
      <Entry c1="72" c2="51" c3="40" c4="255" />
      <Entry c1="70" c2="59" c3="54" c4="255" />
      <Entry c1="85" c2="46" c3="33" c4="255" />
      <Entry c1="88" c2="54" c3="38" c4="255" />
      <Entry c1="86" c2="59" c3="50" c4="255" />
      <Entry c1="104" c2="1" c3="0" c4="255" />
      <Entry c1="102" c2="24" c3="10" c4="255" />
      <Entry c1="102" c2="28" c3="17" c4="255" />
      <Entry c1="118" c2="2" c3="1" c4="255" />
      <Entry c1="120" c2="22" c3="10" c4="255" />
      <Entry c1="118" c2="28" c3="17" c4="255" />
      <Entry c1="103" c2="35" c3="13" c4="255" />
      <Entry c1="103" c2="40" c3="20" c4="255" />
      <Entry c1="105" c2="48" c3="15" c4="255" />
      <Entry c1="104" c2="52" c3="27" c4="255" />
      <Entry c1="119" c2="37" c3="13" c4="255" />
      <Entry c1="119" c2="41" c3="20" c4="255" />
      <Entry c1="120" c2="53" c3="26" c4="255" />
      <Entry c1="99" c2="45" c3="33" c4="255" />
      <Entry c1="103" c2="57" c3="37" c4="255" />
      <Entry c1="104" c2="61" c3="49" c4="255" />
      <Entry c1="119" c2="59" c3="36" c4="255" />
      <Entry c1="114" c2="60" c3="50" c4="255" />
      <Entry c1="73" c2="65" c3="61" c4="255" />
      <Entry c1="90" c2="65" c3="44" c4="255" />
      <Entry c1="88" c2="68" c3="56" c4="255" />
      <Entry c1="121" c2="66" c3="28" c4="255" />
      <Entry c1="106" c2="66" c3="42" c4="255" />
      <Entry c1="103" c2="71" c3="55" c4="255" />
      <Entry c1="109" c2="82" c3="63" c4="255" />
      <Entry c1="120" c2="69" c3="41" c4="255" />
      <Entry c1="119" c2="73" c3="54" c4="255" />
      <Entry c1="122" c2="82" c3="43" c4="255" />
      <Entry c1="122" c2="82" c3="59" c4="255" />
      <Entry c1="70" c2="70" c3="70" c4="255" />
      <Entry c1="85" c2="75" c3="70" c4="255" />
      <Entry c1="90" c2="81" c3="76" c4="255" />
      <Entry c1="87" c2="86" c3="86" c4="255" />
      <Entry c1="95" c2="96" c3="95" c4="255" />
      <Entry c1="102" c2="76" c3="68" c4="255" />
      <Entry c1="104" c2="84" c3="73" c4="255" />
      <Entry c1="103" c2="91" c3="86" c4="255" />
      <Entry c1="115" c2="77" c3="65" c4="255" />
      <Entry c1="119" c2="87" c3="72" c4="255" />
      <Entry c1="116" c2="92" c3="83" c4="255" />
      <Entry c1="106" c2="97" c3="92" c4="255" />
      <Entry c1="124" c2="96" c3="76" c4="255" />
      <Entry c1="119" c2="99" c3="89" c4="255" />
      <Entry c1="102" c2="99" c3="98" c4="255" />
      <Entry c1="117" c2="105" c3="99" c4="255" />
      <Entry c1="126" c2="113" c3="103" c4="255" />
      <Entry c1="114" c2="113" c3="112" c4="255" />
      <Entry c1="141" c2="17" c3="7" c4="255" />
      <Entry c1="135" c2="37" c3="12" c4="255" />
      <Entry c1="135" c2="41" c3="20" c4="255" />
      <Entry c1="135" c2="54" c3="25" c4="255" />
      <Entry c1="151" c2="35" c3="13" c4="255" />
      <Entry c1="151" c2="40" c3="20" c4="255" />
      <Entry c1="152" c2="51" c3="14" c4="255" />
      <Entry c1="151" c2="56" c3="25" c4="255" />
      <Entry c1="134" c2="59" c3="34" c4="255" />
      <Entry c1="149" c2="60" c3="34" c4="255" />
      <Entry c1="179" c2="10" c3="4" c4="255" />
      <Entry c1="166" c2="36" c3="13" c4="255" />
      <Entry c1="166" c2="41" c3="20" c4="255" />
      <Entry c1="167" c2="48" c3="13" c4="255" />
      <Entry c1="167" c2="56" c3="24" c4="255" />
      <Entry c1="181" c2="37" c3="13" c4="255" />
      <Entry c1="182" c2="41" c3="19" c4="255" />
      <Entry c1="179" c2="55" c3="13" c4="255" />
      <Entry c1="182" c2="56" c3="24" c4="255" />
      <Entry c1="174" c2="60" c3="34" c4="255" />
      <Entry c1="135" c2="67" c3="28" c4="255" />
      <Entry c1="152" c2="67" c3="27" c4="255" />
      <Entry c1="154" c2="85" c3="29" c4="255" />
      <Entry c1="136" c2="69" c3="39" c4="255" />
      <Entry c1="134" c2="75" c3="53" c4="255" />
      <Entry c1="138" c2="82" c3="43" c4="255" />
      <Entry c1="135" c2="84" c3="56" c4="255" />
      <Entry c1="151" c2="71" c3="38" c4="255" />
      <Entry c1="151" c2="77" c3="49" c4="255" />
      <Entry c1="153" c2="83" c3="41" c4="255" />
      <Entry c1="151" c2="87" c3="54" c4="255" />
      <Entry c1="150" c2="99" c3="55" c4="255" />
      <Entry c1="167" c2="69" c3="27" c4="255" />
      <Entry c1="170" c2="80" c3="29" c4="255" />
      <Entry c1="183" c2="70" c3="28" c4="255" />
      <Entry c1="185" c2="86" c3="29" c4="255" />
      <Entry c1="167" c2="72" c3="36" c4="255" />
      <Entry c1="167" c2="70" c3="48" c4="255" />
      <Entry c1="168" c2="84" c3="41" c4="255" />
      <Entry c1="166" c2="89" c3="52" c4="255" />
      <Entry c1="183" c2="73" c3="35" c4="255" />
      <Entry c1="181" c2="78" c3="48" c4="255" />
      <Entry c1="183" c2="86" c3="39" c4="255" />
      <Entry c1="183" c2="90" c3="51" c4="255" />
      <Entry c1="185" c2="96" c3="31" c4="255" />
      <Entry c1="171" c2="97" c3="42" c4="255" />
      <Entry c1="169" c2="100" c3="56" c4="255" />
      <Entry c1="170" c2="113" c3="60" c4="255" />
      <Entry c1="185" c2="100" c3="43" c4="255" />
      <Entry c1="183" c2="103" c3="54" c4="255" />
      <Entry c1="181" c2="112" c3="45" c4="255" />
      <Entry c1="183" c2="116" c3="57" c4="255" />
      <Entry c1="131" c2="78" c3="65" c4="255" />
      <Entry c1="134" c2="89" c3="69" c4="255" />
      <Entry c1="131" c2="94" c3="81" c4="255" />
      <Entry c1="150" c2="91" c3="67" c4="255" />
      <Entry c1="144" c2="95" c3="80" c4="255" />
      <Entry c1="137" c2="99" c3="73" c4="255" />
      <Entry c1="135" c2="102" c3="87" c4="255" />
      <Entry c1="150" c2="100" c3="72" c4="255" />
      <Entry c1="149" c2="107" c3="86" c4="255" />
      <Entry c1="151" c2="114" c3="91" c4="255" />
      <Entry c1="133" c2="108" c3="99" c4="255" />
      <Entry c1="136" c2="115" c3="104" c4="255" />
      <Entry c1="137" c2="120" c3="112" c4="255" />
      <Entry c1="145" c2="110" c3="98" c4="255" />
      <Entry c1="151" c2="119" c3="103" c4="255" />
      <Entry c1="152" c2="124" c3="112" c4="255" />
      <Entry c1="167" c2="91" c3="66" c4="255" />
      <Entry c1="165" c2="104" c3="71" c4="255" />
      <Entry c1="166" c2="108" c3="84" c4="255" />
      <Entry c1="168" c2="115" c3="71" c4="255" />
      <Entry c1="165" c2="116" c3="87" c4="255" />
      <Entry c1="182" c2="107" c3="67" c4="255" />
      <Entry c1="184" c2="117" c3="70" c4="255" />
      <Entry c1="177" c2="113" c3="86" c4="255" />
      <Entry c1="164" c2="121" c3="101" c4="255" />
      <Entry c1="203" c2="12" c3="5" c4="255" />
      <Entry c1="209" c2="48" c3="21" c4="255" />
      <Entry c1="207" c2="58" c3="33" c4="255" />
      <Entry c1="248" c2="19" c3="4" c4="255" />
      <Entry c1="241" c2="49" c3="18" c4="255" />
      <Entry c1="231" c2="58" c3="33" c4="255" />
      <Entry c1="199" c2="71" c3="27" c4="255" />
      <Entry c1="199" c2="84" c3="29" c4="255" />
      <Entry c1="215" c2="71" c3="27" c4="255" />
      <Entry c1="214" c2="85" c3="29" c4="255" />
      <Entry c1="198" c2="73" c3="36" c4="255" />
      <Entry c1="199" c2="87" c3="39" c4="255" />
      <Entry c1="198" c2="91" c3="50" c4="255" />
      <Entry c1="215" c2="73" c3="35" c4="255" />
      <Entry c1="208" c2="77" c3="48" c4="255" />
      <Entry c1="214" c2="88" c3="39" c4="255" />
      <Entry c1="214" c2="92" c3="49" c4="255" />
      <Entry c1="223" c2="96" c3="30" c4="255" />
      <Entry c1="201" c2="100" c3="42" c4="255" />
      <Entry c1="199" c2="104" c3="52" c4="255" />
      <Entry c1="202" c2="113" c3="42" c4="255" />
      <Entry c1="201" c2="118" c3="57" c4="255" />
      <Entry c1="216" c2="101" c3="42" c4="255" />
      <Entry c1="215" c2="104" c3="52" c4="255" />
      <Entry c1="219" c2="116" c3="44" c4="255" />
      <Entry c1="217" c2="118" c3="55" c4="255" />
      <Entry c1="244" c2="76" c3="24" c4="255" />
      <Entry c1="236" c2="85" c3="38" c4="255" />
      <Entry c1="254" c2="108" c3="16" c4="255" />
      <Entry c1="231" c2="103" c3="41" c4="255" />
      <Entry c1="231" c2="105" c3="52" c4="255" />
      <Entry c1="233" c2="117" c3="44" c4="255" />
      <Entry c1="232" c2="119" c3="54" c4="255" />
      <Entry c1="248" c2="103" c3="40" c4="255" />
      <Entry c1="246" c2="106" c3="52" c4="255" />
      <Entry c1="249" c2="119" c3="41" c4="255" />
      <Entry c1="246" c2="120" c3="53" c4="255" />
      <Entry c1="207" c2="121" c3="68" c4="255" />
      <Entry c1="237" c2="121" c3="67" c4="255" />
      <Entry c1="155" c2="128" c3="111" c4="255" />
      <Entry c1="153" c2="129" c3="116" c4="255" />
      <Entry c1="186" c2="131" c3="80" c4="255" />
      <Entry c1="165" c2="130" c3="106" c4="255" />
      <Entry c1="166" c2="134" c3="116" c4="255" />
      <Entry c1="177" c2="134" c3="115" c4="255" />
      <Entry c1="215" c2="132" c3="59" c4="255" />
      <Entry c1="254" c2="141" c3="25" c4="255" />
      <Entry c1="235" c2="130" c3="43" c4="255" />
      <Entry c1="233" c2="132" c3="57" c4="255" />
      <Entry c1="235" c2="147" c3="60" c4="255" />
      <Entry c1="251" c2="134" c3="43" c4="255" />
      <Entry c1="248" c2="135" c3="55" c4="255" />
      <Entry c1="254" c2="150" c3="41" c4="255" />
      <Entry c1="251" c2="150" c3="57" c4="255" />
      <Entry c1="255" c2="179" c3="20" c4="255" />
      <Entry c1="254" c2="171" c3="55" c4="255" />
      <Entry c1="201" c2="133" c3="72" c4="255" />
      <Entry c1="200" c2="138" c3="84" c4="255" />
      <Entry c1="203" c2="147" c3="88" c4="255" />
      <Entry c1="216" c2="134" c3="69" c4="255" />
      <Entry c1="217" c2="137" c3="82" c4="255" />
      <Entry c1="214" c2="148" c3="75" c4="255" />
      <Entry c1="218" c2="151" c3="86" c4="255" />
      <Entry c1="223" c2="160" c3="92" c4="255" />
      <Entry c1="218" c2="160" c3="99" c4="255" />
      <Entry c1="232" c2="136" c3="69" c4="255" />
      <Entry c1="232" c2="139" c3="84" c4="255" />
      <Entry c1="234" c2="149" c3="72" c4="255" />
      <Entry c1="232" c2="153" c3="85" c4="255" />
      <Entry c1="246" c2="137" c3="67" c4="255" />
      <Entry c1="248" c2="139" c3="85" c4="255" />
      <Entry c1="248" c2="151" c3="70" c4="255" />
      <Entry c1="247" c2="154" c3="83" c4="255" />
      <Entry c1="240" c2="156" c3="97" c4="255" />
      <Entry c1="233" c2="162" c3="76" c4="255" />
      <Entry c1="234" c2="166" c3="88" c4="255" />
      <Entry c1="233" c2="178" c3="91" c4="255" />
      <Entry c1="252" c2="167" c3="71" c4="255" />
      <Entry c1="249" c2="168" c3="84" c4="255" />
      <Entry c1="254" c2="182" c3="72" c4="255" />
      <Entry c1="253" c2="184" c3="86" c4="255" />
      <Entry c1="246" c2="181" c3="102" c4="255" />
      <Entry c1="254" c2="202" c3="23" c4="255" />
      <Entry c1="254" c2="204" c3="51" c4="255" />
      <Entry c1="254" c2="234" c3="53" c4="255" />
      <Entry c1="254" c2="203" c3="84" c4="255" />
      <Entry c1="237" c2="193" c3="107" c4="255" />
      <Entry c1="254" c2="200" c3="103" c4="255" />
      <Entry c1="251" c2="201" c3="117" c4="255" />
      <Entry c1="254" c2="215" c3="103" c4="255" />
      <Entry c1="254" c2="216" c3="118" c4="255" />
      <Entry c1="254" c2="235" c3="83" c4="255" />
      <Entry c1="254" c2="235" c3="113" c4="255" />
      <Entry c1="252" c2="214" c3="136" c4="255" />
      <Entry c1="254" c2="231" c3="135" c4="255" />
      <Entry c1="255" c2="233" c3="151" c4="255" />
      <Entry c1="254" c2="246" c3="135" c4="255" />
      <Entry c1="254" c2="248" c3="152" c4="255" />
      <Entry c1="254" c2="249" c3="173" c4="255" />
      <Entry c1="254" c2="254" c3="206" c4="255" />
      <Entry c1="254" c2="254" c3="243" c4="255" />
      <Entry c1="0" c2="0" c3="0" c4="255" />
      <Entry c1="0" c2="0" c3="0" c4="255" />
      <Entry c1="0" c2="0" c3="0" c4="255" />
      <Entry c1="0" c2="0" c3="0" c4="255" />
      <Entry c1="0" c2="0" c3="0" c4="255" />
      <Entry c1="0" c2="0" c3="0" c4="0" />
    </ColorTable>
    <SimpleSource>
      <SourceFilename relativeToVRT="1">fractal.png</SourceFilename>
      <SourceBand>1</SourceBand>
      <SourceProperties RasterXSize="300" RasterYSize="225" DataType="Byte" BlockXSize="300" BlockYSize="1" />
      <SrcRect xOff="0" yOff="0" xSize="300" ySize="225" />
      <DstRect xOff="0" yOff="0" xSize="300" ySize="225" />
    </SimpleSource>
  </VRTRasterBand>
</VRTDataset>
