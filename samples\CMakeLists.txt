# OSGEarth WebAssembly 简化示例
cmake_minimum_required(VERSION 3.10)
project(osgearth_simple_earth)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 确保我们使用 Emscripten 编译
if(EMSCRIPTEN)
    message(STATUS "OSGEarth WebAssembly 多线程构建")
    
    # 库路径配置
    set(OSG_WASM_LIB_DIR "F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep")
    set(OSGEARTH_WASM_LIB_DIR "F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/redist_wasm")
    set(OSGEARTH_SOURCE_DIR "F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src")
    
    # 包含头文件目录
    include_directories(
        "${OSG_WASM_LIB_DIR}/include"
        "${OSGEARTH_SOURCE_DIR}"
        "${CMAKE_CURRENT_SOURCE_DIR}/my_osg_sample"
    )
    
    # 源文件 - 使用最简单的 my_osg_sample
    set(SOURCES
        my_osg_sample/main.cpp
        my_osg_sample/TileTextureManager.cpp
        my_osg_sample/TileKey.cpp
    )
    
    # 创建可执行文件
    add_executable(osgearth_simple_earth ${SOURCES})
    
    # 编译器标志
    target_compile_options(osgearth_simple_earth PRIVATE
        -DOSG_GLSL_VERSION=300
        -DOSGEARTH_HAVE_GEOS=1
        -DOSGEARTH_HAVE_GDAL=0
        -DOSGEARTH_HAVE_PROJ=1
        -DUSE_EXTERNAL_WASM_DEPENDS=ON
        -DSTB_IMAGE_IMPLEMENTATION
        -DSTBI_NO_STDIO
        -DSTBI_NO_FAILURE_STRINGS
        -DSTBI_ONLY_PNG
        -DSTBI_ONLY_JPEG
        -pthread
        -matomics
        -mbulk-memory
        -O3
    )
    
    # 核心库文件
    set(CORE_LIBS
        "${OSGEARTH_WASM_LIB_DIR}/libosgEarth.a"
        "${OSGEARTH_WASM_LIB_DIR}/osgdb_earth.a"
        "${OSGEARTH_WASM_LIB_DIR}/osgdb_osgearth_engine_rex.a"
        "${OSG_WASM_LIB_DIR}/lib/libosg.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgViewer.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgDB.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgGA.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgUtil.a"
        "${OSG_WASM_LIB_DIR}/lib/libOpenThreads.a"
    )
    
    # 第三方库
    set(THIRD_PARTY_LIBS
        "${OSG_WASM_LIB_DIR}/lib/libgeos.a"
        "${OSG_WASM_LIB_DIR}/lib/libproj.a"
        "${OSG_WASM_LIB_DIR}/lib/libcurl.a"
        "${OSG_WASM_LIB_DIR}/lib/libGeographicLib.a"
        "${OSG_WASM_LIB_DIR}/lib/libz.a"
        "${OSG_WASM_LIB_DIR}/lib/libexpat.a"
        "${OSG_WASM_LIB_DIR}/lib/libpng16.a"
        "${OSG_WASM_LIB_DIR}/lib/libjpeg.a"
    )
    
    # 链接库
    target_link_libraries(osgearth_simple_earth
        ${CORE_LIBS}
        ${THIRD_PARTY_LIBS}
    )
    
    # 链接器标志 - 多线程版本
    set_target_properties(osgearth_simple_earth PROPERTIES
        SUFFIX ".html"
        LINK_FLAGS "-s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s WASM=1 -s INITIAL_MEMORY=1073741824 -s ALLOW_MEMORY_GROWTH=1 -s MAXIMUM_MEMORY=4294967296 -s FETCH=1 -s ASYNCIFY=1 -s EXPORTED_FUNCTIONS=['_main'] -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','HEAPU8'] -s NO_EXIT_RUNTIME=1 -s DISABLE_EXCEPTION_CATCHING=0 -s FORCE_FILESYSTEM=1 -s USE_PTHREADS=1 -s PTHREAD_POOL_SIZE=4 -s SHARED_MEMORY=1 -s PROXY_TO_PTHREAD=1 -pthread -matomics -mbulk-memory -O3"
    )
    
    message(STATUS "配置完成：多线程 WebAssembly 构建")
    
else()
    message(FATAL_ERROR "此 CMakeLists.txt 仅支持 Emscripten")
endif()
