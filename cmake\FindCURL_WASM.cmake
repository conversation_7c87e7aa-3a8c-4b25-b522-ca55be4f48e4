# FindCURL.cmake - WebAssembly版本
# 查找CURL库，支持外部WASM依赖

# WebAssembly外部依赖支持
IF(USE_EXTERNAL_WASM_DEPENDS)
    message(STATUS "Using external WASM dependencies for CURL")
    
    # 设置CURL根目录
    if(NOT CURL_DIR)
        set(CURL_DIR "$ENV{CURL_DIR}")
        if(NOT CURL_DIR)
            if(DEFINED OSG_DIR)
                set(CURL_DIR "${OSG_DIR}")
            else()
                message(FATAL_ERROR "CURL_DIR not set for external WASM dependencies")
            endif()
        endif()
    endif()
    
    # 设置包含目录
    SET(CURL_INCLUDE_DIR "${CURL_DIR}/include")
    SET(CURL_INCLUDE_DIRS "${CURL_INCLUDE_DIR}")
    
    # 设置库文件路径 - 强制使用静态库
    SET(CURL_LIBRARY "${CURL_DIR}/lib/libcurl.a")
    SET(CURL_LIBRARIES "${CURL_LIBRARY}")
    
    # 设置找到标志
    SET(CURL_FOUND TRUE)
    
    # 设置版本信息
    SET(CURL_VERSION "7.80.0")  # 假设版本，实际应该从头文件读取
    
    message(STATUS "CURL WASM library configured:")
    message(STATUS "  CURL_DIR: ${CURL_DIR}")
    message(STATUS "  CURL_INCLUDE_DIR: ${CURL_INCLUDE_DIR}")
    message(STATUS "  CURL_LIBRARY: ${CURL_LIBRARY}")
    
ELSE()
    # 使用标准查找方式
    find_package(CURL REQUIRED)
    
    # 设置变量以保持兼容性
    if(TARGET CURL::libcurl)
        get_target_property(CURL_INCLUDE_DIR CURL::libcurl INTERFACE_INCLUDE_DIRECTORIES)
        set(CURL_LIBRARY CURL::libcurl)
    endif()
    
    # 设置找到标志
    SET(CURL_FOUND TRUE)
    
ENDIF()

# 处理标准参数
include(FindPackageHandleStandardArgs)
FIND_PACKAGE_HANDLE_STANDARD_ARGS(CURL 
    REQUIRED_VARS CURL_LIBRARY CURL_INCLUDE_DIR
    VERSION_VAR CURL_VERSION
)

# 标记为高级变量
MARK_AS_ADVANCED(
    CURL_INCLUDE_DIR
    CURL_LIBRARY
)
