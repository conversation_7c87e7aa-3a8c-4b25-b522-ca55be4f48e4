<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OSG WebGL 简化测试 - 修复版本</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #000;
            font-family: Arial, sans-serif;
            color: white;
        }
        
        .header {
            background-color: #333;
            padding: 15px;
            text-align: center;
            font-size: 20px;
            border-bottom: 2px solid #555;
        }
        
        .container {
            display: flex;
            height: calc(100vh - 60px);
        }
        
        .canvas-area {
            flex: 1;
            position: relative;
            background-color: #000;
        }
        
        #canvas {
            width: 100%;
            height: 100%;
            display: block;
            border: none;
        }
        
        .info-panel {
            width: 300px;
            background-color: #222;
            padding: 20px;
            overflow-y: auto;
        }
        
        .status-item {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #333;
            border-radius: 5px;
        }
        
        .status-label {
            font-weight: bold;
            color: #4CAF50;
        }
        
        .status-value {
            margin-top: 5px;
            font-family: monospace;
            font-size: 12px;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            font-size: 18px;
            background-color: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 10px;
        }
        
        .hidden {
            display: none;
        }
        
        .error {
            color: #f44336;
        }
        
        .success {
            color: #4CAF50;
        }
    </style>
</head>
<body>
    <div class="header">
        OSG WebGL 简化测试 - 验证基本渲染功能
    </div>
    
    <div class="container">
        <div class="canvas-area">
            <canvas id="canvas" oncontextmenu="event.preventDefault()"></canvas>
            <div id="loading" class="loading">
                <div>正在加载 OSG WebGL 模块...</div>
                <div style="margin-top: 10px; font-size: 14px;">请稍候...</div>
            </div>
        </div>
        
        <div class="info-panel">
            <div class="status-item">
                <div class="status-label">加载状态</div>
                <div id="load-status" class="status-value">初始化中...</div>
            </div>
            
            <div class="status-item">
                <div class="status-label">渲染状态</div>
                <div id="render-status" class="status-value">等待中...</div>
            </div>
            
            <div class="status-item">
                <div class="status-label">Canvas 信息</div>
                <div id="canvas-info" class="status-value">--</div>
            </div>
            
            <div class="status-item">
                <div class="status-label">操作说明</div>
                <div class="status-value">
                    • 鼠标左键拖拽: 旋转地球<br>
                    • 鼠标滚轮: 缩放<br>
                    • 预期显示: 蓝色地球球体
                </div>
            </div>
            
            <div class="status-item">
                <div class="status-label">技术信息</div>
                <div class="status-value">
                    • 引擎: OpenSceneGraph<br>
                    • 渲染: WebGL 2.0<br>
                    • 模式: 单线程<br>
                    • 几何: 简化球体
                </div>
            </div>
            
            <div class="status-item">
                <div class="status-label">调试日志</div>
                <div id="debug-log" class="status-value" style="max-height: 200px; overflow-y: auto; font-size: 10px;">
                    等待日志输出...
                </div>
            </div>
        </div>
    </div>

    <script>
        // 获取元素
        const canvas = document.getElementById('canvas');
        const loadingDiv = document.getElementById('loading');
        const loadStatus = document.getElementById('load-status');
        const renderStatus = document.getElementById('render-status');
        const canvasInfo = document.getElementById('canvas-info');
        const debugLog = document.getElementById('debug-log');
        
        // 日志函数
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            console.log(logEntry);
            
            const logDiv = document.createElement('div');
            logDiv.textContent = logEntry;
            logDiv.style.color = type === 'error' ? '#f44336' : type === 'success' ? '#4CAF50' : '#fff';
            debugLog.appendChild(logDiv);
            debugLog.scrollTop = debugLog.scrollHeight;
        }
        
        // Canvas 尺寸调整
        function resizeCanvas() {
            const container = canvas.parentElement;
            const rect = container.getBoundingClientRect();
            canvas.width = rect.width;
            canvas.height = rect.height;
            
            canvasInfo.textContent = `${canvas.width} x ${canvas.height}`;
            addLog(`Canvas 尺寸调整: ${canvas.width} x ${canvas.height}`);
        }
        
        // 初始化
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
        
        // 状态监控
        function startStatusMonitoring() {
            setInterval(() => {
                if (window.Module) {
                    if (typeof window.Module.getRenderStatus === 'function') {
                        try {
                            const status = window.Module.getRenderStatus();
                            renderStatus.textContent = status;
                        } catch (e) {
                            renderStatus.textContent = '状态获取失败';
                        }
                    } else {
                        renderStatus.textContent = 'OSG 渲染中...';
                    }
                    
                    if (typeof window.Module.isRunning === 'function') {
                        try {
                            const running = window.Module.isRunning();
                            if (running) {
                                renderStatus.className = 'status-value success';
                            }
                        } catch (e) {
                            // 忽略错误
                        }
                    }
                } else {
                    renderStatus.textContent = '模块未加载';
                }
            }, 1000);
        }
        
        // 设置 Module 配置（在脚本加载前）
        addLog('设置 Module 配置...');
        
        // 使用 Emscripten 推荐的方式
        var Module = {
            canvas: canvas,
            
            preRun: [],
            
            postRun: [function() {
                addLog('OSG WebGL 模块 postRun 完成', 'success');
                loadingDiv.classList.add('hidden');
                loadStatus.textContent = '加载完成';
                loadStatus.className = 'status-value success';
                startStatusMonitoring();
            }],
            
            onRuntimeInitialized: function() {
                addLog('OSG WebGL 运行时初始化完成', 'success');
                loadStatus.textContent = '运行时就绪';
            },
            
            print: function(text) {
                addLog('OSG: ' + text);
            },
            
            printErr: function(text) {
                addLog('OSG Error: ' + text, 'error');
            },
            
            setStatus: function(text) {
                addLog('Status: ' + text);
                loadStatus.textContent = text;
            },
            
            totalDependencies: 0,
            
            monitorRunDependencies: function(left) {
                this.totalDependencies = Math.max(this.totalDependencies, left);
                if (left) {
                    loadStatus.textContent = `加载依赖: ${this.totalDependencies - left}/${this.totalDependencies}`;
                } else {
                    loadStatus.textContent = '依赖加载完成';
                }
            }
        };
        
        // 加载 WebAssembly 模块
        addLog('开始加载 OSG WebGL 模块...');
        
        const script = document.createElement('script');
        script.src = 'simple_webgl_test.js';
        
        script.onload = function() {
            addLog('OSG WebGL 脚本加载成功', 'success');
        };
        
        script.onerror = function() {
            addLog('OSG WebGL 脚本加载失败', 'error');
            loadStatus.textContent = '脚本加载失败';
            loadStatus.className = 'status-value error';
            loadingDiv.innerHTML = '<div class="error">加载失败: 找不到 simple_webgl_test.js 文件</div>';
        };
        
        document.head.appendChild(script);
        
        // 错误处理
        window.addEventListener('error', function(e) {
            addLog('JavaScript 错误: ' + e.message, 'error');
        });
        
        window.addEventListener('unhandledrejection', function(e) {
            addLog('Promise 拒绝: ' + e.reason, 'error');
        });
        
        addLog('页面初始化完成');
    </script>
</body>
</html>
