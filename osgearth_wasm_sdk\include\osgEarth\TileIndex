/* osgEarth
* Copyright 2025 Pelican Mapping
* MIT License
*/

#ifndef OSGEARTHUTIL_TILEINDEX_H
#define OSGEARTHUTIL_TILEINDEX_H 1

#include <osgEarth/Common>
#include <osg/Referenced>
#include <osg/ref_ptr>
#include <osgEarth/FeatureSource>

#include <string>
#include <vector>

namespace osgEarth { namespace Contrib
{
    /**
     * Manages a FeatureSource that is an index of geospatial data files     
     */
    class OSGEARTH_EXPORT TileIndex : public osg::Referenced
    {
    public:        

        static TileIndex* load( const std::string& filename );
        static TileIndex* create( const std::string& filename, const osgEarth::SpatialReference* srs);        

        /**
         * Gets files within the given extent.
         */
        void getFiles(const osgEarth::GeoExtent& extent, std::vector< std::string >& files);

        /**
         * Adds the given filename to the index
         */
        bool add( const std::string& filename, const GeoExtent& extent );
        
        /**
         * Gets the filename of the shapefile used for this index.
         */
        const std::string& getFilename() const { return _filename;}

    protected:
        TileIndex();        
        ~TileIndex();

        osg::ref_ptr< osgEarth::FeatureSource > _features;
        std::string _filename;
    };

} } // namespace osgEarth::Util

#endif //OSGEARTHUTIL_TILEINDEX_H
