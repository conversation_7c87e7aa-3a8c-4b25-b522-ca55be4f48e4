# OSGEarth WebAssembly SDK Configuration
# Generated on 2025-07-15

set(OSGEARTH_WASM_SDK_VERSION "2.10.1")
set(OSGEARTH_WASM_SDK_VERSION_MAJOR "2")
set(OSGEARTH_WASM_SDK_VERSION_MINOR "10")
set(OSGEARTH_WASM_SDK_VERSION_PATCH "1")

# SDK paths
get_filename_component(OSGEARTH_WASM_SDK_ROOT "${CMAKE_CURRENT_LIST_DIR}" ABSOLUTE)
set(OSGEARTH_WASM_SDK_INCLUDE_DIR "${OSGEARTH_WASM_SDK_ROOT}/include")
set(OSGEARTH_WASM_SDK_LIB_DIR "${OSGEARTH_WASM_SDK_ROOT}/lib")
set(OSGEARTH_WASM_SDK_SHARE_DIR "${OSGEARTH_WASM_SDK_ROOT}/share")
set(OSGEARTH_WASM_SDK_DATA_DIR "${OSGEARTH_WASM_SDK_ROOT}/share/osgearth/data")

# Core libraries
set(OSGEARTH_WASM_CORE_LIBRARY "${OSGEARTH_WASM_SDK_LIB_DIR}/libosgEarth.a")

# Plugin libraries
set(OSGEARTH_WASM_PLUGINS
    "${OSGEARTH_WASM_SDK_LIB_DIR}/osgdb_earth.a"
    "${OSGEARTH_WASM_SDK_LIB_DIR}/osgdb_osgearth_engine_rex.a"
    "${OSGEARTH_WASM_SDK_LIB_DIR}/osgdb_kml.a"
    "${OSGEARTH_WASM_SDK_LIB_DIR}/osgdb_gltf.a"
    "${OSGEARTH_WASM_SDK_LIB_DIR}/osgdb_osgearth_sky_simple.a"
    "${OSGEARTH_WASM_SDK_LIB_DIR}/osgdb_osgearth_sky_gl.a"
    "${OSGEARTH_WASM_SDK_LIB_DIR}/osgdb_osgearth_terrainshader.a"
    "${OSGEARTH_WASM_SDK_LIB_DIR}/osgdb_osgearth_cache_filesystem.a"
    "${OSGEARTH_WASM_SDK_LIB_DIR}/osgdb_osgearth_viewpoints.a"
    "${OSGEARTH_WASM_SDK_LIB_DIR}/osgdb_osgearth_bumpmap.a"
    "${OSGEARTH_WASM_SDK_LIB_DIR}/osgdb_osgearth_detail.a"
    "${OSGEARTH_WASM_SDK_LIB_DIR}/osgdb_osgearth_colorramp.a"
    "${OSGEARTH_WASM_SDK_LIB_DIR}/osgdb_osgearth_featurefilter_intersect.a"
    "${OSGEARTH_WASM_SDK_LIB_DIR}/osgdb_osgearth_featurefilter_join.a"
    "${OSGEARTH_WASM_SDK_LIB_DIR}/osgdb_osgearth_scriptengine_javascript.a"
    "${OSGEARTH_WASM_SDK_LIB_DIR}/osgdb_osgearth_vdatum_egm2008.a"
    "${OSGEARTH_WASM_SDK_LIB_DIR}/osgdb_osgearth_vdatum_egm96.a"
    "${OSGEARTH_WASM_SDK_LIB_DIR}/osgdb_osgearth_vdatum_egm84.a"
    "${OSGEARTH_WASM_SDK_LIB_DIR}/osgdb_template.a"
)

# All libraries combined
set(OSGEARTH_WASM_LIBRARIES
    ${OSGEARTH_WASM_CORE_LIBRARY}
    ${OSGEARTH_WASM_PLUGINS}
)

# Include directories
set(OSGEARTH_WASM_INCLUDE_DIRS
    ${OSGEARTH_WASM_SDK_INCLUDE_DIR}
)

# Compiler definitions
set(OSGEARTH_WASM_DEFINITIONS
    -DOSGEARTH_WASM_BUILD
    -DOSG_THREADING_ENABLED
)

# WebAssembly specific flags
set(OSGEARTH_WASM_COMPILE_FLAGS
    -std=c++20
    -pthread
    -O2
)

set(OSGEARTH_WASM_LINK_FLAGS
    -s WASM=1
    -s USE_WEBGL2=1
    -s FULL_ES3=1
    -s USE_PTHREADS=1
    -s PTHREAD_POOL_SIZE=4
    -s ALLOW_MEMORY_GROWTH=1
    -s INITIAL_MEMORY=268435456
    -s SHARED_MEMORY=1
    -s FETCH=1
    -s NO_EXIT_RUNTIME=1
    -s DISABLE_EXCEPTION_CATCHING=0
    -pthread
)

# Helper function to setup OSGEarth WebAssembly target
function(setup_osgearth_wasm_target target_name)
    target_include_directories(${target_name} PRIVATE ${OSGEARTH_WASM_INCLUDE_DIRS})
    target_compile_definitions(${target_name} PRIVATE ${OSGEARTH_WASM_DEFINITIONS})
    target_compile_options(${target_name} PRIVATE ${OSGEARTH_WASM_COMPILE_FLAGS})
    target_link_libraries(${target_name} ${OSGEARTH_WASM_LIBRARIES})
    
    # Set WebAssembly link flags
    foreach(flag ${OSGEARTH_WASM_LINK_FLAGS})
        set_property(TARGET ${target_name} APPEND_STRING PROPERTY LINK_FLAGS " ${flag}")
    endforeach()
endfunction()

# Status message
message(STATUS "Found OSGEarth WebAssembly SDK ${OSGEARTH_WASM_SDK_VERSION}")
message(STATUS "  Include dir: ${OSGEARTH_WASM_SDK_INCLUDE_DIR}")
message(STATUS "  Library dir: ${OSGEARTH_WASM_SDK_LIB_DIR}")
message(STATUS "  Data dir: ${OSGEARTH_WASM_SDK_DATA_DIR}")
message(STATUS "  Core library: ${OSGEARTH_WASM_CORE_LIBRARY}")
message(STATUS "  Plugin count: ${CMAKE_LIST_LENGTH OSGEARTH_WASM_PLUGINS}")

# Verify core library exists
if(NOT EXISTS "${OSGEARTH_WASM_CORE_LIBRARY}")
    message(FATAL_ERROR "OSGEarth core library not found: ${OSGEARTH_WASM_CORE_LIBRARY}")
endif()

# Set found flag
set(OSGEarth_WASM_FOUND TRUE)
