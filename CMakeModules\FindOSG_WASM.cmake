# FindOSG.cmake - WebAssembly版本
# 查找OpenSceneGraph库，支持外部WASM依赖

# WebAssembly外部依赖支持
IF(USE_EXTERNAL_WASM_DEPENDS)
    message(STATUS "Using external WASM dependencies for OSG")
    
    # 设置OSG根目录
    if(NOT OSG_DIR)
        set(OSG_DIR "$ENV{OSG_DIR}")
        if(NOT OSG_DIR)
            message(FATAL_ERROR "OSG_DIR not set for external WASM dependencies")
        endif()
    endif()
    
    # 设置包含目录
    SET(OSG_INCLUDE_DIR "${OSG_DIR}/include")
    SET(OSG_GEN_INCLUDE_DIR "${OSG_DIR}/include")
    
    # 设置库文件路径 - 强制使用静态库
    SET(OSG_LIBRARY "${OSG_DIR}/lib/libosg.a")
    SET(OSGUTIL_LIBRARY "${OSG_DIR}/lib/libosgUtil.a")
    SET(OSGDB_LIBRARY "${OSG_DIR}/lib/libosgDB.a")
    SET(OSGGA_LIBRARY "${OSG_DIR}/lib/libosgGA.a")
    SET(OSGFX_LIBRARY "${OSG_DIR}/lib/libosgFX.a")
    SET(OSGSIM_LIBRARY "${OSG_DIR}/lib/libosgSim.a")
    SET(OSGTEXT_LIBRARY "${OSG_DIR}/lib/libosgText.a")
    SET(OSGVIEWER_LIBRARY "${OSG_DIR}/lib/libosgViewer.a")
    SET(OSGVOLUME_LIBRARY "${OSG_DIR}/lib/libosgVolume.a")
    SET(OSGWIDGET_LIBRARY "${OSG_DIR}/lib/libosgWidget.a")
    SET(OSGSHADOW_LIBRARY "${OSG_DIR}/lib/libosgShadow.a")
    SET(OSGANIMATION_LIBRARY "${OSG_DIR}/lib/libosgAnimation.a")
    SET(OSGPARTICLE_LIBRARY "${OSG_DIR}/lib/libosgParticle.a")
    SET(OSGTERRAIN_LIBRARY "${OSG_DIR}/lib/libosgTerrain.a")
    SET(OSGMANIPULATOR_LIBRARY "${OSG_DIR}/lib/libosgManipulator.a")
    
    # OpenThreads库
    SET(OPENTHREADS_LIBRARY "${OSG_DIR}/lib/libOpenThreads.a")
    SET(OPENTHREADS_INCLUDE_DIR "${OSG_DIR}/include")
    
    # 设置找到标志
    SET(OSG_FOUND TRUE)
    SET(OSGUTIL_FOUND TRUE)
    SET(OSGDB_FOUND TRUE)
    SET(OSGGA_FOUND TRUE)
    SET(OSGFX_FOUND TRUE)
    SET(OSGSIM_FOUND TRUE)
    SET(OSGTEXT_FOUND TRUE)
    SET(OSGVIEWER_FOUND TRUE)
    SET(OSGVOLUME_FOUND TRUE)
    SET(OSGWIDGET_FOUND TRUE)
    SET(OSGSHADOW_FOUND TRUE)
    SET(OSGANIMATION_FOUND TRUE)
    SET(OSGPARTICLE_FOUND TRUE)
    SET(OSGTERRAIN_FOUND TRUE)
    SET(OSGMANIPULATOR_FOUND TRUE)
    SET(OPENTHREADS_FOUND TRUE)
    
    # 设置版本信息
    SET(OSG_VERSION "3.6.5")  # 假设版本，实际应该从头文件读取
    
    message(STATUS "OSG WASM libraries configured:")
    message(STATUS "  OSG_DIR: ${OSG_DIR}")
    message(STATUS "  OSG_INCLUDE_DIR: ${OSG_INCLUDE_DIR}")
    message(STATUS "  OSG_LIBRARY: ${OSG_LIBRARY}")
    
ELSE()
    # 标准OSG查找逻辑
    message(STATUS "Using standard OSG find logic")
    
    # 查找OSG安装路径
    FIND_PATH(OSG_INCLUDE_DIR osg/Node
        HINTS
            ENV OSG_DIR
            ENV OSG_ROOT
            ENV OSGDIR
        PATH_SUFFIXES include
        PATHS
            ~/Library/Frameworks
            /Library/Frameworks
            /usr/local
            /usr
            /sw # Fink
            /opt/local # DarwinPorts
            /opt/csw # Blastwave
            /opt
            [HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session\ Manager\\Environment;OSG_ROOT]/include
            ${CMAKE_INSTALL_PREFIX}/include
    )
    
    # 查找各个OSG组件库
    MACRO(FIND_OSG_LIBRARY MYLIBRARY MYLIBRARYNAME)
        FIND_LIBRARY(${MYLIBRARY}
            NAMES ${MYLIBRARYNAME}
            HINTS
                ENV OSG_DIR
                ENV OSG_ROOT
                ENV OSGDIR
            PATH_SUFFIXES lib64 lib
            PATHS
                ~/Library/Frameworks
                /Library/Frameworks
                /usr/local
                /usr
                /sw
                /opt/local
                /opt/csw
                /opt
                [HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session\ Manager\\Environment;OSG_ROOT]/lib
                ${CMAKE_INSTALL_PREFIX}/lib
        )
    ENDMACRO(FIND_OSG_LIBRARY)
    
    # 查找核心OSG库
    FIND_OSG_LIBRARY(OSG_LIBRARY osg)
    FIND_OSG_LIBRARY(OSGUTIL_LIBRARY osgUtil)
    FIND_OSG_LIBRARY(OSGDB_LIBRARY osgDB)
    FIND_OSG_LIBRARY(OSGGA_LIBRARY osgGA)
    FIND_OSG_LIBRARY(OSGFX_LIBRARY osgFX)
    FIND_OSG_LIBRARY(OSGSIM_LIBRARY osgSim)
    FIND_OSG_LIBRARY(OSGTEXT_LIBRARY osgText)
    FIND_OSG_LIBRARY(OSGVIEWER_LIBRARY osgViewer)
    FIND_OSG_LIBRARY(OSGVOLUME_LIBRARY osgVolume)
    FIND_OSG_LIBRARY(OSGWIDGET_LIBRARY osgWidget)
    FIND_OSG_LIBRARY(OSGSHADOW_LIBRARY osgShadow)
    FIND_OSG_LIBRARY(OSGANIMATION_LIBRARY osgAnimation)
    FIND_OSG_LIBRARY(OSGPARTICLE_LIBRARY osgParticle)
    FIND_OSG_LIBRARY(OSGTERRAIN_LIBRARY osgTerrain)
    FIND_OSG_LIBRARY(OSGMANIPULATOR_LIBRARY osgManipulator)
    
    # 查找OpenThreads
    FIND_OSG_LIBRARY(OPENTHREADS_LIBRARY OpenThreads)
    SET(OPENTHREADS_INCLUDE_DIR ${OSG_INCLUDE_DIR})
    
    # 设置找到标志
    SET(OSG_FOUND "NO")
    IF(OSG_LIBRARY AND OSG_INCLUDE_DIR)
        SET(OSG_FOUND "YES")
    ENDIF()
    
    # 设置各组件找到标志
    IF(OSGUTIL_LIBRARY)
        SET(OSGUTIL_FOUND "YES")
    ENDIF()
    IF(OSGDB_LIBRARY)
        SET(OSGDB_FOUND "YES")
    ENDIF()
    IF(OSGGA_LIBRARY)
        SET(OSGGA_FOUND "YES")
    ENDIF()
    IF(OSGFX_LIBRARY)
        SET(OSGFX_FOUND "YES")
    ENDIF()
    IF(OSGSIM_LIBRARY)
        SET(OSGSIM_FOUND "YES")
    ENDIF()
    IF(OSGTEXT_LIBRARY)
        SET(OSGTEXT_FOUND "YES")
    ENDIF()
    IF(OSGVIEWER_LIBRARY)
        SET(OSGVIEWER_FOUND "YES")
    ENDIF()
    IF(OSGVOLUME_LIBRARY)
        SET(OSGVOLUME_FOUND "YES")
    ENDIF()
    IF(OSGWIDGET_LIBRARY)
        SET(OSGWIDGET_FOUND "YES")
    ENDIF()
    IF(OSGSHADOW_LIBRARY)
        SET(OSGSHADOW_FOUND "YES")
    ENDIF()
    IF(OSGANIMATION_LIBRARY)
        SET(OSGANIMATION_FOUND "YES")
    ENDIF()
    IF(OSGPARTICLE_LIBRARY)
        SET(OSGPARTICLE_FOUND "YES")
    ENDIF()
    IF(OSGTERRAIN_LIBRARY)
        SET(OSGTERRAIN_FOUND "YES")
    ENDIF()
    IF(OSGMANIPULATOR_LIBRARY)
        SET(OSGMANIPULATOR_FOUND "YES")
    ENDIF()
    IF(OPENTHREADS_LIBRARY)
        SET(OPENTHREADS_FOUND "YES")
    ENDIF()
    
ENDIF()

# 设置所有OSG库的列表
SET(OSG_LIBRARIES
    ${OSG_LIBRARY}
    ${OSGUTIL_LIBRARY}
    ${OSGDB_LIBRARY}
    ${OSGGA_LIBRARY}
    ${OSGFX_LIBRARY}
    ${OSGSIM_LIBRARY}
    ${OSGTEXT_LIBRARY}
    ${OSGVIEWER_LIBRARY}
    ${OSGVOLUME_LIBRARY}
    ${OSGWIDGET_LIBRARY}
    ${OSGSHADOW_LIBRARY}
    ${OSGANIMATION_LIBRARY}
    ${OSGPARTICLE_LIBRARY}
    ${OSGTERRAIN_LIBRARY}
    ${OSGMANIPULATOR_LIBRARY}
    ${OPENTHREADS_LIBRARY}
)

# 设置包含目录列表
SET(OSG_INCLUDE_DIRS ${OSG_INCLUDE_DIR} ${OPENTHREADS_INCLUDE_DIR})

# 处理标准参数
include(FindPackageHandleStandardArgs)
FIND_PACKAGE_HANDLE_STANDARD_ARGS(OSG DEFAULT_MSG OSG_LIBRARY OSG_INCLUDE_DIR)

# 标记为高级变量
MARK_AS_ADVANCED(
    OSG_INCLUDE_DIR
    OSG_LIBRARY
    OSGUTIL_LIBRARY
    OSGDB_LIBRARY
    OSGGA_LIBRARY
    OSGFX_LIBRARY
    OSGSIM_LIBRARY
    OSGTEXT_LIBRARY
    OSGVIEWER_LIBRARY
    OSGVOLUME_LIBRARY
    OSGWIDGET_LIBRARY
    OSGSHADOW_LIBRARY
    OSGANIMATION_LIBRARY
    OSGPARTICLE_LIBRARY
    OSGTERRAIN_LIBRARY
    OSGMANIPULATOR_LIBRARY
    OPENTHREADS_LIBRARY
    OPENTHREADS_INCLUDE_DIR
)
