/* OSGEarth True Digital Earth WebGL Implementation
 * 使用真正的OSGEarth API创建数字地球
 * 基于osg_texture_test.cpp的WebGL框架
 * 支持WebAssembly多线程
 */

#include <iostream>
#include <osg/Node>
#include <osg/Group>
#include <osgViewer/Viewer>
#include <osgGA/TrackballManipulator>
#include <osgDB/ReadFile>

// OSGEarth核心头文件
#include <osgEarth/Map>
#include <osgEarth/MapNode>
#include <osgEarth/ImageLayer>
#include <osgEarth/ElevationLayer>
#include <osgEarth/EarthManipulator>
#include <osgEarth/Viewpoint>
#include <osgEarth/GeoData>
#include <osgEarth/Profile>
#include <osgEarth/Registry>

#ifdef __EMSCRIPTEN__
#include <emscripten.h>
#include <emscripten/html5.h>
#include <osg/GraphicsContext>
#include <osgGA/EventQueue>
#include <osgGA/GUIEventAdapter>
#endif

#define LC "[TrueEarth] "

// 全局变量
static osgViewer::Viewer* g_viewer = nullptr;
static bool g_running = true;

#ifdef __EMSCRIPTEN__
// WebGL上下文管理 - 基于osg_texture_test.cpp的成功实现
static EMSCRIPTEN_WEBGL_CONTEXT_HANDLE g_webgl_context = 0;
static bool g_webgl_initialized = false;

// 简单的Emscripten GraphicsContext
class EmscriptenGraphicsContext : public osg::GraphicsContext
{
public:
  EmscriptenGraphicsContext(Traits *traits) : osg::GraphicsContext()
  {
    _traits = traits;
    _valid = true;
    setState(new osg::State);
    getState()->setGraphicsContext(this);
    std::cout << LC << "[GC] EmscriptenGraphicsContext created" << std::endl;
  }

  virtual ~EmscriptenGraphicsContext()
  {
    std::cout << LC << "[GC] EmscriptenGraphicsContext destroyed" << std::endl;
  }

  virtual bool valid() const override { return _valid; }

  virtual bool realizeImplementation() override
  {
    std::cout << LC << "[GC] realizeImplementation called" << std::endl;
    if (g_webgl_context > 0)
    {
      EMSCRIPTEN_RESULT result = emscripten_webgl_make_context_current(g_webgl_context);
      if (result == EMSCRIPTEN_RESULT_SUCCESS)
      {
        std::cout << LC << "[GC] WebGL context made current successfully" << std::endl;
        _realized = true;
        return true;
      }
      else
      {
        std::cout << LC << "[GC] Failed to make WebGL context current: " << result << std::endl;
      }
    }
    return false;
  }

  virtual bool isRealizedImplementation() const override { return _realized; }
  virtual void closeImplementation() override { _realized = false; }

  virtual bool makeCurrentImplementation() override
  {
    if (g_webgl_context > 0)
    {
      EMSCRIPTEN_RESULT result = emscripten_webgl_make_context_current(g_webgl_context);
      return (result == EMSCRIPTEN_RESULT_SUCCESS);
    }
    return false;
  }

  virtual bool makeContextCurrentImplementation(GraphicsContext *readContext) override
  {
    return makeCurrentImplementation();
  }

  virtual bool releaseContextImplementation() override { return true; }
  virtual void swapBuffersImplementation() override { }
  virtual void bindPBufferToTextureImplementation(GLenum buffer) override { }

private:
  bool _valid = false;
  bool _realized = false;
};

// 简单的Emscripten WindowingSystemInterface
class EmscriptenWindowingSystemInterface : public osg::GraphicsContext::WindowingSystemInterface
{
public:
  EmscriptenWindowingSystemInterface()
  {
    std::cout << LC << "[WSI] Creating Emscripten WindowingSystemInterface" << std::endl;
  }

  virtual ~EmscriptenWindowingSystemInterface()
  {
    std::cout << LC << "[WSI] Destroying Emscripten WindowingSystemInterface" << std::endl;
  }

  virtual unsigned int getNumScreens(const osg::GraphicsContext::ScreenIdentifier &screenIdentifier = osg::GraphicsContext::ScreenIdentifier()) override
  {
    return 1;
  }

  virtual void getScreenSettings(const osg::GraphicsContext::ScreenIdentifier &screenIdentifier, osg::GraphicsContext::ScreenSettings &resolution) override
  {
    resolution.width = 800;
    resolution.height = 600;
    resolution.refreshRate = 60.0f;
    resolution.colorDepth = 24;
  }

  virtual void enumerateScreenSettings(const osg::GraphicsContext::ScreenIdentifier &screenIdentifier, osg::GraphicsContext::ScreenSettingsList &resolutionList) override
  {
    osg::GraphicsContext::ScreenSettings settings;
    settings.width = 800;
    settings.height = 600;
    settings.refreshRate = 60.0f;
    settings.colorDepth = 24;
    resolutionList.push_back(settings);
  }

  virtual osg::GraphicsContext *createGraphicsContext(osg::GraphicsContext::Traits *traits) override
  {
    std::cout << LC << "[WSI] createGraphicsContext called" << std::endl;
    if (!traits) return nullptr;
    
    osg::ref_ptr<EmscriptenGraphicsContext> context = new EmscriptenGraphicsContext(traits);
    return context.release();
  }
};

// 初始化WebGL上下文
bool initializeWebGL()
{
  if (g_webgl_initialized) return true;

  std::cout << LC << "[WebGL] Initializing WebGL context..." << std::endl;

  EmscriptenWebGLContextAttributes attrs;
  emscripten_webgl_init_context_attributes(&attrs);

  attrs.alpha = 1;
  attrs.depth = 1;
  attrs.stencil = 0;
  attrs.antialias = 1;
  attrs.premultipliedAlpha = 0;
  attrs.preserveDrawingBuffer = 0;
  attrs.powerPreference = EM_WEBGL_POWER_PREFERENCE_DEFAULT;
  attrs.failIfMajorPerformanceCaveat = 0;
  attrs.majorVersion = 2;
  attrs.minorVersion = 0;
  attrs.enableExtensionsByDefault = 1;

  g_webgl_context = emscripten_webgl_create_context("#canvas", &attrs);

  if (g_webgl_context <= 0)
  {
    std::cout << LC << "[WebGL] WebGL2 failed, trying WebGL1..." << std::endl;
    attrs.majorVersion = 1;
    attrs.minorVersion = 0;
    g_webgl_context = emscripten_webgl_create_context("#canvas", &attrs);
  }

  if (g_webgl_context > 0)
  {
    EMSCRIPTEN_RESULT result = emscripten_webgl_make_context_current(g_webgl_context);
    if (result == EMSCRIPTEN_RESULT_SUCCESS)
    {
      g_webgl_initialized = true;
      std::cout << LC << "[WebGL] WebGL context initialized successfully" << std::endl;

      glEnable(GL_DEPTH_TEST);
      glDepthFunc(GL_LEQUAL);
      glClearColor(0.0f, 0.0f, 0.1f, 1.0f); // 深蓝色太空背景

      int canvas_width, canvas_height;
      emscripten_get_canvas_element_size("#canvas", &canvas_width, &canvas_height);
      glViewport(0, 0, canvas_width, canvas_height);

      std::cout << LC << "[WebGL] Canvas size: " << canvas_width << "x" << canvas_height << std::endl;
      
      const char* version = (const char*)glGetString(GL_VERSION);
      const char* renderer = (const char*)glGetString(GL_RENDERER);
      std::cout << LC << "[WebGL] Version: " << (version ? version : "unknown") << std::endl;
      std::cout << LC << "[WebGL] Renderer: " << (renderer ? renderer : "unknown") << std::endl;
      
      return true;
    }
  }

  std::cout << LC << "[WebGL] Failed to create WebGL context" << std::endl;
  return false;
}

// 初始化WindowingSystemInterface
void initializeWindowingSystem()
{
  std::cout << LC << "[WSI] Initializing Emscripten WindowingSystemInterface..." << std::endl;
  osg::ref_ptr<EmscriptenWindowingSystemInterface> wsi = new EmscriptenWindowingSystemInterface();
  osg::GraphicsContext::getWindowingSystemInterfaces()->addWindowingSystemInterface(wsi.get());
  std::cout << LC << "[WSI] WindowingSystemInterface registered successfully" << std::endl;
}

// WebAssembly事件处理回调函数
EM_BOOL mouse_callback(int eventType, const EmscriptenMouseEvent* e, void* userData)
{
  if (!g_viewer || !g_viewer->getCamera()) return EM_FALSE;
  
  osg::ref_ptr<osgGA::EventQueue> eventQueue = g_viewer->getEventQueue();
  if (!eventQueue) return EM_FALSE;
  
  float x = e->targetX;
  float y = e->targetY;
  
  switch (eventType) {
    case EMSCRIPTEN_EVENT_MOUSEDOWN:
      eventQueue->mouseButtonPress(x, y, e->button + 1);
      break;
    case EMSCRIPTEN_EVENT_MOUSEUP:
      eventQueue->mouseButtonRelease(x, y, e->button + 1);
      break;
    case EMSCRIPTEN_EVENT_MOUSEMOVE:
      eventQueue->mouseMotion(x, y);
      break;
  }
  
  return EM_TRUE;
}

EM_BOOL wheel_callback(int eventType, const EmscriptenWheelEvent* e, void* userData)
{
  if (!g_viewer || !g_viewer->getCamera()) return EM_FALSE;
  
  osg::ref_ptr<osgGA::EventQueue> eventQueue = g_viewer->getEventQueue();
  if (!eventQueue) return EM_FALSE;
  
  osgGA::GUIEventAdapter::ScrollingMotion motion = 
    (e->deltaY < 0) ? osgGA::GUIEventAdapter::SCROLL_UP : osgGA::GUIEventAdapter::SCROLL_DOWN;
  
  eventQueue->mouseScroll(motion);
  return EM_TRUE;
}

EM_BOOL key_callback(int eventType, const EmscriptenKeyboardEvent* e, void* userData)
{
  if (!g_viewer || !g_viewer->getCamera()) return EM_FALSE;
  
  osg::ref_ptr<osgGA::EventQueue> eventQueue = g_viewer->getEventQueue();
  if (!eventQueue) return EM_FALSE;
  
  int key = e->keyCode;
  
  switch (eventType) {
    case EMSCRIPTEN_EVENT_KEYDOWN:
      eventQueue->keyPress(key);
      break;
    case EMSCRIPTEN_EVENT_KEYUP:
      eventQueue->keyRelease(key);
      break;
  }
  
  return EM_TRUE;
}

// 设置WebAssembly事件监听器
void setupWebAssemblyEventHandlers()
{
  std::cout << LC << "[Event] Setting up WebAssembly event handlers..." << std::endl;
  
  emscripten_set_mousedown_callback("#canvas", nullptr, EM_TRUE, mouse_callback);
  emscripten_set_mouseup_callback("#canvas", nullptr, EM_TRUE, mouse_callback);
  emscripten_set_mousemove_callback("#canvas", nullptr, EM_TRUE, mouse_callback);
  emscripten_set_wheel_callback("#canvas", nullptr, EM_TRUE, wheel_callback);
  emscripten_set_keydown_callback(EMSCRIPTEN_EVENT_TARGET_WINDOW, nullptr, EM_TRUE, key_callback);
  emscripten_set_keyup_callback(EMSCRIPTEN_EVENT_TARGET_WINDOW, nullptr, EM_TRUE, key_callback);
  
  std::cout << LC << "[Event] WebAssembly event handlers setup complete" << std::endl;
}
#endif

// 创建真正的OSGEarth数字地球场景
osg::ref_ptr<osg::Node> createOSGEarthScene()
{
  std::cout << LC << "Creating OSGEarth digital earth scene..." << std::endl;

  try
  {
    // 创建地图
    osg::ref_ptr<osgEarth::Map> map = new osgEarth::Map();

    // 创建一个简单的调试图像层
    osg::ref_ptr<osgEarth::ImageLayer> imageLayer = new osgEarth::ImageLayer();
    imageLayer->setName("Debug");

    // 设置图像层选项
    osgEarth::ImageLayerOptions imageOptions;
    imageOptions.name() = "Debug Image Layer";

    // 创建调试图像层
    osg::ref_ptr<osgEarth::DebugImageLayer> debugLayer = new osgEarth::DebugImageLayer();
    debugLayer->setName("Debug");
    map->addLayer(debugLayer.get());

    std::cout << LC << "Map created with debug image layer" << std::endl;

    // 创建MapNode
    osg::ref_ptr<osgEarth::MapNode> mapNode = new osgEarth::MapNode(map.get());

    std::cout << LC << "MapNode created successfully" << std::endl;

    return mapNode.get();
  }
  catch (const std::exception& e)
  {
    std::cout << LC << "Exception creating OSGEarth scene: " << e.what() << std::endl;
    return nullptr;
  }
  catch (...)
  {
    std::cout << LC << "Unknown exception creating OSGEarth scene" << std::endl;
    return nullptr;
  }
}

// 主循环函数
void mainLoop()
{
  if (!g_running || !g_viewer) return;

#ifdef __EMSCRIPTEN__
  // 确保WebGL上下文仍然有效
  if (g_webgl_context > 0)
  {
    emscripten_webgl_make_context_current(g_webgl_context);
  }
#endif

  static int frameCount = 0;
  frameCount++;

  if (!g_viewer->done())
  {
    try
    {
      g_viewer->frame();

      // 每60帧打印一次状态
      if (frameCount % 60 == 0)
      {
        std::cout << LC << "Frame " << frameCount << " - OSGEarth rendering" << std::endl;
      }
    }
    catch (const std::exception &e)
    {
      std::cerr << LC << "Exception in main loop: " << e.what() << std::endl;
    }
  }
}

// 主函数
int main()
{
  std::cout << LC << "=== OSGEarth True Digital Earth WebGL Demo ===" << std::endl;

  try
  {
#ifdef __EMSCRIPTEN__
    // 初始化WebGL上下文
    if (!initializeWebGL())
    {
      std::cout << LC << "Failed to initialize WebGL context" << std::endl;
      return -1;
    }

    // 初始化WindowingSystemInterface
    initializeWindowingSystem();
#endif

    // 创建OSG Viewer
    g_viewer = new osgViewer::Viewer();

#ifdef __EMSCRIPTEN__
    // 为WebAssembly环境创建专用的GraphicsContext
    osg::ref_ptr<osg::GraphicsContext::Traits> traits = new osg::GraphicsContext::Traits();
    traits->x = 0;
    traits->y = 0;
    traits->width = 800;
    traits->height = 600;
    traits->red = 8;
    traits->green = 8;
    traits->blue = 8;
    traits->alpha = 8;
    traits->depth = 24;
    traits->stencil = 0;
    traits->windowDecoration = false;
    traits->doubleBuffer = true;
    traits->sharedContext = nullptr;
    traits->setInheritedWindowPixelFormat = false;

    std::cout << LC << "Creating EmscriptenGraphicsContext..." << std::endl;
    osg::ref_ptr<EmscriptenGraphicsContext> gc = new EmscriptenGraphicsContext(traits.get());

    if (gc.valid())
    {
      std::cout << LC << "EmscriptenGraphicsContext created successfully" << std::endl;

      // 设置相机
      osg::Camera *camera = g_viewer->getCamera();
      camera->setGraphicsContext(gc.get());
      camera->setViewport(new osg::Viewport(0, 0, 800, 600));
      camera->setProjectionMatrixAsPerspective(45.0, 800.0 / 600.0, 1000.0, 100000000.0); // 适合地球尺度

      // 设置清除颜色为太空背景
      camera->setClearColor(osg::Vec4(0.0f, 0.0f, 0.1f, 1.0f));
      camera->setClearMask(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

      std::cout << LC << "Camera configured for OSGEarth view" << std::endl;
    }
    else
    {
      std::cout << LC << "Failed to create EmscriptenGraphicsContext" << std::endl;
      return -1;
    }
#else
    // 桌面环境设置
    g_viewer->setUpViewerAsEmbeddedInWindow(0, 0, 800, 600);
    osg::Camera *camera = g_viewer->getCamera();
    camera->setViewport(0, 0, 800, 600);
    camera->setProjectionMatrixAsPerspective(45.0, 800.0 / 600.0, 1000.0, 100000000.0);
#endif

    // 设置EarthManipulator相机操作器
    osg::ref_ptr<osgEarth::EarthManipulator> manipulator = new osgEarth::EarthManipulator();

    // 设置初始视点
    osgEarth::Viewpoint vp;
    vp.focalPoint() = osgEarth::GeoPoint(osgEarth::SpatialReference::get("wgs84"), 0.0, 0.0, 0.0);
    vp.range() = osgEarth::Distance(20000000.0, osgEarth::Units::METERS); // 2000万米高度
    vp.heading() = osgEarth::Angle(0.0, osgEarth::Units::DEGREES);
    vp.pitch() = osgEarth::Angle(-45.0, osgEarth::Units::DEGREES);

    manipulator->setViewpoint(vp);
    g_viewer->setCameraManipulator(manipulator.get());

    // 创建OSGEarth数字地球场景
    osg::ref_ptr<osg::Node> scene = createOSGEarthScene();
    if (scene.valid())
    {
      g_viewer->setSceneData(scene.get());
      std::cout << LC << "OSGEarth scene set successfully" << std::endl;
    }
    else
    {
      std::cout << LC << "Failed to create OSGEarth scene, using fallback" << std::endl;
      return -1;
    }

    // 实现viewer
    std::cout << LC << "Realizing OSG Viewer..." << std::endl;
    g_viewer->realize();

    if (g_viewer->isRealized())
    {
      std::cout << LC << "OSG Viewer realized successfully" << std::endl;
    }
    else
    {
      std::cout << LC << "Warning: OSG Viewer realization may have failed" << std::endl;
    }

#ifdef __EMSCRIPTEN__
    // 设置WebAssembly事件处理
    setupWebAssemblyEventHandlers();

    // WebAssembly环境：设置主循环
    emscripten_set_main_loop(mainLoop, 0, 1);
#else
    // 桌面环境：运行循环
    while (!g_viewer->done())
    {
      g_viewer->frame();
    }
#endif
  }
  catch (const std::exception &e)
  {
    std::cout << LC << "Exception in main: " << e.what() << std::endl;
    return -1;
  }

  std::cout << LC << "OSGEarth True Digital Earth Demo completed" << std::endl;
  return 0;
}
