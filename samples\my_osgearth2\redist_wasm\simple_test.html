<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单WebAssembly测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #000;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        #log {
            background-color: #222;
            color: #0f0;
            padding: 10px;
            margin-top: 10px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>简单WebAssembly测试</h1>
    
    <div id="log"></div>

    <script>
        // 日志函数
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        // WebAssembly模块配置
        var Module = {
            // 预运行函数
            preRun: [],
            
            // 后运行函数
            postRun: [function() {
                log('简单WebAssembly模块加载完成！');
            }],
            
            // 打印函数
            print: function(text) {
                log('WASM: ' + text);
            },
            
            // 错误打印函数
            printErr: function(text) {
                log('WASM Error: ' + text);
            },
            
            // 加载进度回调
            setStatus: function(text) {
                if (text) {
                    log('Status: ' + text);
                }
            },
            
            // 总下载进度
            totalDependencies: 0,
            monitorRunDependencies: function(left) {
                this.totalDependencies = Math.max(this.totalDependencies, left);
                if (left == 0) {
                    log('所有依赖加载完成');
                } else {
                    const progress = Math.round((this.totalDependencies - left) / this.totalDependencies * 100);
                    log(`加载进度: ${progress}% (剩余: ${left})`);
                }
            },
            
            // 错误处理
            onAbort: function(what) {
                log('模块中止: ' + what);
            },
            
            onRuntimeInitialized: function() {
                log('运行时初始化完成');
                
                // 尝试调用main函数
                if (Module._main) {
                    log('找到main函数，开始调用...');
                    try {
                        Module._main();
                        log('main函数调用成功');
                    } catch (e) {
                        log('main函数调用失败: ' + e.message);
                        log('错误堆栈: ' + e.stack);
                    }
                } else {
                    log('警告: 未找到main函数');
                }
            }
        };
        
        // 开始加载
        log('开始加载简单WebAssembly模块...');
        
        // 检查浏览器支持
        if (!window.WebAssembly) {
            log('错误: 浏览器不支持WebAssembly');
        } else {
            log('浏览器支持WebAssembly');
            
            // 加载模块脚本
            const script = document.createElement('script');
            script.src = 'simple_test.js';
            
            script.onload = function() {
                log('JavaScript模块加载完成');
            };
            
            script.onerror = function(e) {
                log('JavaScript模块加载失败: ' + e.message);
            };
            
            document.head.appendChild(script);
        }
        
        // 错误处理
        window.addEventListener('error', function(e) {
            log('页面错误: ' + e.error + ' 在 ' + e.filename + ':' + e.lineno);
        });
        
        window.addEventListener('unhandledrejection', function(e) {
            log('未处理的Promise拒绝: ' + e.reason);
        });
    </script>
</body>
</html>
