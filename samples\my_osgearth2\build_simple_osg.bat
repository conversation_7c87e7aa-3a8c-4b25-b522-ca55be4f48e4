@echo off
chcp 65001
echo ========================================
echo Simple OSG WebAssembly Test
echo ========================================

call C:\dev\emsdk\emsdk_env.bat

set THIRD_PARTY_DIR=E:\project\my-earth202507\thirid_party\wasm_dep

echo Compiling simple OSG test...
C:\dev\emsdk\python\3.13.3_64bit\python.exe C:\dev\emsdk\upstream\emscripten\emcc.py basic_osg_test.cpp -o redist_wasm\basic_osg_test.html -I%THIRD_PARTY_DIR%\include -L%THIRD_PARTY_DIR%\lib -losg -losgViewer -losgGA -losgDB -losgUtil -lOpenThreads -s WASM=1 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++20 -DOSG_THREADING_ENABLED

if %ERRORLEVEL% neq 0 (
    echo Compilation failed
    pause
    exit /b 1
)

echo Compilation successful!
pause
