// osgEarth WebAssembly 测试程序 - 显示 Google Maps 瓦片
#include <iostream>
#include <memory>

// OSG 核心头文件
#include <osg/ref_ptr>
#include <osg/Group>
#include <osg/Geode>
#include <osg/Material>
#include <osg/StateSet>

// OSG 查看器
#include <osgViewer/Viewer>
#include <osgGA/TrackballManipulator>

// osgEarth 头文件
#include <osgEarth/Map>
#include <osgEarth/MapNode>
#include <osgEarth/ImageLayer>
#include <osgEarth/XYZ>
#include <osgEarth/EarthManipulator>

// SDL2 和 Emscripten
#include <SDL2/SDL.h>
#include <emscripten.h>
#include <emscripten/html5.h>

using namespace osg;
using namespace osgEarth;

// Emscripten GraphicsContext实现
class EmscriptenGraphicsContext : public osg::GraphicsContext
{
public:
    EmscriptenGraphicsContext(Traits *traits) : osg::GraphicsContext()
    {
        _traits = traits;
        _valid = true;
        _realized = false;

        setState(new osg::State);
        getState()->setGraphicsContext(this);
    }

    virtual bool valid() const { return _valid; }
    virtual bool realizeImplementation()
    {
        _realized = true;
        return true;
    }
    virtual bool isRealizedImplementation() const { return _realized; }
    virtual void closeImplementation() { _realized = false; }
    virtual bool makeCurrentImplementation() { return true; }
    virtual bool makeContextCurrentImplementation(GraphicsContext *readContext) { return true; }
    virtual bool releaseContextImplementation() { return true; }
    virtual void swapBuffersImplementation()
    {
        if (s_window)
            SDL_GL_SwapWindow(s_window);
    }
    virtual void bindPBufferToTextureImplementation(GLenum buffer) {}
    virtual void resizedImplementation(int x, int y, int width, int height) {}

    static void setWindow(SDL_Window *window) { s_window = window; }

private:
    bool _valid;
    bool _realized;
    static SDL_Window *s_window;
};

SDL_Window *EmscriptenGraphicsContext::s_window = nullptr;

// 全局变量
osg::ref_ptr<osgViewer::Viewer> g_viewer;
SDL_Window *g_window = nullptr;
SDL_GLContext g_glContext = nullptr;

// 创建 osgEarth 地图场景
osg::ref_ptr<osg::Node> createEarthScene()
{
    std::cout << "[INFO] Creating osgEarth scene..." << std::endl;

    try
    {
        // 创建地图
        osg::ref_ptr<Map> map = new Map();

        // 添加 Google Maps 瓦片图层
        osg::ref_ptr<XYZImageLayer> googleLayer = new XYZImageLayer();
        googleLayer->setName("Google Maps");
        googleLayer->setURL("https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}");

        // 设置基本配置，避免多线程问题
        googleLayer->options().cachePolicy() = CachePolicy::NO_CACHE;

        map->addLayer(googleLayer.get());

        std::cout << "[INFO] Added Google Maps layer" << std::endl;

        // 创建地图节点
        osg::ref_ptr<MapNode> mapNode = new MapNode(map.get());

        std::cout << "[INFO] osgEarth scene created successfully" << std::endl;

        return mapNode.get();
    }
    catch (const std::exception &e)
    {
        std::cout << "[ERROR] Exception in createEarthScene: " << e.what() << std::endl;
        return nullptr;
    }
}

// 初始化SDL2
bool initializeSDL2()
{
    std::cout << "[INFO] Initializing SDL2..." << std::endl;

    if (SDL_Init(SDL_INIT_VIDEO) < 0)
    {
        std::cout << "[ERROR] SDL initialization failed: " << SDL_GetError() << std::endl;
        return false;
    }

    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 3);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 0);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_PROFILE_MASK, SDL_GL_CONTEXT_PROFILE_ES);
    SDL_GL_SetAttribute(SDL_GL_DOUBLEBUFFER, 1);
    SDL_GL_SetAttribute(SDL_GL_DEPTH_SIZE, 24);

    g_window = SDL_CreateWindow(
        "osgEarth WebAssembly Test",
        SDL_WINDOWPOS_CENTERED,
        SDL_WINDOWPOS_CENTERED,
        1024, 768,
        SDL_WINDOW_OPENGL | SDL_WINDOW_SHOWN);

    if (!g_window)
    {
        std::cout << "[ERROR] Window creation failed: " << SDL_GetError() << std::endl;
        return false;
    }

    g_glContext = SDL_GL_CreateContext(g_window);
    if (!g_glContext)
    {
        std::cout << "[ERROR] OpenGL context creation failed: " << SDL_GetError() << std::endl;
        return false;
    }

    SDL_GL_MakeCurrent(g_window, g_glContext);
    EmscriptenGraphicsContext::setWindow(g_window);

    std::cout << "[INFO] SDL2 initialized successfully" << std::endl;
    return true;
}

// 初始化OSG查看器
bool initializeOSGViewer()
{
    std::cout << "[INFO] Initializing OSG Viewer..." << std::endl;

    g_viewer = new osgViewer::Viewer();

    osg::ref_ptr<osg::GraphicsContext::Traits> traits = new osg::GraphicsContext::Traits();
    traits->x = 0;
    traits->y = 0;
    traits->width = 1024;
    traits->height = 768;
    traits->windowDecoration = false;
    traits->doubleBuffer = true;
    traits->sharedContext = 0;
    traits->pbuffer = false;

    osg::ref_ptr<EmscriptenGraphicsContext> gc = new EmscriptenGraphicsContext(traits.get());

    if (gc.valid())
    {
        g_viewer->getCamera()->setGraphicsContext(gc.get());
        g_viewer->getCamera()->setViewport(new osg::Viewport(0, 0, traits->width, traits->height));
        g_viewer->getCamera()->setProjectionMatrixAsPerspective(30.0f, (double)traits->width / (double)traits->height, 1.0f, 100000.0f);

        std::cout << "[INFO] Graphics context created successfully" << std::endl;
    }
    else
    {
        std::cerr << "[ERROR] Failed to create graphics context" << std::endl;
        return false;
    }

    // 使用 osgEarth 的地球操作器
    osg::ref_ptr<EarthManipulator> manipulator = new EarthManipulator();
    g_viewer->setCameraManipulator(manipulator.get());

    std::cout << "[INFO] OSG Viewer initialized successfully" << std::endl;
    return true;
}

// 主循环函数
void mainLoop()
{
    if (g_viewer.valid() && !g_viewer->done())
    {
        g_viewer->frame();
    }
}

int main()
{
    std::cout << "[INFO] Starting osgEarth WebAssembly Test..." << std::endl;

    if (!initializeSDL2())
    {
        std::cout << "[ERROR] Failed to initialize SDL2" << std::endl;
        return -1;
    }

    if (!initializeOSGViewer())
    {
        std::cout << "[ERROR] Failed to initialize OSG Viewer" << std::endl;
        return -1;
    }

    osg::ref_ptr<osg::Node> scene = createEarthScene();
    if (!scene.valid())
    {
        std::cout << "[ERROR] Failed to create Earth scene" << std::endl;
        return -1;
    }

    g_viewer->setSceneData(scene.get());
    g_viewer->realize();

    std::cout << "[INFO] Starting main loop..." << std::endl;
    emscripten_set_main_loop(mainLoop, 0, 1);

    return 0;
}
