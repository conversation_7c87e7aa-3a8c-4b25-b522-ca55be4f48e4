src/osgEarth/CMakeFiles/osgEarth.dir/Session.cpp.o: \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Session.cpp \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Session \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Common \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Export \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Notify \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Notify \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Export \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Config \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Referenced \
  E:\project\my-earth202507\third_party\wasm_dep\include\OpenThreads\ScopedLock \
  E:\project\my-earth202507\third_party\wasm_dep\include\OpenThreads\Mutex \
  E:\project\my-earth202507\third_party\wasm_dep\include\OpenThreads\Exports \
  E:\project\my-earth202507\third_party\wasm_dep\include\OpenThreads\Config \
  E:\project\my-earth202507\third_party\wasm_dep\include\OpenThreads\Atomic \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\ostream \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__config \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__config_site \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__configuration\abi.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__configuration\compiler.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__configuration\platform.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__configuration\availability.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__configuration\language.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__ostream\basic_ostream.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__exception\operations.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__fwd\memory.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\unique_ptr.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__assert \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__assertion_handler \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__verbose_abort \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__compare\compare_three_way.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__compare\three_way_comparable.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__compare\common_comparison_category.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__compare\ordering.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\enable_if.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_same.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\integral_constant.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__cstddef\size_t.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__concepts\common_reference_with.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__concepts\convertible_to.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_convertible.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__utility\declval.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__concepts\same_as.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\common_reference.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\common_type.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\conditional.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\decay.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\add_pointer.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_referenceable.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_void.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\remove_reference.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_array.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_function.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\remove_cv.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\remove_extent.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\remove_cvref.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\type_identity.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\void_t.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__utility\empty.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\copy_cv.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\copy_cvref.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\add_lvalue_reference.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\add_rvalue_reference.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_reference.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__concepts\equality_comparable.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__concepts\boolean_testable.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__utility\forward.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\make_const_lvalue_ref.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__concepts\totally_ordered.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__compare\compare_three_way_result.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__cstddef\nullptr_t.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__functional\hash.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__functional\unary_function.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__fwd\functional.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\conjunction.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\invoke.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_base_of.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_core_convertible.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_member_pointer.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_reference_wrapper.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\nat.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_constructible.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_enum.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\underlying_type.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__utility\pair.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__compare\synth_three_way.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__concepts\different_from.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__fwd\array.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__fwd\pair.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__fwd\tuple.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__tuple\tuple_indices.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__utility\integer_sequence.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_integral.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__tuple\tuple_like_no_subrange.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__fwd\complex.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__tuple\tuple_size.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__tuple\tuple_types.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_const.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_volatile.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_assignable.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_implicitly_default_constructible.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_nothrow_assignable.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_nothrow_constructible.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_swappable.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_trivially_relocatable.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_trivially_copyable.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\cstdint \
  C:\dev\emsdk\upstream\lib\clang\21\include\stdint.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\stdint.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\bits\alltypes.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\bits\stdint.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\unwrap_ref.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__utility\move.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__undef_macros \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__utility\piecewise_construct.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__utility\swap.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\cstring \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_constant_evaluated.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\compat\string.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\string.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\string.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\features.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\strings.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__functional\operations.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__functional\binary_function.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\desugars_to.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\allocator_traits.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\construct_at.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\access.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\addressof.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__new\placement_new_delete.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\pointer_traits.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__cstddef\ptrdiff_t.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_class.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\detected_or.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_empty.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\make_unsigned.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_unsigned.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_arithmetic.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_floating_point.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\type_list.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\limits \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_signed.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\version \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\type_traits \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\add_cv_quals.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\aligned_storage.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\aligned_union.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\alignment_of.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\extent.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\has_virtual_destructor.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_abstract.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_compound.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_fundamental.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_null_pointer.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_destructible.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\remove_all_extents.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_literal_type.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_nothrow_destructible.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_object.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_pod.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_pointer.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_polymorphic.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_scalar.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_standard_layout.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_trivial.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_trivially_assignable.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_trivially_constructible.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_trivially_destructible.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_union.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\make_signed.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\rank.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\remove_const.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\remove_pointer.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\remove_volatile.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\result_of.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_final.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\disjunction.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\has_unique_object_representation.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_aggregate.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\negation.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\array_cookie.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\auto_ptr.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\compressed_pair.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\datasizeof.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\dependent_type.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_bounded_array.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_unbounded_array.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__utility\private_constructor_tag.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__new\exceptions.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__exception\exception.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__ostream\put_character_sequence.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__fwd\ostream.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__fwd\string.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__fwd\memory_resource.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\ostreambuf_iterator.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__fwd\ios.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__fwd\streambuf.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\iterator.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\iterator_traits.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__concepts\arithmetic.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_signed_integer.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_unsigned_integer.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__concepts\constructible.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__concepts\destructible.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__concepts\copyable.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__concepts\assignable.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__concepts\movable.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__concepts\swappable.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__concepts\class_or_enum.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__utility\exchange.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\incrementable_traits.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_primary_template.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_valid_expansion.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\readable_traits.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\iosfwd \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__fwd\fstream.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__fwd\istream.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__fwd\sstream.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__std_mbstate_t.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__mbstate_t.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__locale_dir\pad_and_output.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\ios \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__ios\fpos.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__locale \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__locale_dir\locale_base_api.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\compat\xlocale.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\locale.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__locale_dir\locale_base_api\bsd_locale_fallbacks.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\compat\stdarg.h \
  C:\dev\emsdk\upstream\lib\clang\21\include\stdarg.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\stdarg.h \
  C:\dev\emsdk\upstream\lib\clang\21\include\__stdarg_header_macro.h \
  C:\dev\emsdk\upstream\lib\clang\21\include\__stdarg___gnuc_va_list.h \
  C:\dev\emsdk\upstream\lib\clang\21\include\__stdarg_va_list.h \
  C:\dev\emsdk\upstream\lib\clang\21\include\__stdarg_va_arg.h \
  C:\dev\emsdk\upstream\lib\clang\21\include\__stdarg___va_copy.h \
  C:\dev\emsdk\upstream\lib\clang\21\include\__stdarg_va_copy.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\stdio.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\stdio.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\wasi\api.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\stddef.h \
  C:\dev\emsdk\upstream\lib\clang\21\include\stddef.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\stddef.h \
  C:\dev\emsdk\upstream\lib\clang\21\include\__stddef_header_macro.h \
  C:\dev\emsdk\upstream\lib\clang\21\include\__stddef_ptrdiff_t.h \
  C:\dev\emsdk\upstream\lib\clang\21\include\__stddef_size_t.h \
  C:\dev\emsdk\upstream\lib\clang\21\include\__stddef_wchar_t.h \
  C:\dev\emsdk\upstream\lib\clang\21\include\__stddef_null.h \
  C:\dev\emsdk\upstream\lib\clang\21\include\__stddef_nullptr_t.h \
  C:\dev\emsdk\upstream\lib\clang\21\include\__stddef_max_align_t.h \
  C:\dev\emsdk\upstream\lib\clang\21\include\__stddef_offsetof.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\compat\stdlib.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\stdlib.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\stdlib.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\alloca.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\cwchar \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_equality_comparable.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\cwctype \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\cctype \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\ctype.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\ctype.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\wctype.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\wctype.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\wchar.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\wchar.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\cstddef \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__cstddef\byte.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__fwd\byte.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__cstddef\max_align_t.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\compat\time.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\time.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\shared_count.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\typeinfo \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\cstdlib \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__mutex\once_flag.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__functional\invoke.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\tuple \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\allocator_arg_t.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\uses_allocator.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__tuple\find_index.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__tuple\ignore.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__tuple\make_tuple_types.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__tuple\tuple_element.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__tuple\sfinae_helpers.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__tuple\tuple_like_ext.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\lazy.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\maybe_const.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\compare \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\cmath \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__math\hypot.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__math\abs.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__math\exponential_functions.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\promote.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__math\min_max.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__math\roots.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__math\special_functions.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__math\copysign.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__math\traits.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\compat\math.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\math.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\math.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__math\error_functions.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__math\fdim.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__math\fma.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__math\gamma.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__math\hyperbolic_functions.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__math\inverse_hyperbolic_functions.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__math\inverse_trigonometric_functions.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__math\logarithms.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__math\modulo.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__math\remainder.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__math\rounding_functions.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__math\trigonometric_functions.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\exception \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__exception\exception_ptr.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__exception\nested_exception.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__exception\terminate.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\new \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__new\align_val_t.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__new\allocate.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__new\global_new_delete.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__new\nothrow_t.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__utility\element_count.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__new\new_handler.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__new\interference_size.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__new\launder.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\utility \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__utility\rel_ops.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__utility\as_const.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__utility\in_place.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\initializer_list \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__utility\no_destroy.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\clocale \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\string \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\max.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\comp.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\comp_ref_type.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\max_element.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_callable.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\min.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\min_element.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__functional\identity.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\remove.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\find.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\find_segment_if.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\segmented_iterator.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\unwrap_iter.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__bit\countr.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__bit\rotate.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__bit\invert_if.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__fwd\bit_reference.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__string\constexpr_c_functions.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_always_bitcastable.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_trivially_lexicographically_comparable.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__utility\is_pointer_in_range.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__utility\is_valid_range.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\find_if.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\remove_if.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__debug_utils\sanitizers.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__format\enable_insertable.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\bounded_iter.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\distance.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\concepts.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__concepts\derived_from.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__concepts\invocable.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__concepts\predicate.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__concepts\regular.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__concepts\semiregular.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__concepts\relation.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\iter_move.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__ranges\access.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__ranges\enable_borrowed_range.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__utility\auto_cast.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__ranges\concepts.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__ranges\data.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__ranges\enable_view.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__ranges\size.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\reverse_iterator.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\advance.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__utility\convert_to_integral.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__utility\unreachable.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\iter_swap.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\next.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\prev.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__ranges\subrange.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__fwd\subrange.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__ranges\dangling.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__ranges\view_interface.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__ranges\empty.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\wrap_iter.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\allocate_at_least.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\allocator.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\noexcept_move_assign_container.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\swap_allocator.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory_resource\polymorphic_allocator.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory_resource\memory_resource.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__utility\exception_guard.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__ranges\container_compatible_range.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__ranges\from_range.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__string\char_traits.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\fill_n.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\find_end.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\iterator_operations.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\iter_swap.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\ranges_iterator_concept.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\find_first_of.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\cstdio \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__string\extern_template_lists.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_allocator.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__utility\scope_guard.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\climits \
  C:\dev\emsdk\upstream\lib\clang\21\include\limits.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\limits.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\bits\limits.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\stdexcept \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\string_view \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__fwd\string_view.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\data.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\empty.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\reverse_access.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\size.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\algorithm \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\adjacent_find.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\all_of.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\any_of.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\binary_search.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\lower_bound.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\half_positive.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\copy.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\copy_move_common.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\unwrap_range.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\for_each_segment.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\copy_backward.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\copy_if.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\copy_n.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\count.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__bit\popcount.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\count_if.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\equal.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\equal_range.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\upper_bound.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\fill.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\find_if_not.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\for_each.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__ranges\movable_box.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\optional \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\atomic \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__atomic\aliases.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__atomic\atomic.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__atomic\atomic_sync.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__atomic\contention_t.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__atomic\support.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__atomic\support\c11.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__atomic\memory_order.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__atomic\to_gcc_order.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__chrono\duration.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\ratio \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__thread\poll_with_backoff.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__chrono\high_resolution_clock.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__chrono\steady_clock.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__chrono\time_point.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__chrono\system_clock.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\ctime \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__atomic\check_memory_order.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__atomic\is_always_lock_free.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__atomic\atomic_lock_free.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__atomic\atomic_flag.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__thread\support.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__thread\support\pthread.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__chrono\convert_to_timespec.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\errno.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\errno.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\bits\errno.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\pthread.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\sched.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__atomic\atomic_init.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__atomic\fence.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__atomic\kill_dependency.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\concepts \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\iterator \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\back_insert_iterator.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\front_insert_iterator.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\insert_iterator.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\istream_iterator.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\default_sentinel.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\istreambuf_iterator.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\move_iterator.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\move_sentinel.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\ostream_iterator.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\variant \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__fwd\variant.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__utility\forward_like.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__variant\monostate.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\memory \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\align.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\inout_ptr.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\shared_ptr.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__functional\reference_wrapper.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__functional\weak_result_type.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\allocation_guard.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\allocator_destructor.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\uninitialized_algorithms.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\move.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_specialization.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\out_ptr.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\raw_storage_iterator.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\temporary_buffer.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\unique_temporary_buffer.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\generate.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\generate_n.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\includes.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\inplace_merge.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\rotate.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\move_backward.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\swap_ranges.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\destruct_n.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\is_heap.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\is_heap_until.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\is_partitioned.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\is_permutation.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\is_sorted.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\is_sorted_until.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\lexicographical_compare.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\mismatch.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\simd_utils.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__bit\bit_cast.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__bit\countl.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\aliasing_iterator.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\make_heap.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\sift_down.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\merge.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\minmax.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\minmax_element.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\next_permutation.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\reverse.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\none_of.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\nth_element.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\sort.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\partial_sort.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\sort_heap.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\pop_heap.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\push_heap.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__debug_utils\strict_weak_ordering_check.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__debug_utils\randomize_range.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__bit\blsr.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__functional\ranges_operations.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\partial_sort_copy.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\make_projected.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\partition.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\partition_copy.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\partition_point.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\prev_permutation.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\remove_copy.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\remove_copy_if.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\replace.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\replace_copy.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\replace_copy_if.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\replace_if.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\reverse_copy.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\rotate_copy.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\search.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\search_n.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\set_difference.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\set_intersection.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\set_symmetric_difference.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\set_union.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\shuffle.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__random\uniform_int_distribution.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__random\is_valid.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__random\log2.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\stable_partition.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\stable_sort.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\radix_sort.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__bit\bit_log2.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__numeric\partial_sum.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\transform.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\unique.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\unique_copy.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\clamp.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\for_each_n.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\pstl.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\sample.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\bit \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__system_error\error_category.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__system_error\error_code.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__system_error\errc.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\cerrno \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__system_error\error_condition.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__system_error\system_error.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\mutex \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__condition_variable\condition_variable.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__mutex\mutex.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__mutex\unique_lock.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__mutex\tag_types.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__system_error\throw_system_error.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__mutex\lock_guard.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__thread\id.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\system_error \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\bitset \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__bit_reference \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\is_char_like_type.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\locale \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\streambuf \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\cstdarg \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\format \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\array \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\lexicographical_compare_three_way.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\three_way_comp_ref_type.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\static_bounded_iter.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\queue \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\ranges_copy.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\in_out_result.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__fwd\deque.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__fwd\queue.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__fwd\vector.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\deque \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__memory\temp_value.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__split_buffer \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\container_traits.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\functional \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__functional\binary_negate.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__functional\bind.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__functional\binder1st.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__functional\binder2nd.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__functional\mem_fn.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__functional\mem_fun_ref.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__functional\pointer_to_binary_function.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__functional\pointer_to_unary_function.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__functional\unary_negate.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__functional\function.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\strip_signature.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__functional\boyer_moore_searcher.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__vector\vector.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__algorithm\ranges_copy_n.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\unreachable_sentinel.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__vector\comparison.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__vector\container_traits.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__vector\swap.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\unordered_map \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__functional\is_transparent.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__hash_table \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\can_extract_key.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__type_traits\remove_const_ref.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\erase_if_container.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__iterator\ranges_iterator_traits.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__node_handle \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__functional\default_searcher.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__functional\not_fn.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__functional\perfect_forward.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\vector \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__vector\vector_bool.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__vector\pmr.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\stack \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__fwd\stack.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\print \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\unistd.h \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Timer \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\build_wasm\build_include\osgEarth\BuildConfig \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\ref_ptr \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Map \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\GeoData \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\GeoCommon \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\cfloat \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\float.h \
  C:\dev\emsdk\upstream\lib\clang\21\include\float.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\float.h \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Bounds \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\BoundingBox \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec3 \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec3f \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec2f \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Math \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec3d \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec2d \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\SpatialReference \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Units \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Config \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\optional \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\StringUtils \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec4 \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec4f \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec4ub \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\sstream \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\istream \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\iomanip \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\map \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__tree \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\set \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Ellipsoid \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Matrix \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Matrixd \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Object \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\CopyOp \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec4d \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Quat \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Matrixf \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\VerticalDatum \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Geoid \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Shape \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Plane \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\BoundingSphere \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Array \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\MixinVector \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec2b \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec3b \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec4b \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec2s \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec3s \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec4s \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec2i \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec3i \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec4i \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec2ub \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec3ub \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec2us \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec3us \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec4us \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec2ui \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec3ui \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec4ui \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Vec2 \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\BufferObject \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\GL \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Types \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\GLES2\gl2.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\GLES2\gl2platform.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\KHR\khrplatform.h \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\GLExtensions \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\GLDefines \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\MatrixTemplate \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\observer_ptr \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Observer \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\buffered_value \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\DisplaySettings \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\FrameStamp \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\GLObjects \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\list \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Threading \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\shared_mutex \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\weejobs.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\chrono \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__chrono\file_clock.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\forward_list \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\condition_variable \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__stop_token\stop_callback.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__stop_token\intrusive_shared_ptr.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__stop_token\stop_state.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__stop_token\atomic_unique_lock.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__stop_token\intrusive_list_view.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__stop_token\stop_token.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\thread \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__thread\this_thread.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__thread\thread.h \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Containers \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\CoordinateSystemNode \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Group \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Node \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\StateSet \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\StateAttribute \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Callback \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\UserDataContainer \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Shader \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Uniform \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\UniformBase \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\NodeVisitor \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\ValueMap \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\ValueObject \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\ValueStack \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\ImageUtils \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Image \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Texture \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\TextureAttribute \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\GraphicsContext \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\State \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\VertexArrayState \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\AttributeDispatchers \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\ShaderComposer \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Program \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Polytope \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\fast_back_stack \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Viewport \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\GraphicsCostEstimator \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\GraphicsThread \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\OperationThread \
  E:\project\my-earth202507\third_party\wasm_dep\include\OpenThreads\Thread \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\sys\types.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\endian.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\sys\select.h \
  E:\project\my-earth202507\third_party\wasm_dep\include\OpenThreads\Affinity \
  E:\project\my-earth202507\third_party\wasm_dep\include\OpenThreads\Barrier \
  E:\project\my-earth202507\third_party\wasm_dep\include\OpenThreads\Condition \
  E:\project\my-earth202507\third_party\wasm_dep\include\OpenThreads\Block \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Texture2DArray \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgDB\ReaderWriter \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\ScriptEngine \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgDB\AuthenticationMap \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgDB\Export \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Status \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Profile \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\MapCallback \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Revisioning \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\ElevationPool \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Elevation \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\TileKey \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Version \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Math \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Geometry \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Geometry \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Drawable \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\PrimitiveSet \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\RenderInfo \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\View \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Camera \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Transform \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\ColorMask \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\CullSettings \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\ClearNode \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Stats \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Light \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Texture2D \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\ElevationLayer \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\TileLayer \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\CachePolicy \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\DateTime \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgDB\Options \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgDB\Callbacks \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgDB\FileCache \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgDB\DatabaseRevisions \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgDB\ObjectCache \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\ObserverNodePath \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\VisibleLayer \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Layer \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\PluginLoader \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Registry \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\ShaderGenerator \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\StateSetCache \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\VirtualProgram \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\unordered_set \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Callbacks \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgDB\Registry \
  E:\project\my-earth202507\third_party\wasm_dep\include\OpenThreads\ReentrantMutex \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\ArgumentParser \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\ApplicationUsage \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\KdTree \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgDB\DynamicLibrary \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgDB\DotOsgWrapper \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgDB\Input \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgDB\Output \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgDB\fstream \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\fstream \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__filesystem\path.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\filesystem \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__filesystem\copy_options.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__filesystem\directory_entry.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__filesystem\file_status.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__filesystem\file_type.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__filesystem\perms.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__filesystem\file_time_type.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__filesystem\filesystem_error.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__filesystem\operations.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__filesystem\perm_options.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__filesystem\space_info.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__filesystem\directory_iterator.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__filesystem\directory_options.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__filesystem\path_iterator.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__filesystem\recursive_directory_iterator.h \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\__filesystem\u8path.h \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgDB\ObjectWrapper \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgDB\Serializer \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgDB\InputStream \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Endian \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgDB\StreamOperator \
  C:\dev\emsdk\upstream\emscripten\cache\sysroot\include\c++\v1\iostream \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgDB\DataTypes \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgDB\OutputStream \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgDB\SharedStateManager \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Geode \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgDB\ImageProcessor \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgDB\FileNameUtils \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Cache \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\CacheBin \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\IOTypes \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\SceneGraphCallback \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\PagedLOD \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\LOD \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\LayerShader \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\URI \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\TerrainResources \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\PBRMaterial \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\MemCache \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Script \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\ScriptEngine \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Feature \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Style \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Color \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Query \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\IconSymbol \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\InstanceSymbol \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Symbol \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Expression \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Tags \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\PointSymbol \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Fill \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\LineSymbol \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Stroke \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\PolygonSymbol \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\ModelSymbol \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\ExtrusionSymbol \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\AltitudeSymbol \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\TextSymbol \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgText\Text \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgText\TextBase \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgText\String \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgText\Export \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgText\KerningType \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgText\Font \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\TexEnv \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgText\Glyph \
  E:\project\my-earth202507\third_party\wasm_dep\include\osgText\Style \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Skins \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Resource \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\RenderSymbol \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\DepthOffset \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\CoverageSymbol \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\BBoxSymbol \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\FeatureSource \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Filter \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\FilterContext \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\ResourceCache \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\InstanceResource \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\ResourceLibrary \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\ModelResource \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Random \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\ShaderUtils \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\NodeCallback \
  E:\project\my-earth202507\third_party\wasm_dep\include\osg\Material \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\FeatureCursor \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\Progress \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\LayerReference \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\StyleSheet \
  E:\project\my-earth202507\myosgearth_degdalproj20250714\src\osgEarth\StyleSelector
