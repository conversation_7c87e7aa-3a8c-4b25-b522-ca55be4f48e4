# FindGDAL.cmake - WebAssembly版本
# 查找GDAL库，支持外部WASM依赖

# WebAssembly外部依赖支持
IF(USE_EXTERNAL_WASM_DEPENDS)
    message(STATUS "Using external WASM dependencies for GDAL")
    
    # 设置GDAL根目录
    if(NOT GDAL_DIR)
        set(GDAL_DIR "$ENV{GDAL_DIR}")
        if(NOT GDAL_DIR)
            message(FATAL_ERROR "GDAL_DIR not set for external WASM dependencies")
        endif()
    endif()
    
    # 设置包含目录
    SET(GDAL_INCLUDE_DIR "${GDAL_DIR}/include")
    
    # 设置库文件路径 - 强制使用静态库
    SET(GDAL_LIBRARY "${GDAL_DIR}/lib/libgdal.a")
    
    # 设置找到标志
    SET(GDAL_FOUND TRUE)
    
    # 设置版本信息
    SET(GDAL_VERSION "3.4.0")  # 假设版本，实际应该从头文件读取
    
    message(STATUS "GDAL WASM library configured:")
    message(STATUS "  GDAL_DIR: ${GDAL_DIR}")
    message(STATUS "  GDAL_INCLUDE_DIR: ${GDAL_INCLUDE_DIR}")
    message(STATUS "  GDAL_LIBRARY: ${GDAL_LIBRARY}")
    
ELSE()
    # 标准GDAL查找逻辑
    message(STATUS "Using standard GDAL find logic")
    
    # 查找GDAL头文件
    FIND_PATH(GDAL_INCLUDE_DIR gdal.h
        HINTS
            ENV GDAL_DIR
            ENV GDAL_ROOT
        PATH_SUFFIXES 
            include
            include/gdal
        PATHS
            ~/Library/Frameworks/gdal.framework/Headers
            /Library/Frameworks/gdal.framework/Headers
            /usr/local/include
            /usr/include
            /sw/include # Fink
            /opt/local/include # DarwinPorts
            /opt/csw/include # Blastwave
            /opt/include
            [HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session\ Manager\\Environment;GDAL_ROOT]/include
            ${CMAKE_INSTALL_PREFIX}/include
    )
    
    # 查找GDAL库文件
    FIND_LIBRARY(GDAL_LIBRARY
        NAMES gdal gdal_i gdal1.5.0 gdal1.4.0 gdal1.3.2 GDAL
        HINTS
            ENV GDAL_DIR
            ENV GDAL_ROOT
        PATH_SUFFIXES lib64 lib
        PATHS
            ~/Library/Frameworks/gdal.framework
            /Library/Frameworks/gdal.framework
            /usr/local
            /usr
            /sw
            /opt/local
            /opt/csw
            /opt
            [HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session\ Manager\\Environment;GDAL_ROOT]/lib
            ${CMAKE_INSTALL_PREFIX}/lib
    )
    
    # 设置找到标志
    SET(GDAL_FOUND "NO")
    IF(GDAL_LIBRARY AND GDAL_INCLUDE_DIR)
        SET(GDAL_FOUND "YES")
    ENDIF()
    
    # 尝试获取GDAL版本
    IF(GDAL_INCLUDE_DIR AND EXISTS "${GDAL_INCLUDE_DIR}/gdal_version.h")
        FILE(READ "${GDAL_INCLUDE_DIR}/gdal_version.h" GDAL_VERSION_H_CONTENTS)
        STRING(REGEX MATCH "GDAL_RELEASE_NAME[ \t]+\"([^\"]+)\""
               GDAL_RELEASE_NAME ${GDAL_VERSION_H_CONTENTS})
        IF(GDAL_RELEASE_NAME)
            STRING(REGEX REPLACE ".*\"([^\"]+)\".*" "\\1" GDAL_VERSION ${GDAL_RELEASE_NAME})
        ENDIF()
    ENDIF()
    
ENDIF()

# 设置库列表
SET(GDAL_LIBRARIES ${GDAL_LIBRARY})

# 设置包含目录列表
SET(GDAL_INCLUDE_DIRS ${GDAL_INCLUDE_DIR})

# 处理标准参数
include(FindPackageHandleStandardArgs)
FIND_PACKAGE_HANDLE_STANDARD_ARGS(GDAL 
    REQUIRED_VARS GDAL_LIBRARY GDAL_INCLUDE_DIR
    VERSION_VAR GDAL_VERSION
)

# 标记为高级变量
MARK_AS_ADVANCED(
    GDAL_INCLUDE_DIR
    GDAL_LIBRARY
)
