# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.26

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: osgearth_simple_test
# Configurations: Release
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Release
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/build_wasm/test_simple/
# =============================================================================
# Object build statements for EXECUTABLE target simple_test


#############################################
# Order-only phony target for simple_test

build cmake_object_order_depends_target_simple_test: phony || CMakeFiles/simple_test.dir

build CMakeFiles/simple_test.dir/osgearth_simple_test.cpp.o: CXX_COMPILER__simple_test_unscanned_Release F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/build_wasm/test_simple/osgearth_simple_test.cpp || cmake_object_order_depends_target_simple_test
  DEP_FILE = CMakeFiles\simple_test.dir\osgearth_simple_test.cpp.o.d
  FLAGS = -s USE_PTHREADS=1 -s PTHREAD_POOL_SIZE=4 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=1GB -s STACK_SIZE=8MB -s DISABLE_EXCEPTION_CATCHING=0 -s ASSERTIONS=1 -s WASM=1 -s MODULARIZE=0 -s ENVIRONMENT='web,worker' -s EXPORTED_FUNCTIONS=['_main'] -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap'] -O3 -DNDEBUG -std=gnu++20
  OBJECT_DIR = CMakeFiles\simple_test.dir
  OBJECT_FILE_DIR = CMakeFiles\simple_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target simple_test


#############################################
# Link the executable simple_test.js

build simple_test.js: CXX_EXECUTABLE_LINKER__simple_test_Release CMakeFiles/simple_test.dir/osgearth_simple_test.cpp.o
  FLAGS = -s USE_PTHREADS=1 -s PTHREAD_POOL_SIZE=4 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=1GB -s STACK_SIZE=8MB -s DISABLE_EXCEPTION_CATCHING=0 -s ASSERTIONS=1 -s WASM=1 -s MODULARIZE=0 -s ENVIRONMENT='web,worker' -s EXPORTED_FUNCTIONS=['_main'] -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap'] -O3 -DNDEBUG
  LINK_FLAGS = -s USE_PTHREADS=1 -s PTHREAD_POOL_SIZE=4 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=1GB -s STACK_SIZE=8MB -s DISABLE_EXCEPTION_CATCHING=0 -s ASSERTIONS=1 -s WASM=1 -s MODULARIZE=0 -s ENVIRONMENT='web,worker' -s EXPORTED_FUNCTIONS=['_main'] -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap']
  OBJECT_DIR = CMakeFiles\simple_test.dir
  POST_BUILD = cmd.exe /C "cd /D F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\samples\my_osgearth2\build_wasm\test_simple && "C:\Program Files\CMake\bin\cmake.exe" -E echo "Copying WebAssembly files to redist_wasm..." && "C:\Program Files\CMake\bin\cmake.exe" -E copy F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/build_wasm/test_simple/build_wasm/simple_test.js F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/build_wasm/test_simple/redist_wasm/ && "C:\Program Files\CMake\bin\cmake.exe" -E copy F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/build_wasm/test_simple/build_wasm/simple_test.wasm F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/build_wasm/test_simple/redist_wasm/"
  PRE_LINK = cd .
  TARGET_FILE = simple_test.js
  TARGET_PDB = simple_test.js.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\samples\my_osgearth2\build_wasm\test_simple && "C:\Program Files\CMake\bin\cmake-gui.exe" -SF:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\samples\my_osgearth2\build_wasm\test_simple -BF:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\samples\my_osgearth2\build_wasm\test_simple"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\samples\my_osgearth2\build_wasm\test_simple && "C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -SF:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\samples\my_osgearth2\build_wasm\test_simple -BF:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\samples\my_osgearth2\build_wasm\test_simple"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build simple_test: phony simple_test.js

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/build_wasm/test_simple

build all: phony simple_test.js

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCCompiler.cmake.in C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCXXCompiler.cmake.in C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCXXInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCommonLanguageInclude.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCXXCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeDetermineSystem.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeFindBinUtils.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeGenericSystem.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeInitializeConfigs.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeLanguageInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeNinjaFindMake.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystem.cmake.in C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInitialize.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeTestCCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-C.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-CXX.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/GNU.cmake C$:/dev/emsdk/upstream/emscripten/cmake/Modules/Platform/Emscripten.cmake CMakeCache.txt CMakeFiles/3.26.4/CMakeCCompiler.cmake CMakeFiles/3.26.4/CMakeCXXCompiler.cmake CMakeFiles/3.26.4/CMakeSystem.cmake CMakeLists.txt
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCCompiler.cmake.in C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCXXCompiler.cmake.in C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCXXInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCommonLanguageInclude.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCXXCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeDetermineSystem.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeFindBinUtils.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeGenericSystem.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeInitializeConfigs.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeLanguageInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeNinjaFindMake.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystem.cmake.in C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInitialize.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeTestCCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-C.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-CXX.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/GNU.cmake C$:/dev/emsdk/upstream/emscripten/cmake/Modules/Platform/Emscripten.cmake CMakeCache.txt CMakeFiles/3.26.4/CMakeCCompiler.cmake CMakeFiles/3.26.4/CMakeCXXCompiler.cmake CMakeFiles/3.26.4/CMakeSystem.cmake CMakeLists.txt: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
