@echo off
echo ========================================
echo osgEarth WebAssembly ���̹߳����ű�
echo ֧�֣�1GB��ʼ�ڴ棬�ڴ��������8�̳߳�
echo ========================================

REM ���Emscripten����
echo.
echo ���Emscripten����...
where emcc >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ����δ�ҵ�emcc����
    echo ��ȷ���Ѱ�װEmscripten�������
    pause
    exit /b 1
)

echo ? Emscripten�������ͨ��
emcc --version

REM ���û�������
echo.
echo ���ù�����������...
set EMSCRIPTEN=1
set CMAKE_TOOLCHAIN_FILE=C:\dev\emsdk\upstream\emscripten\cmake\Modules\Platform\Emscripten.cmake

REM ����������·��
echo.
echo ����������·��...
set OSG_DIR=E:\project\my-earth202507\third_party\wasm_dep
set GDAL_DIR=E:\project\my-earth202507\third_party\wasm_dep
set GEOS_DIR=E:\project\my-earth202507\third_party\wasm_dep

echo OSG_DIR: %OSG_DIR%
echo GDAL_DIR: %GDAL_DIR%
echo GEOS_DIR: %GEOS_DIR%

REM ��֤���������
echo.
echo ��֤������...
if not exist "%OSG_DIR%\lib\libosg.a" (
    echo ����OSG�ⲻ���� %OSG_DIR%\lib\libosg.a
    pause
    exit /b 1
)
echo ? OSG����֤ͨ��

REM ��������������Ŀ¼
echo.
echo ׼������Ŀ¼...
if exist "build_wasm_mt" (
    rmdir /s /q build_wasm_mt
    echo �����ɵĹ���Ŀ¼
)
mkdir build_wasm_mt

if exist "redist_wasm" (
    rmdir /s /q redist_wasm
    echo �����ɵķ���Ŀ¼
)
mkdir redist_wasm

cd build_wasm_mt

REM ����CMake - ���̰߳汾
echo.
echo ����CMake��Ŀ�����߳�֧�֣�...
cmake .. ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_TOOLCHAIN_FILE=%CMAKE_TOOLCHAIN_FILE% ^
    -DUSE_EXTERNAL_WASM_DEPENDS=ON ^
    -DOSGEARTH_BUILD_SHARED_LIBS=OFF ^
    -DOSG_DIR=%OSG_DIR% ^
    -DGDAL_DIR=%GDAL_DIR% ^
    -DGEOS_DIR=%GEOS_DIR% ^
    -DCMAKE_MODULE_PATH="%CD%\..\cmake;%CD%\..\CMakeModules" ^
    -G Ninja

if %ERRORLEVEL% neq 0 (
    echo CMake����ʧ��
    cd ..
    pause
    exit /b 1
)

echo ? CMake���óɹ�

REM ������Ŀ
echo.
echo ��ʼ�������߳�WebAssembly��Ŀ...
echo �������Ҫ������ʱ��...
ninja -j4

if %ERRORLEVEL% neq 0 (
    echo ����ʧ�ܣ����Ե��̹߳���...
    ninja
    
    if %ERRORLEVEL% neq 0 (
        echo ����ʧ��
        cd ..
        pause
        exit /b 1
    )
)

echo ? �����ɹ�

REM �����ļ�������Ŀ¼
echo.
echo �����ļ�������Ŀ¼...
cd ..

REM ���ƾ�̬��
if exist "build_wasm_mt\src\osgEarth\libosgEarth.a" (
    copy "build_wasm_mt\src\osgEarth\libosgEarth.a" "redist_wasm\"
    echo ���� libosgEarth.a
)

REM ����Ӧ�ó����ļ�
for %%f in (build_wasm_mt\src\applications\*\*.html) do (
    copy "%%f" "redist_wasm\"
    echo ���� %%~nxf
)

for %%f in (build_wasm_mt\src\applications\*\*.js) do (
    copy "%%f" "redist_wasm\"
    echo ���� %%~nxf
)

for %%f in (build_wasm_mt\src\applications\*\*.wasm) do (
    copy "%%f" "redist_wasm\"
    echo ���� %%~nxf
)

for %%f in (build_wasm_mt\src\applications\*\*.data) do (
    copy "%%f" "redist_wasm\"
    echo ���� %%~nxf
)

REM ��ʾ���
echo.
echo ========================================
echo ?? ���߳�WebAssembly������ɣ�
echo ========================================
echo.
echo �������ã�
echo   - ���߳�֧�֣����ã�8�̳߳أ�
echo   - ��ʼ�ڴ棺1GB
echo   - ����ڴ棺4GB
echo   - �ڴ�����������
echo   - �����ڴ棺����
echo   - ԭ�Ӳ���������
echo.
echo ���ɵ��ļ���
dir redist_wasm
echo.
echo Ҫ����Ӧ�ó��������У�
echo   cd redist_wasm
echo   python -m http.server 8080
echo   Ȼ����������з��� http://localhost:8080
echo.
pause
