<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OSGEarth WebAssembly 测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #000;
            color: white;
            font-family: Arial, sans-serif;
        }

        #canvas {
            width: 800px;
            height: 600px;
            background-color: #333;
            border: 1px solid #666;
        }

        #log {
            background-color: #222;
            color: #0f0;
            padding: 10px;
            margin-top: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>

<body>
    <h1>OSGEarth WebAssembly 测试页面</h1>

    <canvas id="canvas" width="800" height="600"></canvas>

    <div id="log"></div>

    <script>
        // 日志函数
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        // 获取canvas
        const canvas = document.getElementById('canvas');

        // WebAssembly模块配置
        var Module = {
            canvas: canvas,

            // 预运行函数
            preRun: [],

            // 后运行函数
            postRun: [function () {
                log('OSGEarth WebAssembly模块加载完成！');
            }],

            // 打印函数
            print: function (text) {
                log('OSGEarth: ' + text);
            },

            // 错误打印函数
            printErr: function (text) {
                log('OSGEarth Error: ' + text);
            },

            // 加载进度回调
            setStatus: function (text) {
                if (text) {
                    log('Status: ' + text);
                }
            },

            // 总下载进度
            totalDependencies: 0,
            monitorRunDependencies: function (left) {
                this.totalDependencies = Math.max(this.totalDependencies, left);
                if (left == 0) {
                    log('所有依赖加载完成');
                } else {
                    const progress = Math.round((this.totalDependencies - left) / this.totalDependencies * 100);
                    log(`加载进度: ${progress}% (剩余: ${left})`);
                }
            },

            // 错误处理
            onAbort: function (what) {
                log('模块中止: ' + what);
            },

            onRuntimeInitialized: function () {
                log('运行时初始化完成');
            }
        };

        // 开始加载
        log('开始加载OSGEarth WebAssembly模块...');

        // 检查浏览器支持
        if (!window.WebAssembly) {
            log('错误: 浏览器不支持WebAssembly');
        } else {
            log('浏览器支持WebAssembly');

            // 加载模块脚本
            const script = document.createElement('script');
            script.src = 'osgearth_myviewer_wasm.js';

            script.onload = function () {
                log('JavaScript模块加载完成');

                // 等待模块初始化完成
                if (typeof Module !== 'undefined') {
                    log('找到Module对象');

                    // 等待模块完全加载
                    if (Module.calledRun) {
                        log('模块已经运行完成');
                        checkMainFunction();
                    } else {
                        log('等待模块运行完成...');
                        Module.onRuntimeInitialized = function () {
                            log('运行时初始化完成');
                            checkMainFunction();
                        };
                    }
                } else {
                    log('错误: 未找到Module对象');
                    setTimeout(function () {
                        if (typeof Module !== 'undefined') {
                            log('延迟找到Module对象');
                            checkMainFunction();
                        } else {
                            log('仍然未找到Module对象');
                        }
                    }, 1000);
                }
            };

            script.onerror = function (e) {
                log('JavaScript模块加载失败: ' + e.message);
            };

            document.head.appendChild(script);
        }

        function checkMainFunction() {
            // 尝试调用main函数
            if (Module._main) {
                log('找到main函数，开始调用...');
                try {
                    Module._main();
                    log('main函数调用成功');

                    // 测试新的导出函数
                    setTimeout(function () {
                        testExportedFunctions();
                    }, 3000); // 等待3秒让OSGEarth初始化

                } catch (e) {
                    log('main函数调用失败: ' + e.message);
                    log('错误堆栈: ' + e.stack);
                }
            } else {
                log('警告: 未找到main函数');
                log('可用的Module属性: ' + Object.keys(Module).filter(k => k.startsWith('_')).slice(0, 10));
            }
        }

        function testExportedFunctions() {
            log('=== 测试导出函数 ===');

            try {
                // 测试getOSGEarthStatus
                if (Module._getOSGEarthStatus) {
                    const statusPtr = Module._getOSGEarthStatus();
                    const status = Module.UTF8ToString(statusPtr);
                    log('OSGEarth状态: ' + status);
                } else {
                    log('警告: 未找到_getOSGEarthStatus函数');
                }

                // 测试getMapInfo
                if (Module._getMapInfo) {
                    const infoPtr = Module._getMapInfo();
                    const info = Module.UTF8ToString(infoPtr);
                    log('地图信息: ' + info);
                } else {
                    log('警告: 未找到_getMapInfo函数');
                }

                // 测试isRunning
                if (Module._isRunning) {
                    const running = Module._isRunning();
                    log('是否运行中: ' + (running ? '是' : '否'));
                } else {
                    log('警告: 未找到_isRunning函数');
                }

                // 测试forceUpdate
                if (Module._forceUpdate) {
                    Module._forceUpdate();
                    log('强制更新已调用');
                } else {
                    log('警告: 未找到_forceUpdate函数');
                }

            } catch (e) {
                log('测试导出函数时出错: ' + e.message);
            }

            log('=== 导出函数测试完成 ===');
        }

        // 错误处理
        window.addEventListener('error', function (e) {
            log('页面错误: ' + e.error + ' 在 ' + e.filename + ':' + e.lineno);
        });

        window.addEventListener('unhandledrejection', function (e) {
            log('未处理的Promise拒绝: ' + e.reason);
        });
    </script>
</body>

</html>