/* osgEarth
 * Copyright 2008-2014 Pelican Mapping
 * MIT License
 */
#pragma once

namespace osgEarth
{
    namespace Util
    {
        /**
         * Interface for querying terrain engine feature requirements.
         */
        struct TerrainEngineRequirements
        {
            bool fullDataAtFirstLod = true;
            bool elevationTextures = true;
            bool normalTextures = true;
            bool landCoverTextures = true;
            bool parentTextures = true;
            bool elevationBorder = false;
            bool tileMesh = false;
        };
    }
}
