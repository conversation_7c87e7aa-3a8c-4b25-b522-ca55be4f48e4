#pragma once

#include "SpatialReference.hpp"
#include <osg/Image>
#include <osg/Texture2D>
#include <osg/ref_ptr>
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <functional>

#ifdef EMSCRIPTEN
#include <emscripten/fetch.h>
#include <emscripten.h>
#endif

namespace EarthSample
{
  /**
   * 瓦片服务类型枚举
   */
  enum class TileServiceType
  {
    GOOGLE_SATELLITE,   // Google卫星影像
    GOOGLE_STREETS,     // Google街道地图
    GOOGLE_HYBRID,      // Google混合地图
    BING_AERIAL,        // Bing Maps正射影像
    BING_ROADS,         // Bing Maps街道地图
    BING_HYBRID,        // Bing Maps混合地图
    OSM_MAPNIK,         // OpenStreetMap标准瓦片
    OSM_CYCLE,          // OpenStreetMap自行车地图
    ESRI_WORLD_IMAGERY, // ESRI World Imagery
    ESRI_WORLD_TOPO,    // ESRI世界地形图
    MAPBOX_SATELLITE,   // Mapbox卫星影像
    MAPBOX_STREETS,     // Mapbox街道地图
    READYMAP_15M,       // ReadyMap 15m正射影像
    NATURAL_EARTH,      // Natural Earth
    STAMEN_TERRAIN,     // Stamen地形图
    STAMEN_WATERCOLOR,  // Stamen水彩地图
    CUSTOM              // 自定义瓦片服务
  };

  /**
   * 瓦片格式枚举
   */
  enum class TileFormat
  {
    PNG,
    JPEG,
    WebP,
    AUTO_DETECT
  };

  /**
   * 瓦片键类
   * 用于唯一标识一个瓦片
   */
  class TileKey
  {
  public:
    TileKey();
    TileKey(int level, int x, int y);

    // 获取瓦片参数
    int getLevel() const { return _level; }
    int getX() const { return _x; }
    int getY() const { return _y; }

    // 获取瓦片的地理范围
    GeoExtent getExtent() const;

    // 获取瓦片的四个子瓦片
    std::vector<TileKey> getSubKeys() const;

    // 获取父瓦片
    TileKey getParentKey() const;

    // 字符串表示
    std::string toString() const;

    // 比较操作符
    bool operator<(const TileKey &other) const;
    bool operator==(const TileKey &other) const;
    bool operator!=(const TileKey &other) const;

    // 从字符串解析
    static TileKey fromString(const std::string &str);

    // 从地理坐标计算瓦片键
    static TileKey fromLonLat(double lon, double lat, int level);

    // 检查瓦片键是否有效
    bool valid() const;

  private:
    int _level;
    int _x;
    int _y;

    // 地理范围计算
    void calculateExtent();
    mutable GeoExtent _extent;
    mutable bool _extentCalculated;
  };

  /**
   * 瓦片服务配置类
   */
  class TileServiceConfig
  {
  public:
    TileServiceConfig();
    TileServiceConfig(TileServiceType type);

    // 基本配置
    void setType(TileServiceType type) { _type = type; }
    TileServiceType getType() const { return _type; }

    void setName(const std::string &name) { _name = name; }
    const std::string &getName() const { return _name; }

    void setDescription(const std::string &desc) { _description = desc; }
    const std::string &getDescription() const { return _description; }

    // URL模板设置
    void setUrlTemplate(const std::string &urlTemplate) { _urlTemplate = urlTemplate; }
    const std::string &getUrlTemplate() const { return _urlTemplate; }

    // 格式设置
    void setFormat(TileFormat format) { _format = format; }
    TileFormat getFormat() const { return _format; }

    // 层级范围
    void setMinLevel(int level) { _minLevel = level; }
    int getMinLevel() const { return _minLevel; }

    void setMaxLevel(int level) { _maxLevel = level; }
    int getMaxLevel() const { return _maxLevel; }

    // 服务器列表（用于负载均衡）
    void setServers(const std::vector<std::string> &servers) { _servers = servers; }
    const std::vector<std::string> &getServers() const { return _servers; }

    // API密钥
    void setApiKey(const std::string &apiKey) { _apiKey = apiKey; }
    const std::string &getApiKey() const { return _apiKey; }

    // 自定义请求头
    void setHeaders(const std::map<std::string, std::string> &headers) { _headers = headers; }
    const std::map<std::string, std::string> &getHeaders() const { return _headers; }

    // 获取瓦片URL
    std::string getTileUrl(const TileKey &key) const;

    // 预定义配置
    static TileServiceConfig *createGoogleSatellite();
    static TileServiceConfig *createGoogleStreets();
    static TileServiceConfig *createBingAerial();
    static TileServiceConfig *createOSMMapnik();
    static TileServiceConfig *createESRIWorldImagery();
    static TileServiceConfig *createMapboxSatellite(const std::string &accessToken);

  private:
    TileServiceType _type;
    std::string _name;
    std::string _description;
    std::string _urlTemplate;
    TileFormat _format;
    int _minLevel;
    int _maxLevel;
    std::vector<std::string> _servers;
    std::string _apiKey;
    std::map<std::string, std::string> _headers;

    // 初始化预定义配置
    void initializePredefinedConfig();

    // URL模板替换
    std::string replaceUrlTemplate(const std::string &urlTemplate, const TileKey &key) const;

    // Bing Maps QuadKey计算
    std::string computeQuadKey(const TileKey &key) const;
  };

  /**
   * 瓦片数据类
   */
  class TileData
  {
  public:
    TileData();
    TileData(const TileKey &key, const std::vector<unsigned char> &data);

    // 瓦片键
    const TileKey &getKey() const { return _key; }
    void setKey(const TileKey &key) { _key = key; }

    // 瓦片数据
    const std::vector<unsigned char> &getData() const { return _data; }
    void setData(const std::vector<unsigned char> &data) { _data = data; }

    // 数据大小
    size_t getSize() const { return _data.size(); }

    // 是否有效
    bool valid() const { return !_data.empty(); }

    // 创建OSG图像
    osg::Image *createOSGImage() const;

    // 创建OSG纹理
    osg::Texture2D *createOSGTexture() const;

    // 时间戳
    void setTimestamp(double timestamp) { _timestamp = timestamp; }
    double getTimestamp() const { return _timestamp; }

    // 错误信息
    void setError(const std::string &error) { _error = error; }
    const std::string &getError() const { return _error; }
    bool hasError() const { return !_error.empty(); }

  private:
    TileKey _key;
    std::vector<unsigned char> _data;
    double _timestamp;
    std::string _error;
  };

  /**
   * 瓦片加载回调接口
   */
  class TileLoadCallback
  {
  public:
    virtual ~TileLoadCallback() = default;

    // 加载成功回调
    virtual void onTileLoaded(const TileData &tile) = 0;

    // 加载失败回调
    virtual void onTileLoadError(const TileKey &key, const std::string &error) = 0;

    // 加载进度回调
    virtual void onTileLoadProgress(const TileKey &key, double progress) {}
  };

  /**
   * 瓦片缓存接口
   */
  class TileCache
  {
  public:
    virtual ~TileCache() = default;

    // 获取瓦片
    virtual bool getTile(const TileKey &key, TileData &tile) = 0;

    // 存储瓦片
    virtual void putTile(const TileData &tile) = 0;

    // 检查瓦片是否存在
    virtual bool hasTile(const TileKey &key) = 0;

    // 清除缓存
    virtual void clear() = 0;

    // 获取缓存大小
    virtual size_t getSize() = 0;

    // 获取缓存项数量
    virtual size_t getCount() = 0;
  };

  /**
   * 内存瓦片缓存实现
   */
  class MemoryTileCache : public TileCache
  {
  public:
    MemoryTileCache(size_t maxSize = 100 * 1024 * 1024); // 默认100MB
    virtual ~MemoryTileCache();

    // 实现TileCache接口
    virtual bool getTile(const TileKey &key, TileData &tile) override;
    virtual void putTile(const TileData &tile) override;
    virtual bool hasTile(const TileKey &key) override;
    virtual void clear() override;
    virtual size_t getSize() override;
    virtual size_t getCount() override;

    // 设置最大缓存大小
    void setMaxSize(size_t maxSize);
    size_t getMaxSize() const { return _maxSize; }

  private:
    struct CacheEntry
    {
      TileData data;
      double timestamp;
      size_t size;
    };

    std::map<TileKey, CacheEntry> _cache;
    size_t _maxSize;
    size_t _currentSize;

    // LRU清理
    void cleanupLRU();

    // 计算瓦片数据大小
    size_t calculateTileSize(const TileData &tile);
  };

  /**
   * 瓦片下载器类
   */
  class TileDownloader
  {
  public:
    TileDownloader();
    virtual ~TileDownloader();

    // 设置瓦片服务配置
    void setServiceConfig(const TileServiceConfig &config);
    const TileServiceConfig &getServiceConfig() const { return _config; }

    // 设置瓦片缓存
    void setTileCache(std::shared_ptr<TileCache> cache);
    std::shared_ptr<TileCache> getTileCache() const { return _cache; }

    // 设置回调
    void setLoadCallback(std::shared_ptr<TileLoadCallback> callback);
    std::shared_ptr<TileLoadCallback> getLoadCallback() const { return _callback; }

    // 下载瓦片
    void downloadTile(const TileKey &key);

    // 批量下载瓦片
    void downloadTiles(const std::vector<TileKey> &keys);

    // 取消下载
    void cancelDownload(const TileKey &key);
    void cancelAllDownloads();

    // 获取下载状态
    bool isDownloading(const TileKey &key) const;
    size_t getDownloadingCount() const;

    // 设置并发下载数量
    void setMaxConcurrentDownloads(int maxDownloads);
    int getMaxConcurrentDownloads() const { return _maxConcurrentDownloads; }

    // 设置超时时间
    void setTimeout(double timeoutSeconds);
    double getTimeout() const { return _timeout; }

    // 获取统计信息
    size_t getDownloadedCount() const { return _downloadedCount; }
    size_t getFailedCount() const { return _failedCount; }
    double getTotalDownloadTime() const { return _totalDownloadTime; }

    // 添加代理配置
    void setProxy(const std::string &host, int port);
    void setProxyAuth(const std::string &username, const std::string &password);
    void clearProxy();

  private:
    TileServiceConfig _config;
    std::shared_ptr<TileCache> _cache;
    std::shared_ptr<TileLoadCallback> _callback;

    // 下载管理
    std::map<TileKey, void *> _activeDownloads; // 平台相关的下载句柄
    int _maxConcurrentDownloads;
    double _timeout;

    // 代理配置
    std::string _proxyHost;
    int _proxyPort;
    std::string _proxyUsername;
    std::string _proxyPassword;
    bool _useProxy;

    // 统计信息
    size_t _downloadedCount;
    size_t _failedCount;
    double _totalDownloadTime;

    // 下载实现
    void downloadTileImpl(const TileKey &key);

    // 处理下载完成
    void handleDownloadComplete(const TileKey &key, const std::vector<unsigned char> &data);

    // 处理下载失败
    void handleDownloadError(const TileKey &key, const std::string &error);

#ifdef EMSCRIPTEN
    // WebAssembly/Emscripten下载实现
    void downloadTileEmscripten(const TileKey &key);
    static void onEmscriptenDownloadSuccess(emscripten_fetch_t *fetch);
    static void onEmscriptenDownloadError(emscripten_fetch_t *fetch);
#endif
  };

  /**
   * 瓦片系统管理器
   */
  class TileSystemManager
  {
  public:
    TileSystemManager();
    virtual ~TileSystemManager();

    // 添加瓦片服务
    void addTileService(const std::string &name, const TileServiceConfig &config);
    void removeTileService(const std::string &name);

    // 获取瓦片服务
    const TileServiceConfig *getTileService(const std::string &name) const;
    std::vector<std::string> getTileServiceNames() const;

    // 设置默认瓦片服务
    void setDefaultTileService(const std::string &name);
    const std::string &getDefaultTileService() const { return _defaultService; }

    // 设置全局瓦片缓存
    void setGlobalTileCache(std::shared_ptr<TileCache> cache);
    std::shared_ptr<TileCache> getGlobalTileCache() const { return _globalCache; }

    // 创建瓦片下载器
    std::shared_ptr<TileDownloader> createTileDownloader(const std::string &serviceName);

    // 获取瓦片（优先从缓存）
    bool getTile(const TileKey &key, TileData &tile, const std::string &serviceName = "");

    // 异步获取瓦片
    void getTileAsync(const TileKey &key, std::shared_ptr<TileLoadCallback> callback, const std::string &serviceName = "");

    // 预加载瓦片
    void preloadTiles(const std::vector<TileKey> &keys, const std::string &serviceName = "");

    // 清理资源
    void cleanup();

    // 获取统计信息
    struct Statistics
    {
      size_t cacheSize;
      size_t cacheCount;
      size_t downloadedCount;
      size_t failedCount;
      double totalDownloadTime;
    };
    Statistics getStatistics() const;

  private:
    std::map<std::string, TileServiceConfig> _tileServices;
    std::string _defaultService;
    std::shared_ptr<TileCache> _globalCache;
    std::map<std::string, std::shared_ptr<TileDownloader>> _downloaders;

    // 初始化默认服务
    void initializeDefaultServices();
  };
}