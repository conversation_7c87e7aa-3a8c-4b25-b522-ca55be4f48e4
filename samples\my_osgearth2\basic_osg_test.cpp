#include <osgViewer/Viewer>
#include <osg/Group>
#include <osg/Geode>
#include <osg/ShapeDrawable>
#include <osg/Material>
#include <osg/PositionAttitudeTransform>
#include <iostream>

#ifdef __EMSCRIPTEN__
#include <emscripten.h>
#include <emscripten/html5.h>
#endif

// 全局变量
osgViewer::Viewer* g_viewer = nullptr;
bool g_initialized = false;

/**
 * 创建一个简单的场景 - 只有一个彩色立方体
 */
osg::Node* createSimpleScene()
{
    std::cout << "[DEBUG] Creating simple scene..." << std::endl;
    
    // 创建根节点
    osg::Group* root = new osg::Group();
    
    // 创建一个立方体
    osg::Geode* geode = new osg::Geode();
    osg::ShapeDrawable* cube = new osg::ShapeDrawable(new osg::Box(osg::Vec3(0, 0, 0), 1.0f));
    
    // 设置颜色
    cube->setColor(osg::Vec4(1.0f, 0.0f, 0.0f, 1.0f)); // 红色
    geode->addDrawable(cube);
    
    // 创建变换节点
    osg::PositionAttitudeTransform* transform = new osg::PositionAttitudeTransform();
    transform->setPosition(osg::Vec3(0, 0, 0));
    transform->addChild(geode);
    
    root->addChild(transform);
    
    std::cout << "[DEBUG] Simple scene created successfully" << std::endl;
    return root;
}

/**
 * 初始化查看器
 */
bool initializeViewer()
{
    std::cout << "[DEBUG] Initializing basic OSG viewer..." << std::endl;
    
    try
    {
        // 创建查看器
        g_viewer = new osgViewer::Viewer();
        
        // 设置单线程模式
        g_viewer->setThreadingModel(osgViewer::Viewer::SingleThreaded);
        
#ifdef __EMSCRIPTEN__
        std::cout << "[DEBUG] WebAssembly mode - setting up embedded window" << std::endl;
        g_viewer->setUpViewerAsEmbeddedInWindow(0, 0, 800, 600);
        
        // 设置相机
        osg::Camera* camera = g_viewer->getCamera();
        camera->setClearColor(osg::Vec4(0.2f, 0.2f, 0.4f, 1.0f)); // 深蓝色背景
        camera->setClearMask(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
        
        // 设置投影
        double fovy = 45.0;
        double aspectRatio = 800.0 / 600.0;
        double zNear = 1.0;
        double zFar = 1000.0;
        camera->setProjectionMatrixAsPerspective(fovy, aspectRatio, zNear, zFar);
        
        // 设置视图矩阵
        camera->setViewMatrixAsLookAt(
            osg::Vec3d(0, -5, 0),  // 眼睛位置
            osg::Vec3d(0, 0, 0),   // 看向的点
            osg::Vec3d(0, 0, 1)    // 上方向
        );
#else
        std::cout << "[DEBUG] Desktop mode" << std::endl;
        g_viewer->setUpViewInWindow(100, 100, 800, 600);
#endif
        
        // 创建场景
        osg::Node* scene = createSimpleScene();
        g_viewer->setSceneData(scene);
        
        // 实现查看器
        std::cout << "[DEBUG] Realizing viewer..." << std::endl;
        g_viewer->realize();
        
        std::cout << "[DEBUG] Viewer initialized successfully" << std::endl;
        g_initialized = true;
        return true;
    }
    catch (const std::exception& e)
    {
        std::cerr << "[ERROR] Exception in initializeViewer: " << e.what() << std::endl;
        return false;
    }
    catch (...)
    {
        std::cerr << "[ERROR] Unknown exception in initializeViewer" << std::endl;
        return false;
    }
}

/**
 * 主循环函数
 */
void mainLoop()
{
    static bool firstFrame = true;
    
    if (firstFrame)
    {
        std::cout << "[DEBUG] First frame - initializing..." << std::endl;
        if (!initializeViewer())
        {
            std::cerr << "[ERROR] Failed to initialize viewer" << std::endl;
            return;
        }
        firstFrame = false;
    }
    
    if (g_viewer && g_initialized)
    {
        // 渲染一帧
        g_viewer->frame();
    }
}

/**
 * 主函数
 */
int main()
{
    std::cout << "========================================" << std::endl;
    std::cout << "Basic OSG WebGL Test" << std::endl;
    std::cout << "========================================" << std::endl;
    
#ifdef __EMSCRIPTEN__
    std::cout << "[INFO] Starting Emscripten main loop..." << std::endl;
    emscripten_set_main_loop(mainLoop, 60, 1);
#else
    std::cout << "[INFO] Starting desktop main loop..." << std::endl;
    
    if (!initializeViewer())
    {
        std::cerr << "[ERROR] Failed to initialize viewer" << std::endl;
        return -1;
    }
    
    // 桌面版本的主循环
    while (!g_viewer->done())
    {
        g_viewer->frame();
    }
#endif
    
    return 0;
}
