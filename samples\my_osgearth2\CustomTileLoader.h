#pragma once

#include <osg/Texture2D>
#include <osg/Image>
#include <osgEarth/TileLayer>
#include <osgEarth/HTTPClient>
#include <string>
#include <memory>

namespace CustomTileSystem {

/**
 * Custom tile loader that directly downloads and applies tiles to geometry
 */
class CustomTileLoader {
public:
    CustomTileLoader();
    ~CustomTileLoader();
    
    /**
     * Download a tile from the given URL and return as OSG image
     */
    osg::ref_ptr<osg::Image> downloadTile(const std::string& url);
    
    /**
     * Create a texture from downloaded tile data
     */
    osg::ref_ptr<osg::Texture2D> createTileTexture(const std::string& url);
    
    /**
     * Apply tile texture to a geometry node
     */
    void applyTileToGeometry(osg::Node* node, const std::string& tileUrl);
    
    /**
     * Load multiple tiles for a given area
     */
    void loadTilesForArea(double minLon, double minLat, double maxLon, double maxLat, int zoomLevel);
    
private:
    osgEarth::HTTPClient _httpClient;
    std::string _cacheDir;
    
    // Helper methods
    std::string getTileCachePath(const std::string& url);
    bool saveTileToCache(const std::string& url, const std::string& data);
    std::string loadTileFromCache(const std::string& url);
};

} // namespace CustomTileSystem
