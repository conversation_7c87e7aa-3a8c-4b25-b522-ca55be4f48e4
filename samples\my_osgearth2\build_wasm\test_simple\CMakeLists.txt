cmake_minimum_required(VERSION 3.16)
project(osgearth_simple_test)

# 设置C++标准
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 检查是否为Emscripten编译
if(EMSCRIPTEN)
    message(STATUS "Configuring for WebAssembly with Ems<PERSON><PERSON>")
    
    # 设置编译器
    set(CMAKE_C_COMPILER "emcc")
    set(CMAKE_CXX_COMPILER "em++")
    
    # WebAssembly特定编译选项
    set(WASM_FLAGS 
        "-s USE_PTHREADS=1"                    # 启用多线程支持
        "-s PTHREAD_POOL_SIZE=4"               # 线程池大小
        "-s ALLOW_MEMORY_GROWTH=1"             # 允许内存增长
        "-s INITIAL_MEMORY=128MB"              # 初始内存大小
        "-s MAXIMUM_MEMORY=1GB"                # 最大内存限制
        "-s STACK_SIZE=8MB"                    # 栈大小
        "-s DISABLE_EXCEPTION_CATCHING=0"      # 启用异常处理
        "-s ASSERTIONS=1"                      # 启用断言
        "-s WASM=1"                            # 生成WASM
        "-s MODULARIZE=0"                      # 关闭模块化，使用传统方式
        "-s ENVIRONMENT='web,worker'"          # 运行环境
        "-s EXPORTED_FUNCTIONS=['_main']"      # 导出函数
        "-s EXPORTED_RUNTIME_METHODS=['ccall','cwrap']"  # 导出运行时方法
    )
    
    # 将标志转换为字符串
    string(REPLACE ";" " " WASM_FLAGS_STR "${WASM_FLAGS}")
    
    # 设置编译和链接标志
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${WASM_FLAGS_STR}")
    set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} ${WASM_FLAGS_STR}")
    
    # 设置输出文件扩展名
    set(CMAKE_EXECUTABLE_SUFFIX ".js")
    
else()
    message(STATUS "Configuring for desktop")
endif()

# 创建可执行文件
add_executable(simple_test osgearth_simple_test.cpp)

# 如果是WebAssembly编译，添加复制命令
if(EMSCRIPTEN)
    # 复制生成的文件到redist_wasm目录
    add_custom_command(TARGET simple_test POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E echo "Copying WebAssembly files to redist_wasm..."
        COMMAND ${CMAKE_COMMAND} -E copy
            "${CMAKE_CURRENT_SOURCE_DIR}/build_wasm/simple_test.js"
            "${CMAKE_CURRENT_SOURCE_DIR}/redist_wasm/"
        COMMAND ${CMAKE_COMMAND} -E copy
            "${CMAKE_CURRENT_SOURCE_DIR}/build_wasm/simple_test.wasm"
            "${CMAKE_CURRENT_SOURCE_DIR}/redist_wasm/"
        COMMENT "Copying WebAssembly files to redist_wasm directory"
    )
endif()
