# osgEarth WebAssembly - Simple Digital Earth Application
# 基于 osgEarth 2.10.1 WASM 静态库

cmake_minimum_required(VERSION 3.10)
project(osgearth_simple_earth)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

if(EMSCRIPTEN)
    include_directories(SYSTEM ${CMAKE_JS_INC_PATH})
endif()

# osgEarth 库路径
set(OSGEARTH_WASM_LIB_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../../../redist_wasm")
set(OSG_WASM_LIB_DIR "F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep")

# 包含头文件目录
include_directories(
    "${CMAKE_CURRENT_SOURCE_DIR}/../../"
    "${OSG_WASM_LIB_DIR}/include"
)

# 源文件
set(SOURCES
    main.cpp
)

# 创建可执行文件
add_executable(osgearth_simple_earth ${SOURCES})

if(EMSCRIPTEN)
    # osgEarth WASM 静态库
    set(OSGEARTH_LIBRARIES
        "${OSGEARTH_WASM_LIB_DIR}/libosgEarth.a"
        "${OSGEARTH_WASM_LIB_DIR}/osgdb_earth.a"
        "${OSGEARTH_WASM_LIB_DIR}/osgdb_osgearth_engine_rex.a"
        "${OSGEARTH_WASM_LIB_DIR}/osgdb_osgearth_cache_filesystem.a"
        "${OSGEARTH_WASM_LIB_DIR}/osgdb_osgearth_sky_simple.a"
    )
    
    # OSG WASM 静态库
    set(OSG_LIBRARIES
        "${OSG_WASM_LIB_DIR}/lib/libosg.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgViewer.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgDB.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgGA.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgUtil.a"
        "${OSG_WASM_LIB_DIR}/lib/libOpenThreads.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgText.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgShadow.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgSim.a"
    )
    
    # 第三方依赖库
    set(THIRD_PARTY_LIBRARIES
        "${OSG_WASM_LIB_DIR}/lib/libGeographicLib.a"
        "${OSG_WASM_LIB_DIR}/lib/libgeos.a"
        "${OSG_WASM_LIB_DIR}/lib/libgeos_c.a"
        "${OSG_WASM_LIB_DIR}/lib/libproj.a"
        "${OSG_WASM_LIB_DIR}/lib/libcurl.a"
        "${OSG_WASM_LIB_DIR}/lib/libz.a"
        "${OSG_WASM_LIB_DIR}/lib/libexpat.a"
        "${OSG_WASM_LIB_DIR}/lib/libpng16.a"
        "${OSG_WASM_LIB_DIR}/lib/libjpeg.a"
        "${OSG_WASM_LIB_DIR}/lib/libtiff.a"
        "${OSG_WASM_LIB_DIR}/lib/libfreetype.a"
        "${OSG_WASM_LIB_DIR}/lib/libfontconfig.a"
        "${OSG_WASM_LIB_DIR}/lib/libbz2.a"
        "${OSG_WASM_LIB_DIR}/lib/liblzma.a"
    )

    # 链接所有库
    target_link_libraries(osgearth_simple_earth
        ${OSGEARTH_LIBRARIES}
        ${OSG_LIBRARIES}
        ${THIRD_PARTY_LIBRARIES}
    )
    
    # 编译器标志
    set(EM_COMPILE_FLAGS
        "-DOSG_GLSL_VERSION=300"
        "-DOSGEARTH_HAVE_GEOS=0"
        "-DOSGEARTH_HAVE_GDAL=0"
        "-DUSE_EXTERNAL_WASM_DEPENDS=ON"
        "-O3"
    )

    # 链接器标志 - 单线程版本
    set(EM_LINK_FLAGS_LIST
        "-s USE_SDL=2"
        "-s USE_WEBGL2=1"
        "-s FULL_ES3=1"
        "-s WASM=1"
        "-s INITIAL_MEMORY=268435456"  # 256MB
        "-s ALLOW_MEMORY_GROWTH=1"
        "-s MAXIMUM_MEMORY=1073741824"  # 1GB
        "-s FETCH=1"
        "-s ASYNCIFY=1"
        "-s ASSERTIONS=1"
        "-s SAFE_HEAP=0"
        "-s STACK_OVERFLOW_CHECK=1"
        "-s EXPORTED_FUNCTIONS=['_main']"
        "-s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','HEAPU8']"
        "-s NO_EXIT_RUNTIME=1"
        "-s DISABLE_EXCEPTION_CATCHING=0"
        "-s FORCE_FILESYSTEM=1"
        "-s PTHREAD_POOL_SIZE=0"
        "-s USE_PTHREADS=0"
        "-O3"
        "--preload-file ${CMAKE_CURRENT_SOURCE_DIR}/data@/data"
    )
    string(REPLACE ";" " " EM_LINK_FLAGS "${EM_LINK_FLAGS_LIST}")

    # 设置编译器标志
    target_compile_options(osgearth_simple_earth PRIVATE ${EM_COMPILE_FLAGS})
    
    # 设置链接器属性
    set_target_properties(osgearth_simple_earth PROPERTIES
        SUFFIX ".html"
        LINK_FLAGS "${EM_LINK_FLAGS}"
    )

    message(STATUS "osgEarth WebAssembly build configured")
    message(STATUS "Output will be: osgearth_simple_earth.html, osgearth_simple_earth.js, osgearth_simple_earth.wasm")
    
else()
    # 桌面版本配置
    find_package(OpenSceneGraph REQUIRED)
    find_package(osgEarth REQUIRED)
    
    target_link_libraries(osgearth_simple_earth
        ${OPENSCENEGRAPH_LIBRARIES}
        ${OSGEARTH_LIBRARIES}
    )
endif()

# 安装规则
install(TARGETS osgearth_simple_earth
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib)

if(EMSCRIPTEN)
    # 安装WebAssembly文件到 redist_wasm 目录
    install(FILES 
        "${CMAKE_CURRENT_BINARY_DIR}/osgearth_simple_earth.html"
        "${CMAKE_CURRENT_BINARY_DIR}/osgearth_simple_earth.js"
        "${CMAKE_CURRENT_BINARY_DIR}/osgearth_simple_earth.wasm"
        DESTINATION "${CMAKE_CURRENT_SOURCE_DIR}/redist_wasm"
        OPTIONAL)
endif()
