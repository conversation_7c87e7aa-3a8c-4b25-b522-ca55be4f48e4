/* osgEarth
* Copyright 2025 Pelican Mapping
* MIT License
*/
#ifndef OSGEARTH_TILEHANDLER_H
#define OSGEARTH_TILEHANDLER_H 1

#include <osgEarth/Common>
#include <osgEarth/TileKey>
#include <osgEarth/Map>

#include <osgEarth/TileLayer>


namespace osgEarth { namespace Util
{
    using namespace osgEarth;
    class TileVisitor;

    /**
    * TileHandler is an interface for operations on a TileKey
    */
    class OSGEARTH_EXPORT TileHandler : public osg::Referenced
    {
    public:        
        /**
         * Process a tile - also provides a reference to the calling TileVisitor
         */
        virtual bool handleTile(const TileKey& key, const TileVisitor& tv);

        /**
         * Callback that tells a TileVisitor if it should attempt to process this key.
         * If this function returns false no further processing is done on child keys.
         */
        virtual bool hasData( const TileKey& key ) const;

        /**
         * Returns the process to run when executing in a MultiProcessTileVisitor.
         * 
         * When running in a MultiProcessTileVisitor the actual data processing is done by an external program
         * that takes a --tiles argument.  This function lets you tie that process to the TileHandler
         */
        virtual std::string getProcessString() const;

        //! Estimates the total number of tiles that this handler
        //! expects to handle for the given input extents.
        virtual unsigned getEstimatedTileCount(
            const std::vector<GeoExtent>& extents,
            unsigned minLevel,
            unsigned maxLevel) const { return 0; }
    };    

} } // namespace osgEarth

#endif // OSGEARTH_TRAVERSAL_DATA_H
