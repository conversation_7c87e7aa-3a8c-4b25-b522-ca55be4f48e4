<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>osgEarth Chrome 调试版本</title>
    <style>
        body {
            margin: 0;
            padding: 10px;
            background-color: #1e1e1e;
            color: #ffffff;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
        }
        
        .debug-header {
            background-color: #2d2d30;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 10px;
            border-left: 4px solid #007acc;
        }
        
        .debug-container {
            display: flex;
            gap: 10px;
            height: calc(100vh - 120px);
        }
        
        .canvas-panel {
            flex: 2;
            background-color: #252526;
            border-radius: 5px;
            padding: 10px;
        }
        
        .debug-panel {
            flex: 1;
            background-color: #252526;
            border-radius: 5px;
            padding: 10px;
            overflow-y: auto;
        }
        
        #canvas {
            width: 100%;
            height: 100%;
            background-color: #000000;
            border: 1px solid #3c3c3c;
            border-radius: 3px;
        }
        
        .debug-section {
            margin-bottom: 15px;
            background-color: #2d2d30;
            border-radius: 3px;
            padding: 10px;
        }
        
        .debug-title {
            color: #4ec9b0;
            font-weight: bold;
            margin-bottom: 8px;
            border-bottom: 1px solid #3c3c3c;
            padding-bottom: 4px;
        }
        
        .debug-content {
            color: #d4d4d4;
            font-size: 11px;
            line-height: 1.4;
        }
        
        .status-ok { color: #4ec9b0; }
        .status-error { color: #f44747; }
        .status-warning { color: #ffcc02; }
        .status-info { color: #569cd6; }
        
        #debug-log {
            background-color: #1e1e1e;
            border: 1px solid #3c3c3c;
            border-radius: 3px;
            padding: 8px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Consolas', monospace;
            font-size: 10px;
            white-space: pre-wrap;
        }
        
        .control-button {
            background-color: #0e639c;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 3px;
            cursor: pointer;
            margin-right: 5px;
            font-size: 11px;
        }
        
        .control-button:hover {
            background-color: #1177bb;
        }
        
        .webgl-info {
            background-color: #1e1e1e;
            border: 1px solid #3c3c3c;
            border-radius: 3px;
            padding: 8px;
            margin-top: 8px;
        }
    </style>
</head>
<body>
    <div class="debug-header">
        <h2 style="margin: 0; color: #ffffff;">🔍 osgEarth Chrome 调试控制台</h2>
        <p style="margin: 5px 0 0 0; color: #cccccc;">实时监控 WebGL 状态、错误信息和性能数据</p>
    </div>
    
    <div class="debug-container">
        <div class="canvas-panel">
            <div class="debug-title">渲染画布</div>
            <canvas id="canvas" oncontextmenu="event.preventDefault()"></canvas>
        </div>
        
        <div class="debug-panel">
            <div class="debug-section">
                <div class="debug-title">🎮 调试控制</div>
                <div class="debug-content">
                    <button class="control-button" onclick="forceFrame()">强制渲染一帧</button>
                    <button class="control-button" onclick="clearLog()">清除日志</button>
                    <button class="control-button" onclick="exportDebugInfo()">导出调试信息</button>
                </div>
            </div>
            
            <div class="debug-section">
                <div class="debug-title">📊 实时状态</div>
                <div class="debug-content">
                    <div>模块状态: <span id="module-status" class="status-info">加载中...</span></div>
                    <div>查看器状态: <span id="viewer-status" class="status-info">等待中...</span></div>
                    <div>渲染帧数: <span id="frame-count" class="status-info">0</span></div>
                    <div>最后错误: <span id="last-error" class="status-ok">无</span></div>
                </div>
            </div>
            
            <div class="debug-section">
                <div class="debug-title">🖥️ WebGL 信息</div>
                <div class="debug-content">
                    <div id="webgl-info" class="webgl-info">检测中...</div>
                </div>
            </div>
            
            <div class="debug-section">
                <div class="debug-title">📝 调试日志</div>
                <div class="debug-content">
                    <div id="debug-log"></div>
                </div>
            </div>
            
            <div class="debug-section">
                <div class="debug-title">🔧 Chrome 调试提示</div>
                <div class="debug-content">
                    <div style="color: #ffcc02;">1. 打开 F12 开发者工具</div>
                    <div style="color: #ffcc02;">2. 查看 Console 面板的错误信息</div>
                    <div style="color: #ffcc02;">3. 在 Sources 面板设置断点</div>
                    <div style="color: #ffcc02;">4. 使用 Performance 面板分析性能</div>
                    <div style="color: #ffcc02;">5. 检查 Network 面板的资源加载</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let debugLog = document.getElementById('debug-log');
        let moduleStatus = document.getElementById('module-status');
        let viewerStatus = document.getElementById('viewer-status');
        let frameCount = document.getElementById('frame-count');
        let lastError = document.getElementById('last-error');
        let webglInfo = document.getElementById('webgl-info');
        let canvas = document.getElementById('canvas');
        
        // 日志函数
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'status-error' : 
                             type === 'warning' ? 'status-warning' : 
                             type === 'success' ? 'status-ok' : 'status-info';
            
            debugLog.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
            
            // 同时输出到 Chrome 控制台
            console.log(`[osgEarth Debug] ${message}`);
        }
        
        // 检测 WebGL 支持
        function detectWebGL() {
            try {
                const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
                if (!gl) {
                    webglInfo.innerHTML = '<span class="status-error">WebGL 不支持</span>';
                    return;
                }
                
                const info = {
                    version: gl.getParameter(gl.VERSION),
                    vendor: gl.getParameter(gl.VENDOR),
                    renderer: gl.getParameter(gl.RENDERER),
                    glslVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION),
                    maxTextureSize: gl.getParameter(gl.MAX_TEXTURE_SIZE),
                    maxViewportDims: gl.getParameter(gl.MAX_VIEWPORT_DIMS)
                };
                
                webglInfo.innerHTML = `
                    <div>版本: ${info.version}</div>
                    <div>供应商: ${info.vendor}</div>
                    <div>渲染器: ${info.renderer}</div>
                    <div>GLSL: ${info.glslVersion}</div>
                    <div>最大纹理: ${info.maxTextureSize}</div>
                    <div>最大视口: ${info.maxViewportDims}</div>
                `;
                
                addLog('WebGL 检测完成', 'success');
                
            } catch (e) {
                webglInfo.innerHTML = `<span class="status-error">WebGL 检测失败: ${e.message}</span>`;
                addLog(`WebGL 检测失败: ${e.message}`, 'error');
            }
        }
        
        // 状态监控
        function startStatusMonitoring() {
            setInterval(() => {
                if (window.Module) {
                    // 获取调试状态
                    if (typeof window.Module.getDebugStatus === 'function') {
                        try {
                            const status = window.Module.getDebugStatus();
                            viewerStatus.textContent = status;
                            viewerStatus.className = 'status-ok';
                        } catch (e) {
                            viewerStatus.textContent = '状态获取失败';
                            viewerStatus.className = 'status-error';
                        }
                    }
                    
                    // 获取帧数
                    if (typeof window.Module.getFrameCount === 'function') {
                        try {
                            const count = window.Module.getFrameCount();
                            frameCount.textContent = count;
                        } catch (e) {
                            frameCount.textContent = '获取失败';
                        }
                    }
                    
                    // 获取错误信息
                    if (typeof window.Module.getLastError === 'function') {
                        try {
                            const error = window.Module.getLastError();
                            if (error && error.length > 0) {
                                lastError.textContent = error;
                                lastError.className = 'status-error';
                            } else {
                                lastError.textContent = '无';
                                lastError.className = 'status-ok';
                            }
                        } catch (e) {
                            lastError.textContent = '获取失败';
                            lastError.className = 'status-error';
                        }
                    }
                }
            }, 1000);
        }
        
        // 控制函数
        function forceFrame() {
            if (window.Module && typeof window.Module.forceFrame === 'function') {
                window.Module.forceFrame();
                addLog('强制渲染一帧', 'info');
            } else {
                addLog('forceFrame 函数不可用', 'warning');
            }
        }
        
        function clearLog() {
            debugLog.innerHTML = '';
            addLog('日志已清除', 'info');
        }
        
        function exportDebugInfo() {
            const info = {
                timestamp: new Date().toISOString(),
                webgl: webglInfo.textContent,
                moduleStatus: moduleStatus.textContent,
                viewerStatus: viewerStatus.textContent,
                frameCount: frameCount.textContent,
                lastError: lastError.textContent,
                log: debugLog.textContent
            };
            
            const blob = new Blob([JSON.stringify(info, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'osgearth_debug_info.json';
            a.click();
            URL.revokeObjectURL(url);
            
            addLog('调试信息已导出', 'success');
        }
        
        // Module 配置
        var Module = {
            canvas: canvas,
            
            print: function(text) {
                addLog(`[OSG] ${text}`, 'info');
            },
            
            printErr: function(text) {
                addLog(`[ERROR] ${text}`, 'error');
            },
            
            setStatus: function(text) {
                moduleStatus.textContent = text;
                addLog(`[STATUS] ${text}`, 'info');
                
                if (text.includes('Exception') || text.includes('Error')) {
                    moduleStatus.className = 'status-error';
                } else if (text.includes('complete') || text.includes('Running')) {
                    moduleStatus.className = 'status-ok';
                } else {
                    moduleStatus.className = 'status-warning';
                }
            },
            
            onRuntimeInitialized: function() {
                addLog('WebAssembly 运行时初始化完成', 'success');
                moduleStatus.textContent = '运行时就绪';
                moduleStatus.className = 'status-ok';
                startStatusMonitoring();
            },
            
            postRun: [function() {
                addLog('模块 postRun 完成', 'success');
            }],
            
            totalDependencies: 0,
            
            monitorRunDependencies: function(left) {
                this.totalDependencies = Math.max(this.totalDependencies, left);
                Module.setStatus(left ? 
                    `Preparing... (${this.totalDependencies - left}/${this.totalDependencies})` : 
                    'All downloads complete.');
            }
        };
        
        // 初始化
        detectWebGL();
        addLog('Chrome 调试页面初始化完成', 'success');
        addLog('等待 WebAssembly 模块加载...', 'info');
        
        // 错误处理
        window.addEventListener('error', function(e) {
            addLog(`JavaScript 错误: ${e.message}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', function(e) {
            addLog(`Promise 拒绝: ${e.reason}`, 'error');
        });
        
        // Canvas 事件
        canvas.addEventListener('webglcontextlost', function(e) {
            addLog('WebGL 上下文丢失', 'error');
            e.preventDefault();
        }, false);
        
        canvas.addEventListener('webglcontextrestored', function(e) {
            addLog('WebGL 上下文恢复', 'success');
        }, false);
        
        // 设置初始状态
        Module.setStatus('Downloading...');
    </script>
    
    <!-- 这里需要替换为实际的 JS 文件名 -->
    <script async src="osgearth_myviewer_debug.js"></script>
</body>
</html>
