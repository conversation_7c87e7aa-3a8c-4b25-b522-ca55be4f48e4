/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */

#include <osgEarth/TileIndexBuilder>
#include <osgEarth/FileUtils>
#include <osgEarth/Progress>
// #include <osgEarth/GDAL>
#include <osgEarth/TMS>
#include <osgEarth/ImageUtils>
#include <osgDB/ReadFile>
#include <osgDB/FileUtils>

using namespace osgDB;
using namespace osgEarth;
using namespace osgEarth::Contrib;

TileIndexBuilder::TileIndexBuilder()
{
}

void TileIndexBuilder::setProgressCallback(ProgressCallback *progress)
{
    _progress = progress;
}

void TileIndexBuilder::build(const std::string &indexFilename, const SpatialReference *srs)
{
    expandFilenames();

    if (!srs)
    {
        srs = osgEarth::SpatialReference::create("wgs84");
    }

    osg::ref_ptr<TileIndex> index = TileIndex::create(indexFilename, srs);

    _indexFilename = indexFilename;
    std::string indexDir = getFilePath(_indexFilename);

    unsigned int total = _expandedFilenames.size();

    for (unsigned int i = 0; i < _expandedFilenames.size(); i++)
    {
        std::string filename = _expandedFilenames[i];

        // 使用自定义的图像范围检测替代GDALImageLayer
        bool ok = false;

        // 尝试读取图像文件获取基本信息
        osg::ref_ptr<osg::Image> image = osgDB::readImageFile(filename);
        if (image.valid())
        {
            // 创建一个基本的数据范围
            // 这是一个简化的实现，实际应该从图像的地理信息中获取范围
            // 对于没有地理信息的图像，我们创建一个默认范围

            // 检查文件扩展名来判断可能的格式
            std::string ext = osgDB::getLowerCaseFileExtension(filename);

            DataExtent extent;
            if (ext == "tif" || ext == "tiff" || ext == "jpg" || ext == "jpeg" || ext == "png")
            {
                // 对于常见的图像格式，创建一个基于文件名或默认的地理范围
                // 这里使用一个简化的方法：假设图像覆盖一个小的地理区域

                // 尝试从文件名中解析坐标信息（如果有的话）
                // 这是一个非常简化的实现
                double minX = -180.0, minY = -90.0, maxX = 180.0, maxY = 90.0;

                // 如果无法从文件名解析，使用图像尺寸创建一个相对范围
                int width = image->s();
                int height = image->t();

                // 创建一个基于图像像素比例的小范围
                double aspectRatio = (double)width / (double)height;
                double size = 1.0; // 1度的基础大小

                if (aspectRatio > 1.0)
                {
                    maxX = minX + size * aspectRatio;
                    maxY = minY + size;
                }
                else
                {
                    maxX = minX + size;
                    maxY = minY + size / aspectRatio;
                }

                extent = DataExtent(GeoExtent(srs, minX, minY, maxX, maxY));

                // We want the filename as it is relative to the index file
                std::string relative = getPathRelative(indexDir, filename);
                index->add(relative, extent);
                ok = true;
            }
        }

        if (_progress.valid())
        {
            std::stringstream buf;
            if (ok)
            {
                buf << "Processed ";
            }
            else
            {
                buf << "Skipped ";
            }

            buf << filename;
            _progress->reportProgress((double)i + 1, (double)total, buf.str());
        }
    }

    osg::Timer_t end = osg::Timer::instance()->tick();
}

void TileIndexBuilder::expandFilenames()
{
    // Expand the filenames since they might contain directories
    for (unsigned int i = 0; i < _filenames.size(); i++)
    {
        std::string filename = _filenames[i];
        if (osgDB::fileType(filename) == osgDB::DIRECTORY)
        {
            CollectFilesVisitor v;
            v.traverse(filename);
            for (unsigned int j = 0; j < v.filenames.size(); j++)
            {
                _expandedFilenames.push_back(v.filenames[j]);
            }
        }
        else
        {
            _expandedFilenames.push_back(filename);
        }
    }
}
