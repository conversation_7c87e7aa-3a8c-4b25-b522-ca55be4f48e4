#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的HTTP服务器，用于测试WebAssembly应用
支持CORS和正确的MIME类型
"""

import http.server
import socketserver
import os
import sys
from urllib.parse import urlparse

class WASMHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """自定义HTTP请求处理器，支持WebAssembly MIME类型"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
    
    def end_headers(self):
        """添加CORS头和其他必要的头"""
        self.send_header('Cross-Origin-Opener-Policy', 'same-origin')
        self.send_header('Cross-Origin-Embedder-Policy', 'require-corp')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def guess_type(self, path):
        """重写MIME类型猜测，添加WebAssembly支持"""
        # 添加WebAssembly特定的MIME类型
        if path.endswith('.wasm'):
            return 'application/wasm'
        elif path.endswith('.js'):
            return 'application/javascript'
        elif path.endswith('.html'):
            return 'text/html'
        elif path.endswith('.css'):
            return 'text/css'

        # 对于其他文件类型，使用父类方法
        return super().guess_type(path)
    
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{self.log_date_time_string()}] {format % args}")

def main():
    """主函数"""
    # 默认端口
    port = 8080
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print(f"错误: 无效的端口号 '{sys.argv[1]}'")
            sys.exit(1)
    
    # 检查是否在redist_wasm目录中
    if not os.path.exists('redist_wasm'):
        print("警告: 当前目录下没有找到 'redist_wasm' 目录")
        print("请确保在项目根目录运行此脚本，或者先编译WebAssembly项目")
    
    # 切换到redist_wasm目录（如果存在）
    if os.path.exists('redist_wasm'):
        os.chdir('redist_wasm')
        print(f"切换到目录: {os.getcwd()}")
    
    # 检查必要的文件
    required_files = ['index.html']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"警告: 缺少以下文件: {', '.join(missing_files)}")
        print("请确保已经编译了WebAssembly项目并复制了HTML文件")
    
    # 列出当前目录的文件
    print("\n当前目录文件:")
    for file in os.listdir('.'):
        if os.path.isfile(file):
            size = os.path.getsize(file)
            print(f"  {file} ({size} bytes)")
    
    # 创建HTTP服务器
    try:
        with socketserver.TCPServer(("", port), WASMHTTPRequestHandler) as httpd:
            print(f"\n========================================")
            print(f"OSGEarth WebAssembly 测试服务器")
            print(f"========================================")
            print(f"服务器地址: http://localhost:{port}")
            print(f"服务目录: {os.getcwd()}")
            print(f"========================================")
            print(f"在浏览器中打开: http://localhost:{port}")
            print(f"按 Ctrl+C 停止服务器")
            print(f"========================================\n")
            
            # 启动服务器
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"错误: 端口 {port} 已被占用")
            print(f"请尝试使用其他端口: python {sys.argv[0]} <端口号>")
        else:
            print(f"错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
