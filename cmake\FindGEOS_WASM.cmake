# FindGEOS.cmake - WebAssembly版本
# 查找GEOS库，支持外部WASM依赖

# WebAssembly外部依赖支持
IF(USE_EXTERNAL_WASM_DEPENDS)
    message(STATUS "Using external WASM dependencies for GEOS")
    
    # 设置GEOS根目录
    if(NOT GEOS_DIR)
        set(GEOS_DIR "$ENV{GEOS_DIR}")
        if(NOT GEOS_DIR)
            if(DEFINED OSG_DIR)
                set(GEOS_DIR "${OSG_DIR}")
            else()
                message(FATAL_ERROR "GEOS_DIR not set for external WASM dependencies")
            endif()
        endif()
    endif()
    
    # 设置包含目录
    SET(GEOS_INCLUDE_DIR "${GEOS_DIR}/include")
    SET(GEOS_INCLUDE_DIRS "${GEOS_INCLUDE_DIR}")
    
    # 设置库文件路径 - 强制使用静态库
    SET(GEOS_LIBRARY "${GEOS_DIR}/lib/libgeos.a")
    SET(GEOS_C_LIBRARY "${GEOS_DIR}/lib/libgeos_c.a")
    SET(GEOS_LIBRARIES "${GEOS_LIBRARY};${GEOS_C_LIBRARY}")
    
    # 设置找到标志
    SET(GEOS_FOUND TRUE)
    
    # 设置版本信息
    SET(GEOS_VERSION "3.9.0")  # 假设版本，实际应该从头文件读取
    
    message(STATUS "GEOS WASM library configured:")
    message(STATUS "  GEOS_DIR: ${GEOS_DIR}")
    message(STATUS "  GEOS_INCLUDE_DIR: ${GEOS_INCLUDE_DIR}")
    message(STATUS "  GEOS_LIBRARY: ${GEOS_LIBRARY}")
    message(STATUS "  GEOS_C_LIBRARY: ${GEOS_C_LIBRARY}")
    
ELSE()
    # 使用标准查找方式
    find_package(GEOS REQUIRED)
    
    # 设置变量以保持兼容性
    if(TARGET GEOS::geos)
        get_target_property(GEOS_INCLUDE_DIR GEOS::geos INTERFACE_INCLUDE_DIRECTORIES)
        set(GEOS_LIBRARY GEOS::geos)
    endif()
    
    # 设置找到标志
    SET(GEOS_FOUND TRUE)
    
ENDIF()

# 处理标准参数
include(FindPackageHandleStandardArgs)
FIND_PACKAGE_HANDLE_STANDARD_ARGS(GEOS 
    REQUIRED_VARS GEOS_LIBRARY GEOS_INCLUDE_DIR
    VERSION_VAR GEOS_VERSION
)

# 标记为高级变量
MARK_AS_ADVANCED(
    GEOS_INCLUDE_DIR
    GEOS_LIBRARY
    GEOS_C_LIBRARY
)
