#include <iostream>
#include <emscripten.h>
#include <emscripten/html5.h>

// 简化的测试程序，验证基本的WebAssembly功能
class SimpleTest
{
public:
    SimpleTest() 
    {
        std::cout << "[SimpleTest] Constructor called" << std::endl;
        initialized = true;
    }
    
    ~SimpleTest()
    {
        std::cout << "[SimpleTest] Destructor called" << std::endl;
    }
    
    void run()
    {
        std::cout << "[SimpleTest] Run method called" << std::endl;
        if (initialized) {
            std::cout << "[SimpleTest] Test is initialized and running successfully!" << std::endl;
        } else {
            std::cout << "[SimpleTest] Test is not initialized!" << std::endl;
        }
    }
    
    void frame()
    {
        static int frameCount = 0;
        frameCount++;
        if (frameCount % 60 == 0) {  // 每60帧打印一次
            std::cout << "[SimpleTest] Frame " << frameCount << " - Still running..." << std::endl;
        }
    }
    
private:
    bool initialized = false;
};

// 全局测试对象
SimpleTest* g_test = nullptr;

// 主循环函数
void mainLoop()
{
    if (g_test) {
        g_test->frame();
    }
}

// 主函数
int main(int argc, char** argv)
{
    try {
        std::cout << "[SimpleTest] Starting simple WebAssembly test..." << std::endl;
        
        // 创建测试对象
        g_test = new SimpleTest();
        
        // 运行测试
        g_test->run();
        
        std::cout << "[SimpleTest] Setting up main loop..." << std::endl;
        
#ifdef __EMSCRIPTEN__
        // 设置主循环
        emscripten_set_main_loop(mainLoop, 60, 1);  // 60 FPS
#else
        // 桌面版本的主循环
        for (int i = 0; i < 1000; ++i) {
            mainLoop();
        }
#endif
        
        std::cout << "[SimpleTest] Test completed successfully" << std::endl;
        return 0;
    }
    catch (const std::exception& e) {
        std::cerr << "[SimpleTest] Exception: " << e.what() << std::endl;
        return 1;
    }
    catch (...) {
        std::cerr << "[SimpleTest] Unknown exception" << std::endl;
        return 1;
    }
}
