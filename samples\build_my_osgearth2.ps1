# 构建 my_osgearth2 项目 - 基于成功经验的稳定版本
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "构建 my_osgearth2 数字地球项目" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# 设置路径
$OSG_WASM_LIB_DIR = "F:\cmo-dev\my_osgearth_web\osgearth_third_party\wasm_dep"
$OSGEARTH_WASM_LIB_DIR = "F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\redist_wasm"

# 通用编译选项
$COMMON_FLAGS = @(
    "-DOSG_GLSL_VERSION=300"
    "-DOSGEARTH_HAVE_GEOS=1"
    "-DOSGEARTH_HAVE_GDAL=0"
    "-DOSGEARTH_HAVE_PROJ=1"
    "-DUSE_EXTERNAL_WASM_DEPENDS=ON"
    "-O3"
    "-I$OSG_WASM_LIB_DIR\include"
    "-I$OSGEARTH_WASM_LIB_DIR\..\src"
)

# 链接库（完整的 osgEarth 支持 + 缺失的库）
$LIBS = @(
    "$OSGEARTH_WASM_LIB_DIR\libosgEarth.a"
    "$OSGEARTH_WASM_LIB_DIR\osgdb_earth.a"
    "$OSGEARTH_WASM_LIB_DIR\osgdb_osgearth_engine_rex.a"
    "$OSG_WASM_LIB_DIR\lib\libosg.a"
    "$OSG_WASM_LIB_DIR\lib\libosgViewer.a"
    "$OSG_WASM_LIB_DIR\lib\libosgDB.a"
    "$OSG_WASM_LIB_DIR\lib\libosgGA.a"
    "$OSG_WASM_LIB_DIR\lib\libosgUtil.a"
    "$OSG_WASM_LIB_DIR\lib\libosgShadow.a"
    "$OSG_WASM_LIB_DIR\lib\libosgText.a"
    "$OSG_WASM_LIB_DIR\lib\libosgSim.a"
    "$OSG_WASM_LIB_DIR\lib\libOpenThreads.a"
    "$OSG_WASM_LIB_DIR\lib\libgeos.a"
    "$OSG_WASM_LIB_DIR\lib\libproj.a"
    "$OSG_WASM_LIB_DIR\lib\libcurl.a"
    "$OSG_WASM_LIB_DIR\lib\libGeographicLib.a"
    "$OSG_WASM_LIB_DIR\lib\libz.a"
    "$OSG_WASM_LIB_DIR\lib\libexpat.a"
    "$OSG_WASM_LIB_DIR\lib\libpng16.a"
    "$OSG_WASM_LIB_DIR\lib\libjpeg.a"
)

# 多线程链接选项（支持 SharedArrayBuffer 和线程）
$LINK_FLAGS = @(
    "-s USE_SDL=2"
    "-s USE_WEBGL2=1"
    "-s FULL_ES3=1"
    "-s WASM=1"
    "-s INITIAL_MEMORY=1073741824"     # 1GB 初始内存
    "-s ALLOW_MEMORY_GROWTH=1"
    "-s MAXIMUM_MEMORY=2147483648"     # 2GB 最大内存
    "-s FETCH=1"
    "-s ASYNCIFY=1"
    "-s USE_PTHREADS=1"                # 启用多线程支持
    "-s PTHREAD_POOL_SIZE=4"           # 线程池大小
    "-s EXPORTED_FUNCTIONS=[_main]"
    "-s EXPORTED_RUNTIME_METHODS=[ccall,cwrap,HEAPU8]"
    "-s NO_EXIT_RUNTIME=1"
    "-s DISABLE_EXCEPTION_CATCHING=0"
    "-s FORCE_FILESYSTEM=1"
    "-s ENVIRONMENT=web,worker"        # 支持 Web Worker
    "-s GL_ENABLE_GET_PROC_ADDRESS=1"
    "-s GL_UNSAFE_OPTS=0"              # 安全的 WebGL 选项
    "-s LEGACY_GL_EMULATION=0"         # 禁用传统 GL 模拟
    "-s OFFSCREEN_FRAMEBUFFER=1"       # 启用离屏帧缓冲（多线程需要）
    "-s OFFSCREENCANVAS_SUPPORT=1"     # 启用 OffscreenCanvas 支持
    "-O3"
)

Write-Host "`n🔨 构建 my_osgearth2 项目" -ForegroundColor Green
$sources = @(
    "my_osgearth2\main.cpp"
)

$cmd = "em++ " + ($COMMON_FLAGS -join " ") + " " + ($sources -join " ") + " " + ($LIBS -join " ") + " " + ($LINK_FLAGS -join " ") + " -o redist_all_samples\my_osgearth2.html"
Write-Host "执行命令: $cmd" -ForegroundColor Gray

try {
    Invoke-Expression $cmd
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ my_osgearth2 构建成功" -ForegroundColor Green
        
        # 检查生成的文件
        $files = @("my_osgearth2.html", "my_osgearth2.js", "my_osgearth2.wasm")
        foreach ($file in $files) {
            $path = "redist_all_samples\$file"
            if (Test-Path $path) {
                $size = (Get-Item $path).Length
                $sizeStr = if ($size -gt 1MB) { "{0:F1} MB" -f ($size / 1MB) } else { "{0:F0} KB" -f ($size / 1KB) }
                Write-Host "  📄 $file ($sizeStr)" -ForegroundColor Gray
            }
        }
        
        Write-Host "`n🎉 构建完成！" -ForegroundColor Green
        Write-Host "🌐 访问: http://localhost:8080/my_osgearth2.html" -ForegroundColor Cyan
        
    }
    else {
        Write-Host "❌ my_osgearth2 构建失败，返回码: $LASTEXITCODE" -ForegroundColor Red
        Write-Host "🔧 开始迭代修复..." -ForegroundColor Yellow
        return $false
    }
}
catch {
    Write-Host "❌ my_osgearth2 构建异常: $_" -ForegroundColor Red
    Write-Host "🔧 开始迭代修复..." -ForegroundColor Yellow
    return $false
}

Write-Host "`n========================================" -ForegroundColor Cyan
Write-Host "🎉 my_osgearth2 项目构建完成！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan

Write-Host "`n📋 项目特点：" -ForegroundColor White
Write-Host "  ✅ 完整的 osgEarth 支持" -ForegroundColor Gray
Write-Host "  ✅ SDL2 窗口和交互" -ForegroundColor Gray
Write-Host "  ✅ 单线程稳定配置" -ForegroundColor Gray
Write-Host "  ✅ 备用简单地球" -ForegroundColor Gray
Write-Host "  ✅ 512MB-2GB 内存配置" -ForegroundColor Gray
Write-Host "  ✅ WebGL 2.0 渲染" -ForegroundColor Gray

Write-Host "`n🎯 功能：" -ForegroundColor White
Write-Host "  • osgEarth 三维数字地球" -ForegroundColor Gray
Write-Host "  • 鼠标拖拽旋转" -ForegroundColor Gray
Write-Host "  • 滚轮缩放" -ForegroundColor Gray
Write-Host "  • ESC 键退出" -ForegroundColor Gray
Write-Host "  • 深蓝色太空背景" -ForegroundColor Gray
Write-Host "  • 异常处理和备用方案" -ForegroundColor Gray

return $true
