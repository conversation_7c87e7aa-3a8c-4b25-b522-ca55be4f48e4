#include <iostream>
#include <emscripten.h>

#ifdef __EMSCRIPTEN__
#include <emscripten/html5.h>
#endif

// 简单的测试函数
void mainLoop() {
    static int counter = 0;
    counter++;
    
    if (counter % 60 == 0) {
        std::cout << "Frame " << counter << " - Simple test running" << std::endl;
    }
}

int main() {
    std::cout << "=== Simple WebAssembly Test ===" << std::endl;
    std::cout << "Testing basic Emscripten compilation..." << std::endl;
    
#ifdef __EMSCRIPTEN__
    std::cout << "Emscripten environment detected" << std::endl;
    emscripten_set_main_loop(mainLoop, 60, 1);
#else
    std::cout << "Desktop environment" << std::endl;
    for (int i = 0; i < 100; ++i) {
        mainLoop();
    }
#endif
    
    return 0;
}
