/* osgEarth
* Copyright 2025 Pelican Mapping
* MIT License
*/

#ifndef OSGEARTH_TileEstimator_H
#define OSGEARTH_TileEstimator_H 1

#include <osgEarth/Common>
#include <osgEarth/Profile>

namespace osgEarth { namespace Util
{
    using namespace osgEarth;

    /**
     * Utility for estimating the size of a tiling operation.
     * This provides a ROUGH estimate that you can use for progress reporting
     * or disk use projections.
     */
    class OSGEARTH_EXPORT TileEstimator
    {
    public:
        TileEstimator();

        /**
        * Sets the minimum level to seed to
        */
        void setMinLevel(const unsigned int& minLevel) {_minLevel = minLevel;}

        /**
        * Gets the minimum level to seed to.
        */
        const unsigned int getMinLevel() const {return _minLevel;}

        /**
        * Sets the maximum level to seed to
        */
        void setMaxLevel(const unsigned int& maxLevel) {_maxLevel = maxLevel;}

        /**
        * Gets the maximum level to cache to.
        */
        const unsigned int getMaxLevel() const {return _maxLevel;}

        /**
        *Adds an extent to cache
        */
        void addExtent( const GeoExtent& value );
       

        /**
         * Gets or sets the Profile used for this Cache.  Defaults to a global-geodetic profile
         */
        const osgEarth::Profile* getProfile() const { return _profile.get(); }
        void setProfile( const osgEarth::Profile* profile ) { _profile = profile; }

        /**
         * Gets or sets the approximate size in MB for each tile
         */
        double getSizeInMBPerTile() const { return _sizeInMBPerTile;}
        void setSizeInMBPerTile( double sizeInMBPerTile ) { _sizeInMBPerTile = sizeInMBPerTile; }

        /**
         * Gets or sets the approximate amount of processing time in seconds it will take for each tile
         */
        double getTimeInSecondsPerTile() const { return _timeInSecondsPerTile;}
        void setTimeInSecondsPerTile( double timeInSecondsPerTile ) { _timeInSecondsPerTile = timeInSecondsPerTile; }

        /**
         * Gets the estimated total number of tiles that will be cached
         */
        unsigned int getNumTiles() const;

        /**
         * Get the estimated size of the output cache in MB.  
         * This is a ROUGH estimate based on the _sizeInMBPerTile setting.
         */
        double getSizeInMB() const;

        /**
         * Get an estimate on the amount of time it will take to process the cache.
         * This is a ROUGH estimate based on the _timeInSecondsPerTile setting
         */
        double getTotalTimeInSeconds() const;


    protected:

        osg::ref_ptr< const osgEarth::Profile > _profile;
        unsigned int _minLevel;
        unsigned int _maxLevel;        
        std::vector< GeoExtent > _extents;
        double _sizeInMBPerTile;
        double _timeInSecondsPerTile;

    };
} }

#endif // OSGEARTH_BOUNDS_H
