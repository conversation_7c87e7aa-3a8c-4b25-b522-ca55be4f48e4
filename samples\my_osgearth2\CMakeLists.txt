# WebAssembly CMakeLists.txt for osgearth_myviewer
cmake_minimum_required(VERSION 3.16)

# 设置项目名称
project(osgearth_myviewer_wasm)

# 设置C++标准
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 检查是否使用Emscripten编译器
if(NOT EMSCRIPTEN)
    message(FATAL_ERROR "This CMakeLists.txt is designed for Emscripten only!")
endif()

# 基本编译器标志
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++20")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -std=c11")

# 多线程支持编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -pthread")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -pthread")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -matomics")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -matomics")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -mbulk-memory")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -mbulk-memory")

# WebAssembly特定编译选项
set(WASM_FLAGS
    "-s USE_PTHREADS=1"                    # 启用多线程支持
    "-s PTHREAD_POOL_SIZE=8"               # 线程池大小（增加到8）
    "-s ALLOW_MEMORY_GROWTH=1"             # 允许内存增长
    "-s INITIAL_MEMORY=1073741824"         # 初始内存大小（1GB）
    "-s MAXIMUM_MEMORY=4294967296"         # 最大内存限制（4GB）
    "-s STACK_SIZE=16777216"               # 栈大小（16MB）
    "-s DISABLE_EXCEPTION_CATCHING=0"      # 启用异常处理
    "-s USE_WEBGL2=1"                      # 使用WebGL2
    "-s FULL_ES3=1"                        # 完整ES3支持
    "-s FETCH=1"                           # 启用Fetch API
    "-s ASYNCIFY=1"                        # 启用异步支持
    "-s WASM=1"                            # 生成WASM
    "-s SHARED_MEMORY=1"                   # 启用共享内存
    "-s OFFSCREENCANVAS_SUPPORT=1"         # 启用离屏渲染支持
    "-s PROXY_TO_PTHREAD=0"                # 禁用主线程代理
    "-s NO_EXIT_RUNTIME=1"                 # 不退出运行时
    "-s FORCE_FILESYSTEM=1"                # 强制文件系统支持
    "-s STACK_OVERFLOW_CHECK=1"            # 启用栈溢出检查
    "-s SAFE_HEAP=0"                       # 禁用堆安全检查（性能优化）
    "-s EXPORTED_FUNCTIONS=['_main','_getOSGEarthStatus','_getMapInfo','_isRunning','_forceUpdate']"      # 导出函数
    "-s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','HEAPU8']"  # 导出运行时方法
)

# 将WASM_FLAGS应用到编译和链接
string(REPLACE ";" " " WASM_FLAGS_STR "${WASM_FLAGS}")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${WASM_FLAGS_STR}")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} ${WASM_FLAGS_STR}")
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} ${WASM_FLAGS_STR}")

# 定义WebAssembly特定的宏
add_definitions(-DOSGEARTH_WASM_BUILD)
add_definitions(-D__EMSCRIPTEN__)
add_definitions(-DOSGEARTH_THREADING_ENABLED)
add_definitions(-DOSGEARTH_WASM_MULTITHREADING)
add_definitions(-DOSG_THREADING_ENABLED)
add_definitions(-DOPENTHREADS_ATOMIC_USE_MUTEX)

# 设置依赖库路径
set(OSGEARTH_WASM_LIB_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../../redist_wasm")
set(THIRD_PARTY_WASM_DIR "E:/project/my-earth202507/thirid_party/wasm_dep")

# 检查依赖库目录是否存在
if(NOT EXISTS ${OSGEARTH_WASM_LIB_DIR})
    message(FATAL_ERROR "OSGEarth WASM library directory not found: ${OSGEARTH_WASM_LIB_DIR}")
endif()

if(NOT EXISTS ${THIRD_PARTY_WASM_DIR})
    message(FATAL_ERROR "Third party WASM dependency directory not found: ${THIRD_PARTY_WASM_DIR}")
endif()

# 设置包含目录
include_directories(
    ${THIRD_PARTY_WASM_DIR}/include
    ${THIRD_PARTY_WASM_DIR}/include/osg
    ${CMAKE_CURRENT_SOURCE_DIR}/../../src
)

# 创建可执行文件
add_executable(osgearth_myviewer_wasm osgearth_myviewer_wasm.cpp)

# 链接OSGEarth库
target_link_libraries(osgearth_myviewer_wasm
    ${OSGEARTH_WASM_LIB_DIR}/libosgEarth.a
)

# 链接OSGEarth插件
target_link_libraries(osgearth_myviewer_wasm
    ${OSGEARTH_WASM_LIB_DIR}/osgdb_earth.a
    ${OSGEARTH_WASM_LIB_DIR}/osgdb_osgearth_engine_rex.a
    ${OSGEARTH_WASM_LIB_DIR}/osgdb_osgearth_sky_simple.a
    ${OSGEARTH_WASM_LIB_DIR}/osgdb_osgearth_sky_gl.a
    ${OSGEARTH_WASM_LIB_DIR}/osgdb_osgearth_cache_filesystem.a
    ${OSGEARTH_WASM_LIB_DIR}/osgdb_osgearth_terrainshader.a
    ${OSGEARTH_WASM_LIB_DIR}/osgdb_osgearth_viewpoints.a
)

# 链接第三方库 - 直接指定库文件路径
target_link_libraries(osgearth_myviewer_wasm
    ${THIRD_PARTY_WASM_DIR}/lib/libosg.a
    ${THIRD_PARTY_WASM_DIR}/lib/libosgViewer.a
    ${THIRD_PARTY_WASM_DIR}/lib/libosgGA.a
    ${THIRD_PARTY_WASM_DIR}/lib/libosgDB.a
    ${THIRD_PARTY_WASM_DIR}/lib/libosgUtil.a
    ${THIRD_PARTY_WASM_DIR}/lib/libOpenThreads.a
    ${THIRD_PARTY_WASM_DIR}/lib/libosgText.a
    ${THIRD_PARTY_WASM_DIR}/lib/libosgSim.a
    ${THIRD_PARTY_WASM_DIR}/lib/libosgShadow.a
    ${THIRD_PARTY_WASM_DIR}/lib/libGeographicLib.a
)

# 设置输出目录
set_target_properties(osgearth_myviewer_wasm PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/build_wasm"
)

# 添加自定义命令复制到发布目录
add_custom_command(TARGET osgearth_myviewer_wasm POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E make_directory "${CMAKE_CURRENT_SOURCE_DIR}/redist_wasm"
    COMMAND ${CMAKE_COMMAND} -E copy
        "${CMAKE_CURRENT_SOURCE_DIR}/build_wasm/osgearth_myviewer_wasm.js"
        "${CMAKE_CURRENT_SOURCE_DIR}/redist_wasm/"
    COMMAND ${CMAKE_COMMAND} -E copy
        "${CMAKE_CURRENT_SOURCE_DIR}/build_wasm/osgearth_myviewer_wasm.wasm"
        "${CMAKE_CURRENT_SOURCE_DIR}/redist_wasm/"

    COMMENT "Copying WebAssembly files to redist_wasm directory"
)
