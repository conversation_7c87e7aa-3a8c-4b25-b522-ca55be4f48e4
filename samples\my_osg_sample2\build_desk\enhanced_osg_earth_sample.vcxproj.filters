﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="C:\GeminiCLI\my_osg_sample\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\GeminiCLI\my_osg_sample\SpatialReference.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\GeminiCLI\my_osg_sample\TileSystem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="C:\GeminiCLI\my_osg_sample\SpatialReference.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\GeminiCLI\my_osg_sample\TileSystem.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\GeminiCLI\my_osg_sample\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{4AA17670-F0F0-396A-B1A5-313EB538F5C2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{DC1F28CB-315B-3CB2-A129-593770566C1E}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
