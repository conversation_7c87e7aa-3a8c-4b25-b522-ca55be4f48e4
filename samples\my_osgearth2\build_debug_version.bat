@echo off
echo ========================================
echo osgEarth Chrome 调试版本构建脚本
echo ========================================

REM 设置环境变量
set EMSCRIPTEN_ROOT=C:\dev\emsdk
set OSG_WASM_LIB=..\..\..\..\osgearth_third_party\wasm_dep
set OSGEARTH_WASM_LIB=..\..\redist_wasm

echo 🔧 检查环境...

REM 检查 Emscripten
if not exist "%EMSCRIPTEN_ROOT%\emsdk_env.bat" (
    echo ❌ Emscripten SDK 未找到
    pause
    exit /b 1
)

REM 检查依赖库
if not exist "%OSG_WASM_LIB%\lib" (
    echo ❌ OSG WASM 库目录不存在: %OSG_WASM_LIB%\lib
    pause
    exit /b 1
)

if not exist "%OSGEARTH_WASM_LIB%\lib" (
    echo ❌ osgEarth WASM 库目录不存在: %OSGEARTH_WASM_LIB%\lib
    pause
    exit /b 1
)

echo ✅ 环境检查通过

REM 设置 Emscripten 环境
echo 🔧 配置 Emscripten 环境...
call "%EMSCRIPTEN_ROOT%\emsdk_env.bat" >nul 2>&1

REM 验证 emcc
emcc --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Emscripten 编译器不可用
    pause
    exit /b 1
)

echo ✅ Emscripten 环境就绪

REM 创建输出目录
if not exist "redist_wasm" mkdir redist_wasm

echo 🔨 开始编译调试版本...

REM 编译参数
set COMMON_FLAGS=-DOSG_GLSL_VERSION=300 -O2 -g
set INCLUDE_DIRS=-I%OSG_WASM_LIB%\include -I%OSGEARTH_WASM_LIB%\include
set LIBRARIES=%OSG_WASM_LIB%\lib\libosg.a %OSG_WASM_LIB%\lib\libosgViewer.a %OSG_WASM_LIB%\lib\libosgDB.a %OSG_WASM_LIB%\lib\libosgGA.a %OSG_WASM_LIB%\lib\libosgUtil.a %OSG_WASM_LIB%\lib\libOpenThreads.a %OSGEARTH_WASM_LIB%\lib\libosgEarth.a

REM 调试版本的链接选项
set LINK_FLAGS=-s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s WASM=1 -s INITIAL_MEMORY=268435456 -s ALLOW_MEMORY_GROWTH=1 -s MAXIMUM_MEMORY=1073741824 -s EXPORTED_FUNCTIONS=[_main] -s EXPORTED_RUNTIME_METHODS=[ccall,cwrap,HEAPU8] -s NO_EXIT_RUNTIME=1 -s DISABLE_EXCEPTION_CATCHING=0 -s FORCE_FILESYSTEM=1 -s ENVIRONMENT=web -s GL_ENABLE_GET_PROC_ADDRESS=1 -s GL_UNSAFE_OPTS=0 -s LEGACY_GL_EMULATION=0 -s ASSERTIONS=1 -s GL_DEBUG=1 -s DEMANGLE_SUPPORT=1

REM 执行编译
em++ %COMMON_FLAGS% %INCLUDE_DIRS% osgearth_myviewer_debug.cpp %LIBRARIES% %LINK_FLAGS% -o redist_wasm\osgearth_myviewer_debug.html

if errorlevel 1 (
    echo ❌ 编译失败
    pause
    exit /b 1
)

echo ✅ 编译成功

REM 复制调试模板
echo 📄 创建调试页面...
copy chrome_debug_template.html redist_wasm\debug.html >nul

REM 修改调试页面中的 JS 文件引用
powershell -Command "(Get-Content redist_wasm\debug.html) -replace 'osgearth_myviewer_debug.js', 'osgearth_myviewer_debug.js' | Set-Content redist_wasm\debug.html"

echo 📊 检查生成的文件...
if exist "redist_wasm\osgearth_myviewer_debug.html" (
    for %%f in (redist_wasm\osgearth_myviewer_debug.*) do (
        echo   📄 %%f
    )
)

if exist "redist_wasm\debug.html" (
    echo   📄 redist_wasm\debug.html ^(Chrome 调试版本^)
)

echo.
echo 🎉 构建完成！
echo.
echo 📋 使用说明：
echo   1. 启动本地服务器 ^(如 python -m http.server 8080^)
echo   2. 在 Chrome 中打开: http://localhost:8080/debug.html
echo   3. 按 F12 打开开发者工具
echo   4. 查看 Console、Sources、Performance 等面板
echo.
echo 🔍 调试功能：
echo   • 实时状态监控
echo   • 详细错误日志
echo   • WebGL 信息检测
echo   • 强制渲染控制
echo   • 调试信息导出
echo.

pause
