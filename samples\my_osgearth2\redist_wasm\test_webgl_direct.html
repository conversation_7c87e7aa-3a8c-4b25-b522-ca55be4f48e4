<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>WebGL 直接测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #000;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            font-size: 24px;
            color: #4CAF50;
        }
        
        .container {
            display: flex;
            gap: 20px;
        }
        
        .canvas-container {
            flex: 1;
            background-color: #111;
            border: 2px solid #333;
            border-radius: 8px;
            overflow: hidden;
        }
        
        #canvas {
            width: 100%;
            height: 600px;
            display: block;
            background-color: #000;
        }
        
        .info {
            width: 300px;
            background-color: #222;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #333;
        }
        
        .status {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #333;
            border-radius: 5px;
        }
        
        .status-label {
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 5px;
        }
        
        .status-value {
            font-family: monospace;
            font-size: 12px;
            color: #ccc;
        }
        
        #output {
            width: 100%;
            height: 200px;
            background-color: #000;
            color: #0f0;
            font-family: monospace;
            font-size: 11px;
            border: 1px solid #333;
            padding: 5px;
            overflow-y: auto;
            resize: vertical;
        }
        
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
    </style>
</head>
<body>
    <div class="header">WebGL 直接测试 - 绕过 OSG 验证 WebGL 基础功能</div>
    
    <div class="container">
        <div class="canvas-container">
            <canvas id="canvas" oncontextmenu="event.preventDefault()"></canvas>
        </div>
        
        <div class="info">
            <div class="status">
                <div class="status-label">测试目标</div>
                <div class="status-value">
                    验证 WebGL 是否能正常工作<br>
                    绕过 OSG 图形上下文系统<br>
                    直接使用 Emscripten WebGL API
                </div>
            </div>
            
            <div class="status">
                <div class="status-label">加载状态</div>
                <div id="status" class="status-value">初始化中...</div>
            </div>
            
            <div class="status">
                <div class="status-label">WebGL 状态</div>
                <div id="webgl-status" class="status-value">等待中...</div>
            </div>
            
            <div class="status">
                <div class="status-label">预期效果</div>
                <div class="status-value">
                    • 深蓝色背景<br>
                    • 旋转的彩色三角形<br>
                    • 红绿蓝三色顶点<br>
                    • 60 FPS 流畅动画
                </div>
            </div>
            
            <div class="status">
                <div class="status-label">技术信息</div>
                <div id="tech-info" class="status-value">等待 WebGL 信息...</div>
            </div>
            
            <div class="status">
                <div class="status-label">控制台输出</div>
                <textarea id="output" readonly></textarea>
            </div>
        </div>
    </div>

    <script>
        // 获取元素
        var statusElement = document.getElementById("status");
        var webglStatusElement = document.getElementById("webgl-status");
        var techInfoElement = document.getElementById("tech-info");
        var canvasElement = document.getElementById("canvas");
        var outputElement = document.getElementById("output");
        
        // 输出函数
        function addOutput(text, type = 'info') {
            console.log(text);
            if (outputElement) {
                outputElement.value += text + '\n';
                outputElement.scrollTop = outputElement.scrollHeight;
            }
        }
        
        // 状态监控
        function startMonitoring() {
            setInterval(function() {
                if (window.Module) {
                    // 检查 WebGL 状态
                    if (typeof window.Module.getTestStatus === 'function') {
                        try {
                            var status = window.Module.getTestStatus();
                            webglStatusElement.textContent = status;
                            webglStatusElement.className = 'status-value success';
                        } catch (e) {
                            webglStatusElement.textContent = '状态获取失败';
                            webglStatusElement.className = 'status-value error';
                        }
                    }
                    
                    // 检查 WebGL 是否工作
                    if (typeof window.Module.isWebGLWorking === 'function') {
                        try {
                            var working = window.Module.isWebGLWorking();
                            if (working) {
                                webglStatusElement.textContent = 'WebGL 正常工作';
                                webglStatusElement.className = 'status-value success';
                            }
                        } catch (e) {
                            // 忽略错误
                        }
                    }
                }
            }, 1000);
        }
        
        // Module 配置
        var Module = {
            canvas: canvasElement,
            
            print: function(text) {
                addOutput('[WebGL] ' + text);
                
                // 提取技术信息
                if (text.includes('WebGL Vendor:') || text.includes('WebGL Renderer:') || 
                    text.includes('WebGL Version:') || text.includes('GLSL Version:')) {
                    var currentInfo = techInfoElement.textContent;
                    if (currentInfo === '等待 WebGL 信息...') {
                        techInfoElement.textContent = text.replace('[webgl_direct_test] ', '');
                    } else {
                        techInfoElement.textContent += '\n' + text.replace('[webgl_direct_test] ', '');
                    }
                }
            },
            
            printErr: function(text) {
                addOutput('[ERROR] ' + text, 'error');
            },
            
            setStatus: function(text) {
                addOutput('[STATUS] ' + text);
                statusElement.textContent = text;
                if (text.includes('Exception') || text.includes('Error')) {
                    statusElement.className = 'status-value error';
                } else if (text.includes('complete') || text.includes('Running')) {
                    statusElement.className = 'status-value success';
                }
            },
            
            onRuntimeInitialized: function() {
                addOutput('[INFO] WebGL 直接测试运行时初始化完成', 'success');
                statusElement.textContent = '运行时就绪';
                statusElement.className = 'status-value success';
                startMonitoring();
            },
            
            postRun: [function() {
                addOutput('[INFO] WebGL 直接测试模块 postRun 完成', 'success');
                statusElement.textContent = '测试运行中';
                statusElement.className = 'status-value success';
            }],
            
            totalDependencies: 0,
            
            monitorRunDependencies: function(left) {
                this.totalDependencies = Math.max(this.totalDependencies, left);
                Module.setStatus(left ? 
                    'Preparing... (' + (this.totalDependencies - left) + '/' + this.totalDependencies + ')' : 
                    'All downloads complete.');
            }
        };
        
        // 设置初始状态
        Module.setStatus('Downloading...');
        
        // 错误处理
        window.onerror = function(event) {
            addOutput('[JS ERROR] ' + event, 'error');
            Module.setStatus('Exception thrown, see console');
        };
        
        // Canvas 事件
        canvasElement.addEventListener("webglcontextlost", function(e) {
            addOutput('[WEBGL] Context lost - page reload required', 'error');
            alert('WebGL context lost. You will need to reload the page.');
            e.preventDefault();
        }, false);
        
        addOutput('[INFO] 页面初始化完成，开始 WebGL 直接测试...', 'success');
        addOutput('[INFO] 这个测试绕过 OSG，直接验证 WebGL 是否能正常工作');
        addOutput('[INFO] 如果看到旋转的彩色三角形，说明 WebGL 基础功能正常');
    </script>
    
    <!-- 加载 WebGL 直接测试模块 -->
    <script async src="webgl_direct_test.js"></script>
</body>
</html>
