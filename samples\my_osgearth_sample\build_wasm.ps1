#!/usr/bin/env pwsh
# OSGEarth WebAssembly 编译脚本
# 支持多线程和完整优化

Write-Host "OSGEarth WebAssembly 编译开始..." -ForegroundColor Green

# 设置 Emscripten 环境
$env:EMSDK = "C:/dev/emsdk"
$env:PATH = "$env:EMSDK;" + $env:PATH

# 激活 Emscripten
& "$env:EMSDK/emsdk_env.ps1"

# 创建构建目录
if (-not (Test-Path "build_wasm")) {
    New-Item -ItemType Directory -Path "build_wasm"
}

Set-Location "build_wasm"

# 配置 CMake
Write-Host "配置 CMake..." -ForegroundColor Yellow
emcmake cmake -DUSE_EXTERNAL_WASM_DEPENDS=ON -DCMAKE_BUILD_TYPE=Release -DOSGEARTH_BUILD_SHARED_LIBS=OFF ..

# 编译
Write-Host "开始编译..." -ForegroundColor Yellow
emmake make -j4

# 检查编译结果
if (Test-Path "osgearth_simple_earth.wasm") {
    Write-Host "编译成功!" -ForegroundColor Green
    
    # 显示文件大小
    $wasmSize = (Get-Item "osgearth_simple_earth.wasm").Length
    $jsSize = (Get-Item "osgearth_simple_earth.js").Length
    $htmlSize = (Get-Item "osgearth_simple_earth.html").Length
    
    Write-Host "文件大小:" -ForegroundColor Cyan
    Write-Host "  WASM: $([math]::Round($wasmSize/1MB, 2)) MB" -ForegroundColor White
    Write-Host "  JS:   $([math]::Round($jsSize/1KB, 2)) KB" -ForegroundColor White
    Write-Host "  HTML: $([math]::Round($htmlSize/1KB, 2)) KB" -ForegroundColor White
    
    # 复制到发布目录
    if (-not (Test-Path "../redist_wasm")) {
        New-Item -ItemType Directory -Path "../redist_wasm"
    }
    
    Copy-Item "osgearth_simple_earth.*" -Destination "../redist_wasm/" -Force
    Write-Host "文件已复制到 redist_wasm 目录" -ForegroundColor Green
    
    Write-Host "可以通过 HTTP 服务器访问: http://localhost:8000/osgearth_simple_earth.html" -ForegroundColor Cyan
} else {
    Write-Host "编译失败!" -ForegroundColor Red
}

Set-Location ..
