/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */

#ifndef OSGEARTH_METADATANODE_H
#define OSGEARTH_METADATANODE_H 1

#include <osgEarth/Common>
#include <osgEarth/Feature>
#include <osgEarth/ObjectIndex>
#include <osg/Group>
#include <osg/Array>
#include <osg/BufferIndexBinding>

namespace osgEarth
{
    class OSGEARTH_EXPORT MetadataNode : public osg::Group
    {
    public:
        MetadataNode();

        MetadataNode(const MetadataNode& rhs, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY);

        META_Node(osgEarth, MetadataNode);

        virtual ~MetadataNode();

        virtual ObjectID add(Feature* feature, bool visible = true);

        virtual void tagDrawable(osg::Drawable* drawable, ObjectID id) const;

        virtual void tagNode(osg::Node* node, ObjectID id) const;

        virtual void finalize();

        virtual unsigned int getNumFeatures() const;

        virtual bool getVisible(unsigned int index) const;

        virtual void setVisible(unsigned int index, bool value);

        virtual ObjectID getObjectID(unsigned index) const;

        virtual const Feature* getFeature(unsigned int index) const;

        virtual Feature* getFeature(unsigned int index);

        virtual int getIndexFromObjectID(ObjectID id) const;

    protected:
        virtual void init();

        std::vector< osg::ref_ptr< Feature > > _features;
        osg::ref_ptr< osg::Vec2uiArray > _instances;
    };

} // namespace osgEarth

#endif // OSGEARTH_METADATANODE_H
