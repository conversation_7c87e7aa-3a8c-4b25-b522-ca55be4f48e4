/*
 * osgEarth MyViewer Debug 版本
 * 专门用于 Chrome 调试，包含详细的错误检查和状态报告
 */

#include <osgViewer/Viewer>
#include <osgEarth/MapNode>
#include <osgEarth/Map>
#include <osgEarth/ImageLayer>
#include <osgEarth/XYZ>
#include <osgEarth/Profile>
#include <osgEarth/URI>
#include <osgEarth/GLUtils>
#include <osgEarth/Registry>
#include <osgGA/StateSetManipulator>
#include <osgGA/GUIEventHandler>
#include <osgViewer/ViewerEventHandlers>
#include <osg/Group>
#include <iostream>
#include <string>

#ifdef __EMSCRIPTEN__
#include <emscripten.h>
#include <emscripten/html5.h>
#include <osg/GraphicsContext>
#endif

#define DEBUG_LOG(msg) std::cout << "[DEBUG] " << msg << std::endl
#define ERROR_LOG(msg) std::cerr << "[ERROR] " << msg << std::endl
#define INFO_LOG(msg) std::cout << "[INFO] " << msg << std::endl

#ifdef __EMSCRIPTEN__
// Emscripten GraphicsContext 实现
class EmscriptenGraphicsContext : public osg::GraphicsContext
{
public:
    EmscriptenGraphicsContext(Traits *traits) : osg::GraphicsContext()
    {
        _traits = traits;
        _valid = true;
        _realized = false;

        // 重要：初始化OSG内部状态，避免内存访问错误
        setState(new osg::State);
        getState()->setGraphicsContext(this);

        DEBUG_LOG("EmscriptenGraphicsContext created with proper state initialization");
    }

    virtual ~EmscriptenGraphicsContext()
    {
        DEBUG_LOG("EmscriptenGraphicsContext destroyed");
    }

    virtual bool valid() const { return _valid; }

    virtual bool realizeImplementation()
    {
        _realized = true;
        DEBUG_LOG("EmscriptenGraphicsContext realized");
        return true;
    }

    virtual bool isRealizedImplementation() const { return _realized; }

    virtual void closeImplementation() { _realized = false; }

    virtual bool makeCurrentImplementation() { return true; }

    virtual bool releaseContextImplementation() { return true; }

    virtual void swapBuffersImplementation() {}

    virtual void bindPBufferToTextureImplementation(GLenum buffer) {}

private:
    bool _valid;
    bool _realized;
};

// Emscripten 窗口系统接口实现
class EmscriptenWindowingSystemInterface : public osg::GraphicsContext::WindowingSystemInterface
{
public:
    EmscriptenWindowingSystemInterface()
    {
        DEBUG_LOG("EmscriptenWindowingSystemInterface created");
    }

    virtual ~EmscriptenWindowingSystemInterface()
    {
        DEBUG_LOG("EmscriptenWindowingSystemInterface destroyed");
    }

    virtual unsigned int getNumScreens(const osg::GraphicsContext::ScreenIdentifier &screenIdentifier = osg::GraphicsContext::ScreenIdentifier()) override
    {
        return 1;
    }

    virtual void getScreenSettings(const osg::GraphicsContext::ScreenIdentifier &screenIdentifier, osg::GraphicsContext::ScreenSettings &resolution) override
    {
        resolution.width = 800;
        resolution.height = 600;
        resolution.colorDepth = 32;
        resolution.refreshRate = 60.0;
    }

    virtual void enumerateScreenSettings(const osg::GraphicsContext::ScreenIdentifier &screenIdentifier, osg::GraphicsContext::ScreenSettingsList &resolutionList) override
    {
        osg::GraphicsContext::ScreenSettings resolution;
        getScreenSettings(screenIdentifier, resolution);
        resolutionList.push_back(resolution);
    }

    virtual osg::GraphicsContext *createGraphicsContext(osg::GraphicsContext::Traits *traits) override
    {
        DEBUG_LOG("Creating EmscriptenGraphicsContext");
        return new EmscriptenGraphicsContext(traits);
    }
};
#endif

// 全局变量
static osgViewer::Viewer *g_viewer = nullptr;
static osgEarth::MapNode *g_mapNode = nullptr;
static int g_frameCount = 0;
static bool g_initialized = false;
static std::string g_lastError = "";

/**
 * 检查 OpenGL 错误
 */
void checkGLError(const std::string &operation)
{
#ifdef __EMSCRIPTEN__
    // WebAssembly 环境下跳过 OpenGL 错误检查，避免 WebGL 上下文问题
    DEBUG_LOG("WebAssembly mode: Skipping OpenGL error check for " + operation);
#else
    // 桌面版本进行正常的 OpenGL 错误检查
    // GLenum error = glGetError();
    // if (error != GL_NO_ERROR)
    // {
    //     std::string errorMsg = "OpenGL error in " + operation + ": " + std::to_string(error);
    //     ERROR_LOG(errorMsg);
    //     g_lastError = errorMsg;
    // }
    DEBUG_LOG("Desktop mode: OpenGL error check for " + operation);
#endif
}

/**
 * 创建调试用的简单地图
 */
osgEarth::Map *createDebugMap()
{
    DEBUG_LOG("Creating debug map...");

    try
    {
        // 创建地图，使用默认的地理坐标系
        osgEarth::Map *map = new osgEarth::Map();

        // 设置地图的坐标系为地理坐标系（WGS84）
        map->setProfile(osgEarth::Profile::create(osgEarth::Profile::GLOBAL_GEODETIC));

#ifdef __EMSCRIPTEN__
        // WebAssembly 环境下创建最简单的地图，确保地形引擎能够创建
        DEBUG_LOG("WebAssembly mode: Creating minimal map for terrain engine");

        // 不添加任何图层，只创建基础地形
        DEBUG_LOG("Creating empty map with global geodetic profile");
#else
        // 桌面版本添加简单的 XYZ 图层
        DEBUG_LOG("Desktop mode: Adding XYZ layer");

        osgEarth::XYZImageLayer::Options xyzOptions;
        xyzOptions.url() = osgEarth::URI("https://tile.openstreetmap.org/{z}/{x}/{y}.png");

        osgEarth::XYZImageLayer *layer = new osgEarth::XYZImageLayer(xyzOptions);

        // 设置球面墨卡托投影
        layer->setProfile(osgEarth::Profile::create(osgEarth::Profile::SPHERICAL_MERCATOR));

        map->addLayer(layer);
        DEBUG_LOG("XYZ layer added successfully");
#endif

        DEBUG_LOG("Debug map created successfully");
        return map;
    }
    catch (const std::exception &e)
    {
        ERROR_LOG("Exception in createDebugMap: " + std::string(e.what()));
        g_lastError = "Map creation failed: " + std::string(e.what());
        return nullptr;
    }
    catch (...)
    {
        ERROR_LOG("Unknown exception in createDebugMap");
        g_lastError = "Map creation failed: unknown exception";
        return nullptr;
    }
}

/**
 * 初始化查看器
 */
bool initializeViewer()
{
    DEBUG_LOG("Initializing debug viewer...");

    try
    {
        // 创建查看器
        g_viewer = new osgViewer::Viewer();

        // 设置线程模型 - WebAssembly 环境下使用单线程避免 WebGL 上下文问题
        g_viewer->setThreadingModel(osgViewer::Viewer::SingleThreaded);
        g_viewer->setRunFrameScheme(osgViewer::Viewer::CONTINUOUS);

        // 应用全局默认设置
        osgEarth::GLUtils::setGlobalDefaults(g_viewer->getCamera()->getOrCreateStateSet());

        DEBUG_LOG("Viewer created, setting up graphics context...");

#ifdef __EMSCRIPTEN__
        // WebAssembly 特定设置 - 参考 osgearth_myviewer_wasm.cpp
        DEBUG_LOG("WebAssembly mode detected");

        // 使用嵌入式窗口设置，让OSG内部处理WebGL上下文
        g_viewer->setUpViewerAsEmbeddedInWindow(0, 0, 800, 600);

        // 禁用不支持的OpenGL功能
        osg::ref_ptr<osg::StateSet> globalStateSet = g_viewer->getCamera()->getOrCreateStateSet();

        // 禁用WebGL不支持的功能
        globalStateSet->setMode(GL_ALPHA_TEST, osg::StateAttribute::OFF);

        // 强制使用VBO
        osg::DisplaySettings::instance()->setVertexBufferHint(osg::DisplaySettings::VERTEX_BUFFER_OBJECT);

        // WebGL纹理渲染优化设置
        globalStateSet->setMode(GL_CULL_FACE, osg::StateAttribute::ON);
        globalStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);

        // 设置相机参数
        osg::Camera *camera = g_viewer->getCamera();
        camera->setClearColor(osg::Vec4(0.1f, 0.1f, 0.3f, 1.0f)); // 深蓝色背景，便于调试
        camera->setClearMask(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

        // 设置投影
        double fovy = 45.0;
        double aspectRatio = 800.0 / 600.0;
        double zNear = 1.0;
        double zFar = 1e12;
        camera->setProjectionMatrixAsPerspective(fovy, aspectRatio, zNear, zFar);
        camera->setComputeNearFarMode(osg::CullSettings::COMPUTE_NEAR_FAR_USING_BOUNDING_VOLUMES);

        DEBUG_LOG("WebGL compatibility settings applied successfully");

#else
        // 桌面版本设置
        DEBUG_LOG("Desktop mode detected");

        osg::ref_ptr<osg::GraphicsContext::Traits> traits = new osg::GraphicsContext::Traits();
        traits->x = 0;
        traits->y = 0;
        traits->width = 800;
        traits->height = 600;
        traits->windowDecoration = false;
        traits->doubleBuffer = true;

        osg::ref_ptr<osg::GraphicsContext> gc = osg::GraphicsContext::createGraphicsContext(traits.get());
        if (!gc.valid())
        {
            ERROR_LOG("Failed to create graphics context");
            return false;
        }

        g_viewer->getCamera()->setGraphicsContext(gc.get());
        g_viewer->getCamera()->setViewport(new osg::Viewport(0, 0, 800, 600));

        DEBUG_LOG("Desktop graphics context created");
#endif

        // 创建地图
        DEBUG_LOG("Creating map...");
        osgEarth::Map *map = createDebugMap();
        if (!map)
        {
            ERROR_LOG("Failed to create map");
            return false;
        }

        // 创建地图节点
        DEBUG_LOG("Creating map node...");
        g_mapNode = new osgEarth::MapNode(map);
        if (!g_mapNode)
        {
            ERROR_LOG("Failed to create map node");
            return false;
        }

        // 设置场景
        DEBUG_LOG("Setting scene data...");
        g_viewer->setSceneData(g_mapNode);

        // 添加事件处理器
        DEBUG_LOG("Adding event handlers...");
        g_viewer->addEventHandler(new osgViewer::StatsHandler());
        g_viewer->addEventHandler(new osgViewer::WindowSizeHandler());
        g_viewer->addEventHandler(new osgGA::StateSetManipulator(g_viewer->getCamera()->getOrCreateStateSet()));

        // 实现查看器
        DEBUG_LOG("Realizing viewer...");

#ifdef __EMSCRIPTEN__
        // WebAssembly 环境下跳过 realize()，让 OSG 在第一次 frame() 时自动处理
        DEBUG_LOG("WebAssembly mode: Skipping realize() - will auto-realize on first frame");

        // 确保查看器不会立即退出
        g_viewer->setDone(false);

        // 设置 WebGL 特定的状态
        osg::ref_ptr<osg::StateSet> ss = g_viewer->getCamera()->getOrCreateStateSet();
        ss->setMode(GL_BLEND, osg::StateAttribute::ON);
        ss->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);

        DEBUG_LOG("WebGL context preparation completed");
#else
        g_viewer->realize();
#endif

        checkGLError("viewer initialization");

        INFO_LOG("Viewer initialized successfully");
        return true;
    }
    catch (const std::exception &e)
    {
        ERROR_LOG("Exception in initializeViewer: " + std::string(e.what()));
        g_lastError = "Viewer initialization failed: " + std::string(e.what());
        return false;
    }
    catch (...)
    {
        ERROR_LOG("Unknown exception in initializeViewer");
        g_lastError = "Viewer initialization failed: unknown exception";
        return false;
    }
}

/**
 * 主循环函数
 */
void mainLoop()
{
    g_frameCount++;

    if (!g_initialized)
    {
        DEBUG_LOG("First frame - initializing...");
        if (initializeViewer())
        {
            g_initialized = true;
            INFO_LOG("Initialization completed successfully");
        }
        else
        {
            ERROR_LOG("Initialization failed");
            return;
        }
    }

    if (g_viewer && !g_viewer->done())
    {
        try
        {
#ifdef __EMSCRIPTEN__
            // WebAssembly 环境下安全地渲染一帧
            g_viewer->frame();

            // 每60帧报告一次状态
            if (g_frameCount % 60 == 0)
            {
                DEBUG_LOG("Frame " + std::to_string(g_frameCount) + " rendered successfully");
            }
#else
            // 桌面版本正常渲染
            g_viewer->frame();
            checkGLError("frame rendering");

            // 每60帧报告一次状态
            if (g_frameCount % 60 == 0)
            {
                DEBUG_LOG("Frame " + std::to_string(g_frameCount) + " rendered successfully");
            }
#endif
        }
        catch (const std::exception &e)
        {
            ERROR_LOG("Exception in main loop: " + std::string(e.what()));
            g_lastError = "Rendering error: " + std::string(e.what());
        }
        catch (...)
        {
            ERROR_LOG("Unknown exception in main loop");
            g_lastError = "Rendering error: unknown exception";
        }
    }
}

/**
 * 清理资源
 */
void cleanup()
{
    DEBUG_LOG("Cleaning up resources...");

    if (g_viewer)
    {
        delete g_viewer;
        g_viewer = nullptr;
    }

    g_mapNode = nullptr;

    DEBUG_LOG("Cleanup completed");
}

/**
 * 主函数
 */
int main(int argc, char **argv)
{
    INFO_LOG("========================================");
    INFO_LOG("osgEarth MyViewer Debug Version");
    INFO_LOG("========================================");

    // 初始化 osgEarth
    DEBUG_LOG("Initializing osgEarth...");

#ifdef __EMSCRIPTEN__
    // 注册 Emscripten 窗口系统接口
    DEBUG_LOG("Registering EmscriptenWindowingSystemInterface...");
    osg::GraphicsContext::setWindowingSystemInterface(new EmscriptenWindowingSystemInterface());
    DEBUG_LOG("EmscriptenWindowingSystemInterface registered successfully");
#endif

    osgEarth::initialize();
    DEBUG_LOG("osgEarth initialized successfully");

    try
    {

#ifdef __EMSCRIPTEN__
        INFO_LOG("Starting Emscripten main loop...");
        emscripten_set_main_loop(mainLoop, 60, 1); // 60 FPS
        atexit(cleanup);
#else
        INFO_LOG("Starting desktop main loop...");
        if (initializeViewer())
        {
            g_initialized = true;
            int result = g_viewer->run();
            cleanup();
            return result;
        }
        else
        {
            ERROR_LOG("Failed to initialize viewer");
            cleanup();
            return 1;
        }
#endif

        return 0;
    }
    catch (const std::exception &e)
    {
        ERROR_LOG("Exception in main: " + std::string(e.what()));
        cleanup();
        return 1;
    }
    catch (...)
    {
        ERROR_LOG("Unknown exception in main");
        cleanup();
        return 1;
    }
}

#ifdef __EMSCRIPTEN__
// 导出调试函数供 JavaScript 调用
extern "C"
{

    EMSCRIPTEN_KEEPALIVE
    const char *getDebugStatus()
    {
        static std::string status;
        if (g_initialized)
        {
            status = "Initialized - Frame " + std::to_string(g_frameCount);
        }
        else
        {
            status = "Not initialized";
        }
        return status.c_str();
    }

    EMSCRIPTEN_KEEPALIVE
    const char *getLastError()
    {
        return g_lastError.c_str();
    }

    EMSCRIPTEN_KEEPALIVE
    bool isViewerRunning()
    {
        return g_viewer && !g_viewer->done();
    }

    EMSCRIPTEN_KEEPALIVE
    int getFrameCount()
    {
        return g_frameCount;
    }

    EMSCRIPTEN_KEEPALIVE
    void forceFrame()
    {
        if (g_viewer && !g_viewer->done())
        {
            g_viewer->frame();
        }
    }

    EMSCRIPTEN_KEEPALIVE
    const char *getViewerInfo()
    {
        static std::string info;
        if (g_viewer)
        {
            info = "Viewer active, MapNode: " + std::string(g_mapNode ? "OK" : "NULL");
        }
        else
        {
            info = "Viewer not created";
        }
        return info.c_str();
    }
}
#endif
