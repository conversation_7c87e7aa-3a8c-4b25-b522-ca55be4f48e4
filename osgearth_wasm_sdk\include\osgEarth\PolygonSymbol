/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/Symbol>
#include <osgEarth/Fill>
#include <osgEarth/URI>

namespace osgEarth
{
    /**
     * Symbol that describes how to render a polygonal geometry.
     */
    class OSGEARTH_EXPORT PolygonSymbol : public Symbol
    {
    public:
        META_Object(osgEarth, PolygonSymbol);

        PolygonSymbol(const PolygonSymbol& rhs, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY);
        PolygonSymbol(const Config& conf = Config());

        /** dtor */
        virtual ~PolygonSymbol() { }

        //! Polygon fill properties.
        OE_OPTION(Fill, fill);

        //! Whether to use a LineStyle (if one exists in the Style) as an outline.
        //! This defaults to true, but you set this to false to suppress outlining
        //! even when a LineStyle exists.
        OE_OPTION(bool, outline, true);

        //! URI of material to use to texture polygon geometries
        OE_OPTION(URI, material);

    public:
        virtual Config getConfig() const;
        virtual void mergeConfig(const Config& conf);
        static void parseSLD(const Config& c, class Style& style);
    };
} // namespace osgEarth
