/**
 * WebAssembly HTTP客户端 - 集成STB_Image解码器
 * 专门用于WebAssembly环境的HTTP图像下载和解码
 */

#ifndef WEBASSEMBLY_HTTP_CLIENT_H
#define WEBASSEMBLY_HTTP_CLIENT_H

#include <osg/Image>
#include <osgEarth/URI>
#include <osgEarth/HTTPClient>
#include <string>
#include <vector>
#include <functional>

#ifdef EMSCRIPTEN
#include <emscripten/fetch.h>
#include "STBImageDecoder.h"
#endif

namespace osgEarth
{

/**
 * WebAssembly HTTP客户端类
 */
class WebAssemblyHTTPClient
{
public:
    /**
     * 异步下载图像的回调函数类型
     */
    using ImageCallback = std::function<void(osg::ref_ptr<osg::Image>, const std::string& error)>;

    /**
     * 异步下载并解码图像
     */
    static void downloadImageAsync(const std::string& url, ImageCallback callback)
    {
#ifdef EMSCRIPTEN
        // 创建回调数据结构
        struct CallbackData
        {
            ImageCallback callback;
            std::string url;
        };

        CallbackData* data = new CallbackData{callback, url};

        // 配置fetch选项
        emscripten_fetch_attr_t attr;
        emscripten_fetch_attr_init(&attr);
        strcpy(attr.requestMethod, "GET");
        attr.attributes = EMSCRIPTEN_FETCH_LOAD_TO_MEMORY;
        attr.userData = data;
        
        // 设置成功回调
        attr.onsuccess = [](emscripten_fetch_t* fetch) {
            CallbackData* data = static_cast<CallbackData*>(fetch->userData);
            
            if (fetch->numBytes > 0 && fetch->data)
            {
                // 使用STB_Image解码图像
                osg::ref_ptr<osg::Image> image = decodeImageData(
                    reinterpret_cast<const unsigned char*>(fetch->data),
                    fetch->numBytes
                );
                
                if (image.valid())
                {
                    image->setFileName(data->url);
                    data->callback(image, "");
                }
                else
                {
                    data->callback(nullptr, "Failed to decode image data");
                }
            }
            else
            {
                data->callback(nullptr, "Empty response data");
            }
            
            delete data;
            emscripten_fetch_close(fetch);
        };
        
        // 设置错误回调
        attr.onerror = [](emscripten_fetch_t* fetch) {
            CallbackData* data = static_cast<CallbackData*>(fetch->userData);
            
            std::string error = "HTTP error " + std::to_string(fetch->status) + 
                               " when downloading: " + data->url;
            data->callback(nullptr, error);
            
            delete data;
            emscripten_fetch_close(fetch);
        };

        // 发起异步请求
        emscripten_fetch(&attr, url.c_str());
#else
        // 非WebAssembly环境，使用标准HTTP客户端
        callback(nullptr, "WebAssemblyHTTPClient only works in WebAssembly environment");
#endif
    }

    /**
     * 同步下载图像（在WebAssembly中实际上是异步的）
     */
    static osg::ref_ptr<osg::Image> downloadImageSync(const std::string& url)
    {
#ifdef EMSCRIPTEN
        // 在WebAssembly中，同步下载不推荐，但可以通过Promise模拟
        // 这里返回空，建议使用异步版本
        return nullptr;
#else
        // 非WebAssembly环境，可以使用标准HTTP客户端
        return nullptr;
#endif
    }

private:
    /**
     * 使用STB_Image解码图像数据
     */
    static osg::ref_ptr<osg::Image> decodeImageData(const unsigned char* data, size_t size)
    {
#ifdef EMSCRIPTEN
        STBImageDecoder decoder;
        
        // 创建内存流
        std::string dataStr(reinterpret_cast<const char*>(data), size);
        std::istringstream stream(dataStr, std::ios::binary);
        
        // 使用STB解码器解码图像
        osgDB::ReaderWriter::ReadResult result = decoder.readImage(stream);
        
        if (result.validImage())
        {
            return osg::ref_ptr<osg::Image>(result.takeImage());
        }
        else
        {
            std::cout << "[WebAssemblyHTTPClient] STB解码失败: " << result.message() << std::endl;
            return nullptr;
        }
#else
        return nullptr;
#endif
    }
};

/**
 * WebAssembly图像加载器 - 替代标准URI图像读取
 */
class WebAssemblyImageLoader
{
public:
    /**
     * 异步加载图像
     */
    static void loadImageAsync(const URI& uri, WebAssemblyHTTPClient::ImageCallback callback)
    {
        if (uri.isRemote())
        {
            WebAssemblyHTTPClient::downloadImageAsync(uri.full(), callback);
        }
        else
        {
            callback(nullptr, "Local file loading not supported in WebAssembly");
        }
    }

    /**
     * 尝试同步加载图像（实际上启动异步加载）
     */
    static osg::ref_ptr<osg::Image> loadImage(const URI& uri)
    {
        if (uri.isRemote())
        {
            // 在WebAssembly中，同步加载不可行
            // 这里返回一个占位符图像或nullptr
            std::cout << "[WebAssemblyImageLoader] 警告: 在WebAssembly中无法同步加载图像: " 
                      << uri.full() << std::endl;
            return nullptr;
        }
        else
        {
            return nullptr;
        }
    }
};

/**
 * 注册WebAssembly HTTP客户端
 */
inline void registerWebAssemblyHTTPClient()
{
#ifdef EMSCRIPTEN
    static bool registered = false;
    if (!registered)
    {
        // 注册STB_Image解码器
        registerSTBImageDecoder();
        
        registered = true;
        std::cout << "[WebAssemblyHTTPClient] ✅ WebAssembly HTTP客户端已注册" << std::endl;
    }
#endif
}

} // namespace osgEarth

#endif // WEBASSEMBLY_HTTP_CLIENT_H
