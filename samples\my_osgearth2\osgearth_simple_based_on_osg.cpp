// 基于成功的 OSG 示例创建的简化版 osgEarth 测试
// 采用与 my_osg_sample2 相同的初始化模式

#ifdef EMSCRIPTEN
#define _LIBCPP_HAS_NO_PRAGMA_SYSTEM_HEADER
#define _LIBCPP_DISABLE_AVAILABILITY
#endif

#ifdef _WIN32
#define _USE_MATH_DEFINES
#define NOMINMAX
#endif

#include <iostream>
#include <memory>
#include <string>

// OSG核心头文件
#include <osg/ref_ptr>
#include <osg/Group>
#include <osg/MatrixTransform>
#include <osg/Geode>
#include <osg/Geometry>
#include <osg/Texture2D>
#include <osg/Material>
#include <osg/StateSet>
#include <osg/ShapeDrawable>
#include <osg/Shape>

// OSG Viewer
#include <osgViewer/Viewer>
#include <osgViewer/ViewerEventHandlers>
#include <osgGA/TrackballManipulator>
#include <osgGA/StateSetManipulator>

// osgEarth 头文件 - 逐步添加
#include <osgEarth/MapNode>
#include <osgEarth/Map>

// 平台相关头文件
#ifdef EMSCRIPTEN
#include <SDL2/SDL.h>
#include <emscripten.h>
#include <emscripten/html5.h>
#else
#include <SDL.h>
#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#endif
#endif

// 全局变量
struct AppContext
{
    osg::ref_ptr<osgViewer::Viewer> viewer;
    osg::ref_ptr<osg::Group> rootNode;
    osg::ref_ptr<osgEarth::MapNode> mapNode;
    
    SDL_Window *window;
    SDL_GLContext context;
    bool shouldExit;
    
    AppContext() : shouldExit(false) {}
};

static AppContext* g_appContext = nullptr;

/**
 * 创建简单的 OSG 球体（作为对比测试）
 */
osg::ref_ptr<osg::Node> createSimpleEarth()
{
    std::cout << "[DEBUG] Creating simple OSG sphere..." << std::endl;
    
    osg::ref_ptr<osg::Geode> geode = new osg::Geode();
    osg::ref_ptr<osg::ShapeDrawable> sphere = new osg::ShapeDrawable(new osg::Sphere(osg::Vec3(0, 0, 0), 6378137.0f));
    
    // 设置材质
    osg::ref_ptr<osg::StateSet> stateSet = sphere->getOrCreateStateSet();
    osg::ref_ptr<osg::Material> material = new osg::Material();
    material->setDiffuse(osg::Material::FRONT_AND_BACK, osg::Vec4(0.2f, 0.5f, 0.8f, 1.0f));
    stateSet->setAttributeAndModes(material.get());
    
    geode->addDrawable(sphere.get());
    
    std::cout << "[DEBUG] Simple OSG sphere created successfully" << std::endl;
    return geode.get();
}

/**
 * 创建简单的 osgEarth 地图
 */
osg::ref_ptr<osg::Node> createSimpleOsgEarthMap()
{
    std::cout << "[DEBUG] Creating simple osgEarth map..." << std::endl;
    
    try
    {
        // 创建地图
        osg::ref_ptr<osgEarth::Map> map = new osgEarth::Map();
        
        // 创建地图节点
        osg::ref_ptr<osgEarth::MapNode> mapNode = new osgEarth::MapNode(map.get());
        
        std::cout << "[DEBUG] Simple osgEarth map created successfully" << std::endl;
        return mapNode.get();
    }
    catch (const std::exception& e)
    {
        std::cerr << "[ERROR] Failed to create osgEarth map: " << e.what() << std::endl;
        return nullptr;
    }
}

/**
 * 初始化SDL
 */
bool initializeSDL()
{
    std::cout << "[DEBUG] Initializing SDL..." << std::endl;
    
    if (SDL_Init(SDL_INIT_VIDEO) < 0)
    {
        std::cerr << "[ERROR] SDL initialization failed: " << SDL_GetError() << std::endl;
        return false;
    }

    // 设置OpenGL属性
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 3);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 0);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_PROFILE_MASK, SDL_GL_CONTEXT_PROFILE_ES);
    SDL_GL_SetAttribute(SDL_GL_DOUBLEBUFFER, 1);
    SDL_GL_SetAttribute(SDL_GL_DEPTH_SIZE, 24);
    SDL_GL_SetAttribute(SDL_GL_STENCIL_SIZE, 8);

    // 创建窗口
    g_appContext->window = SDL_CreateWindow(
        "osgEarth Simple Test Based on OSG",
        SDL_WINDOWPOS_CENTERED, SDL_WINDOWPOS_CENTERED,
        800, 600,
        SDL_WINDOW_OPENGL | SDL_WINDOW_SHOWN
    );

    if (!g_appContext->window)
    {
        std::cerr << "[ERROR] Window creation failed: " << SDL_GetError() << std::endl;
        return false;
    }

    // 创建OpenGL上下文
    g_appContext->context = SDL_GL_CreateContext(g_appContext->window);
    if (!g_appContext->context)
    {
        std::cerr << "[ERROR] OpenGL context creation failed: " << SDL_GetError() << std::endl;
        return false;
    }

    SDL_GL_MakeCurrent(g_appContext->window, g_appContext->context);
    SDL_GL_SetSwapInterval(1); // 启用垂直同步

    std::cout << "[DEBUG] SDL initialized successfully" << std::endl;
    return true;
}

/**
 * 初始化OSG - 采用与成功的 OSG 示例相同的模式
 */
bool initializeOSG()
{
    std::cout << "[DEBUG] Initializing OSG..." << std::endl;
    
    // 创建viewer
    g_appContext->viewer = new osgViewer::Viewer();

    // 设置为嵌入式窗口
    int x, y, width, height;
    SDL_GetWindowPosition(g_appContext->window, &x, &y);
    SDL_GetWindowSize(g_appContext->window, &width, &height);

#ifdef EMSCRIPTEN
    // WebAssembly版本 - 使用与成功的 OSG 示例相同的设置
    g_appContext->viewer->setUpViewerAsEmbeddedInWindow(0, 0, width, height);

    // WebGL兼容性设置 - 强制单线程
    g_appContext->viewer->setThreadingModel(osgViewer::Viewer::SingleThreaded);

    // 禁用不支持的OpenGL功能
    osg::ref_ptr<osg::StateSet> globalStateSet = g_appContext->viewer->getCamera()->getOrCreateStateSet();

    // 禁用WebGL不支持的功能
    globalStateSet->setMode(GL_ALPHA_TEST, osg::StateAttribute::OFF);

#ifndef __EMSCRIPTEN__
    globalStateSet->setMode(GL_POLYGON_SMOOTH, osg::StateAttribute::OFF);
    globalStateSet->setMode(GL_LINE_SMOOTH, osg::StateAttribute::OFF);
    globalStateSet->setMode(GL_POINT_SMOOTH, osg::StateAttribute::OFF);
    globalStateSet->setMode(GL_POLYGON_STIPPLE, osg::StateAttribute::OFF);
    globalStateSet->setMode(GL_LINE_STIPPLE, osg::StateAttribute::OFF);
#endif

    // 强制使用VBO
    osg::DisplaySettings::instance()->setVertexBufferHint(osg::DisplaySettings::VERTEX_BUFFER_OBJECT);

    // WebGL纹理渲染优化设置
    globalStateSet->setMode(GL_CULL_FACE, osg::StateAttribute::ON);
    globalStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);

    // 设置清除颜色为深蓝色，便于调试
    g_appContext->viewer->getCamera()->setClearColor(osg::Vec4(0.1f, 0.1f, 0.3f, 1.0f));

    std::cout << "[DEBUG] WebGL compatibility settings applied" << std::endl;
#else
    // 桌面版本设置
    osg::ref_ptr<osg::GraphicsContext::Traits> traits = new osg::GraphicsContext::Traits();
    traits->x = x;
    traits->y = y;
    traits->width = width;
    traits->height = height;
    traits->windowDecoration = false;
    traits->doubleBuffer = true;
    traits->sharedContext = 0;

    osg::ref_ptr<osg::GraphicsContext> gc = osg::GraphicsContext::createGraphicsContext(traits.get());
    g_appContext->viewer->getCamera()->setGraphicsContext(gc.get());
#endif

    // 设置相机参数
    g_appContext->viewer->getCamera()->setViewport(new osg::Viewport(0, 0, width, height));
    g_appContext->viewer->getCamera()->setProjectionMatrixAsPerspective(30.0, (double)width / height, 1.0, 1000000000.0);

    // 添加操作器
    g_appContext->viewer->setCameraManipulator(new osgGA::TrackballManipulator());

    // 添加事件处理器
    g_appContext->viewer->addEventHandler(new osgGA::StateSetManipulator(g_appContext->viewer->getCamera()->getOrCreateStateSet()));
    g_appContext->viewer->addEventHandler(new osgViewer::StatsHandler());
    g_appContext->viewer->addEventHandler(new osgViewer::WindowSizeHandler());

    std::cout << "[DEBUG] OSG initialized successfully" << std::endl;
    return true;
}

/**
 * 主循环
 */
void mainLoop()
{
    // 处理SDL事件
    SDL_Event event;
    while (SDL_PollEvent(&event))
    {
        if (event.type == SDL_QUIT)
        {
            g_appContext->shouldExit = true;
        }
    }

    // 渲染OSG场景
    if (g_appContext->viewer.valid())
    {
        g_appContext->viewer->frame();
    }

#ifndef EMSCRIPTEN
    // 桌面版本需要交换缓冲区
    SDL_GL_SwapWindow(g_appContext->window);
#endif
}

/**
 * 主函数
 */
int main(int argc, char *argv[])
{
#ifndef EMSCRIPTEN
    // 对于桌面版本，分配控制台用于调试输出
    if (AllocConsole())
    {
        freopen_s((FILE **)stdout, "CONOUT$", "w", stdout);
        freopen_s((FILE **)stderr, "CONOUT$", "w", stderr);
        freopen_s((FILE **)stdin, "CONIN$", "r", stdin);
        SetConsoleTitle("osgEarth Simple Test - Debug Output");
    }
#endif

    std::cout << "========================================" << std::endl;
    std::cout << "osgEarth Simple Test Based on OSG" << std::endl;
    std::cout << "========================================" << std::endl;

    // 创建应用上下文
    g_appContext = new AppContext();

    // 初始化SDL
    if (!initializeSDL())
    {
        delete g_appContext;
        return -1;
    }

    // 初始化OSG
    if (!initializeOSG())
    {
        delete g_appContext;
        return -1;
    }

    // 创建根节点
    g_appContext->rootNode = new osg::Group();

    // 测试1：先尝试创建简单的 OSG 球体
    std::cout << "[TEST] Creating simple OSG sphere..." << std::endl;
    osg::ref_ptr<osg::Node> simpleEarth = createSimpleEarth();
    if (simpleEarth.valid())
    {
        g_appContext->rootNode->addChild(simpleEarth.get());
        std::cout << "[TEST] Simple OSG sphere added successfully" << std::endl;
    }

    // 测试2：尝试创建 osgEarth 地图（如果失败，至少有 OSG 球体显示）
    std::cout << "[TEST] Creating osgEarth map..." << std::endl;
    
    // 在添加 osgEarth 之前，先初始化 osgEarth
    try
    {
        std::cout << "[DEBUG] Initializing osgEarth..." << std::endl;
        osgEarth::initialize();
        std::cout << "[DEBUG] osgEarth initialized successfully" << std::endl;
        
        osg::ref_ptr<osg::Node> osgEarthMap = createSimpleOsgEarthMap();
        if (osgEarthMap.valid())
        {
            // 如果 osgEarth 创建成功，替换简单球体
            g_appContext->rootNode->removeChildren(0, g_appContext->rootNode->getNumChildren());
            g_appContext->rootNode->addChild(osgEarthMap.get());
            std::cout << "[TEST] osgEarth map added successfully" << std::endl;
        }
        else
        {
            std::cout << "[TEST] osgEarth map creation failed, using simple OSG sphere" << std::endl;
        }
    }
    catch (const std::exception& e)
    {
        std::cerr << "[ERROR] osgEarth initialization failed: " << e.what() << std::endl;
        std::cout << "[TEST] Using simple OSG sphere as fallback" << std::endl;
    }

    // 设置场景
    g_appContext->viewer->setSceneData(g_appContext->rootNode.get());

    std::cout << "[INFO] Starting main loop..." << std::endl;

    // 启动主循环
#ifdef EMSCRIPTEN
    emscripten_set_main_loop(mainLoop, 60, 1);
#else
    while (!g_appContext->shouldExit)
    {
        mainLoop();
        std::this_thread::sleep_for(std::chrono::milliseconds(16)); // ~60 FPS
    }
#endif

    // 清理资源
    std::cout << "[INFO] Cleaning up..." << std::endl;
    delete g_appContext;
    SDL_Quit();

    return 0;
}
