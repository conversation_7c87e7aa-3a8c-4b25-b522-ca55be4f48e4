// 地形引擎创建失败诊断程序
// 专门用于调试 TerrainEngineNode::create() 失败的原因

#ifdef EMSCRIPTEN
#define _LIBCPP_HAS_NO_PRAGMA_SYSTEM_HEADER
#define _LIBCPP_DISABLE_AVAILABILITY
#endif

#ifdef _WIN32
#define _USE_MATH_DEFINES
#define NOMINMAX
#endif

#include <iostream>
#include <memory>
#include <string>

// OSG核心头文件
#include <osg/ref_ptr>
#include <osg/Group>
#include <osgDB/Registry>

// OSG Viewer
#include <osgViewer/Viewer>
#include <osgGA/TrackballManipulator>

// osgEarth 头文件
#include <osgEarth/Registry>
#include <osgEarth/TerrainEngineNode>
#include <osgEarth/TerrainOptions>
#include <osgEarth/Map>
#include <osgEarth/MapNode>

// 平台相关头文件
#ifdef EMSCRIPTEN
#include <SDL2/SDL.h>
#include <emscripten.h>
#include <emscripten/html5.h>
#else
#include <SDL.h>
#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#endif
#endif

#define DEBUG_LOG(msg) std::cout << "[DEBUG] " << msg << std::endl
#define ERROR_LOG(msg) std::cerr << "[ERROR] " << msg << std::endl
#define INFO_LOG(msg) std::cout << "[INFO] " << msg << std::endl

/**
 * 测试 OSG 插件系统
 */
void testOSGPluginSystem()
{
    INFO_LOG("=== 测试 OSG 插件系统 ===");

    // 获取 OSG 注册表
    osgDB::Registry *registry = osgDB::Registry::instance();

    // 列出所有可用的 ReaderWriter
    INFO_LOG("可用的 ReaderWriter 插件:");
    auto &rwMap = registry->getReaderWriterList();
    for (auto &rw : rwMap)
    {
        INFO_LOG("  - " << rw->className());
    }

    // 测试特定的地形引擎插件
    std::vector<std::string> engineNames = {"rex", "mp"};

    for (const std::string &engineName : engineNames)
    {
        INFO_LOG("测试地形引擎: " << engineName);

        std::string driverExt = std::string("osgearth_engine_") + engineName;
        INFO_LOG("  查找插件: " << driverExt);

        auto rw = registry->getReaderWriterForExtension(driverExt);
        if (rw)
        {
            INFO_LOG("  ✅ 找到 ReaderWriter: " << rw->className());

            // 尝试读取对象
            try
            {
                osg::ref_ptr<osg::Object> object = rw->readObject("." + driverExt).getObject();
                if (object.valid())
                {
                    INFO_LOG("  ✅ 成功创建对象: " << object->className());

                    // 尝试转换为 TerrainEngineNode
                    osgEarth::TerrainEngineNode *node = dynamic_cast<osgEarth::TerrainEngineNode *>(object.get());
                    if (node)
                    {
                        INFO_LOG("  ✅ 成功转换为 TerrainEngineNode");
                    }
                    else
                    {
                        ERROR_LOG("  ❌ 无法转换为 TerrainEngineNode");
                    }
                }
                else
                {
                    ERROR_LOG("  ❌ 无法创建对象");
                }
            }
            catch (const std::exception &e)
            {
                ERROR_LOG("  ❌ 创建对象时异常: " << e.what());
            }
        }
        else
        {
            ERROR_LOG("  ❌ 未找到 ReaderWriter");
        }
    }
}

/**
 * 测试 osgEarth Registry
 */
void testOsgEarthRegistry()
{
    INFO_LOG("=== 测试 osgEarth Registry ===");

    try
    {
        osgEarth::Registry *registry = osgEarth::Registry::instance();
        if (registry)
        {
            INFO_LOG("✅ osgEarth Registry 创建成功");

            // 获取默认地形引擎驱动程序名称
            std::string defaultDriver = registry->getDefaultTerrainEngineDriverName();
            INFO_LOG("默认地形引擎驱动程序: " << defaultDriver);

            // 检查覆盖设置
            auto override = registry->overrideTerrainEngineDriverName();
            if (override.isSet())
            {
                INFO_LOG("覆盖地形引擎驱动程序: " << override.get());
            }
            else
            {
                INFO_LOG("未设置覆盖地形引擎驱动程序");
            }
        }
        else
        {
            ERROR_LOG("❌ osgEarth Registry 创建失败");
        }
    }
    catch (const std::exception &e)
    {
        ERROR_LOG("❌ osgEarth Registry 异常: " << e.what());
    }
}

/**
 * 测试 TerrainEngineNode 创建
 */
void testTerrainEngineCreation()
{
    INFO_LOG("=== 测试 TerrainEngineNode 创建 ===");

    try
    {
        // 创建默认的地形选项
        osgEarth::TerrainOptions options;
        INFO_LOG("地形选项创建成功");

        // 尝试创建地形引擎
        INFO_LOG("尝试创建地形引擎...");
        osg::ref_ptr<osgEarth::TerrainEngineNode> terrainEngine = osgEarth::TerrainEngineNode::create(options);

        if (terrainEngine.valid())
        {
            INFO_LOG("✅ 地形引擎创建成功: " << terrainEngine->className());
        }
        else
        {
            ERROR_LOG("❌ 地形引擎创建失败");

            // 尝试手动指定驱动程序
            std::vector<std::string> drivers = {"rex", "mp"};
            for (const std::string &driver : drivers)
            {
                INFO_LOG("尝试使用驱动程序: " << driver);
                osgEarth::TerrainOptions specificOptions;
                specificOptions.setDriver(driver);

                osg::ref_ptr<osgEarth::TerrainEngineNode> specificEngine =
                    osgEarth::TerrainEngineNode::create(specificOptions);

                if (specificEngine.valid())
                {
                    INFO_LOG("✅ 使用 " << driver << " 驱动程序创建成功");
                    break;
                }
                else
                {
                    ERROR_LOG("❌ 使用 " << driver << " 驱动程序创建失败");
                }
            }
        }
    }
    catch (const std::exception &e)
    {
        ERROR_LOG("❌ TerrainEngineNode 创建异常: " << e.what());
    }
}

/**
 * 测试完整的 MapNode 创建
 */
void testMapNodeCreation()
{
    INFO_LOG("=== 测试 MapNode 创建 ===");

    try
    {
        // 创建地图
        osg::ref_ptr<osgEarth::Map> map = new osgEarth::Map();
        INFO_LOG("地图创建成功");

        // 创建地图节点
        INFO_LOG("尝试创建 MapNode...");
        osg::ref_ptr<osgEarth::MapNode> mapNode = new osgEarth::MapNode(map.get());

        if (mapNode.valid())
        {
            INFO_LOG("✅ MapNode 创建成功");

            // 检查地形引擎
            auto terrainEngine = mapNode->getTerrainEngine();
            if (terrainEngine)
            {
                INFO_LOG("✅ 地形引擎已附加");
            }
            else
            {
                ERROR_LOG("❌ 地形引擎未附加");
            }
        }
        else
        {
            ERROR_LOG("❌ MapNode 创建失败");
        }
    }
    catch (const std::exception &e)
    {
        ERROR_LOG("❌ MapNode 创建异常: " << e.what());
    }
}

/**
 * 主函数
 */
int main(int argc, char *argv[])
{
#ifndef EMSCRIPTEN
    // 对于桌面版本，分配控制台用于调试输出
    if (AllocConsole())
    {
        freopen_s((FILE **)stdout, "CONOUT$", "w", stdout);
        freopen_s((FILE **)stderr, "CONOUT$", "w", stderr);
        freopen_s((FILE **)stdin, "CONIN$", "r", stdin);
        SetConsoleTitle("Terrain Engine Debug - Debug Output");
    }
#endif

    std::cout << "========================================" << std::endl;
    std::cout << "osgEarth 地形引擎创建失败诊断程序" << std::endl;
    std::cout << "========================================" << std::endl;

    // 不调用 osgEarth::initialize() 来避免 Capabilities 问题
    INFO_LOG("跳过 osgEarth::initialize() 调用");

    // 1. 测试 OSG 插件系统
    testOSGPluginSystem();

    std::cout << std::endl;

    // 2. 测试 osgEarth Registry
    testOsgEarthRegistry();

    std::cout << std::endl;

    // 3. 测试 TerrainEngineNode 创建
    testTerrainEngineCreation();

    std::cout << std::endl;

    // 4. 测试完整的 MapNode 创建
    testMapNodeCreation();

    std::cout << std::endl;
    std::cout << "========================================" << std::endl;
    std::cout << "诊断完成" << std::endl;
    std::cout << "========================================" << std::endl;

    return 0;
}
