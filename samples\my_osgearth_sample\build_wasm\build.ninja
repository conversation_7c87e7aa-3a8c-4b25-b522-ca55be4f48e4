# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.26

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: osgearth_simple_earth
# Configurations: Release
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Release
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/build_wasm/
# =============================================================================
# Object build statements for EXECUTABLE target osgearth_simple_earth


#############################################
# Order-only phony target for osgearth_simple_earth

build cmake_object_order_depends_target_osgearth_simple_earth: phony || CMakeFiles/osgearth_simple_earth.dir

build CMakeFiles/osgearth_simple_earth.dir/main.cpp.o: CXX_COMPILER__osgearth_simple_earth_unscanned_Release F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/main.cpp || cmake_object_order_depends_target_osgearth_simple_earth
  DEP_FILE = CMakeFiles\osgearth_simple_earth.dir\main.cpp.o.d
  FLAGS = -O3 -DNDEBUG -std=gnu++17 -DOSG_GLSL_VERSION=300 -DOSGEARTH_HAVE_GEOS=1 -DOSGEARTH_HAVE_GDAL=0 -DOSGEARTH_HAVE_PROJ=1 -DUSE_EXTERNAL_WASM_DEPENDS=ON -DSTB_IMAGE_IMPLEMENTATION -DSTBI_NO_STDIO -DSTBI_NO_FAILURE_STRINGS -DSTBI_ONLY_PNG -DSTBI_ONLY_JPEG "-s USE_WEBGL2=1" "-s ALLOW_MEMORY_GROWTH=1" "-s MAXIMUM_MEMORY=2GB" "-s FETCH=1" -O3
  INCLUDES = -IF:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/../.. -IF:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/../../../src/osgEarth -IF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include
  OBJECT_DIR = CMakeFiles\osgearth_simple_earth.dir
  OBJECT_FILE_DIR = CMakeFiles\osgearth_simple_earth.dir


# =============================================================================
# Link build statements for EXECUTABLE target osgearth_simple_earth


#############################################
# Link the executable osgearth_simple_earth.html

build osgearth_simple_earth.html: CXX_EXECUTABLE_LINKER__osgearth_simple_earth_Release CMakeFiles/osgearth_simple_earth.dir/main.cpp.o | F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/../../../redist_wasm/libosgEarth.a F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/../../../redist_wasm/osgdb_earth.a F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/../../../redist_wasm/osgdb_osgearth_engine_rex.a F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/../../../redist_wasm/osgdb_osgearth_cache_filesystem.a F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/../../../redist_wasm/osgdb_osgearth_sky_simple.a F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/../../../redist_wasm/osgdb_osgearth_tileindex.a F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/../../../redist_wasm/osgdb_osgearth_xyz.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosg.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgViewer.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgDB.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgGA.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgUtil.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libOpenThreads.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgText.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgShadow.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgSim.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgAnimation.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgFX.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgManipulator.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgParticle.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgPresentation.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgTerrain.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgVolume.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgWidget.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libGeographicLib.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libgeos.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libgeos_c.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libproj.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libcurl.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libz.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libexpat.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libpng16.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libjpeg.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libtiff.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libfreetype.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libfontconfig.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libbz2.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/liblzma.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libssl.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libcrypto.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libwebp.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libwebpmux.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libwebpdemux.a
  FLAGS = -O3 -DNDEBUG
  LINK_FLAGS = -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s WASM=1 -s INITIAL_MEMORY=268435456 -s ALLOW_MEMORY_GROWTH=1 -s MAXIMUM_MEMORY=2147483648 -s FETCH=1 -s ASYNCIFY=1 -s ASSERTIONS=1 -s SAFE_HEAP=0 -s STACK_OVERFLOW_CHECK=1 -s EXPORTED_FUNCTIONS=['_main'] -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','HEAPU8','getValue','setValue'] -s NO_EXIT_RUNTIME=1 -s DISABLE_EXCEPTION_CATCHING=0 -s FORCE_FILESYSTEM=1 -s PTHREAD_POOL_SIZE=4 -s USE_PTHREADS=1 -s PROXY_TO_PTHREAD=1 -s OFFSCREENCANVAS_SUPPORT=1 -s OFFSCREEN_FRAMEBUFFER=1 -s MODULARIZE=1 -s EXPORT_NAME='createOSGEarthModule' -s ENVIRONMENT=web,worker -s SHARED_MEMORY=1 -s TEXTDECODER=2 -s ABORTING_MALLOC=0 -s ALLOW_UNIMPLEMENTED_SYSCALLS=1 -O3 --bind --preload-file F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/data@/data --shell-file F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/build_wasm/shell_template.html
  LINK_LIBRARIES = F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/../../../redist_wasm/libosgEarth.a  F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/../../../redist_wasm/osgdb_earth.a  F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/../../../redist_wasm/osgdb_osgearth_engine_rex.a  F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/../../../redist_wasm/osgdb_osgearth_cache_filesystem.a  F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/../../../redist_wasm/osgdb_osgearth_sky_simple.a  F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/../../../redist_wasm/osgdb_osgearth_tileindex.a  F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/../../../redist_wasm/osgdb_osgearth_xyz.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosg.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgViewer.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgDB.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgGA.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgUtil.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libOpenThreads.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgText.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgShadow.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgSim.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgAnimation.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgFX.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgManipulator.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgParticle.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgPresentation.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgTerrain.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgVolume.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgWidget.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libGeographicLib.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libgeos.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libgeos_c.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libproj.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libcurl.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libz.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libexpat.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libpng16.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libjpeg.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libtiff.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libfreetype.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libfontconfig.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libbz2.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/liblzma.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libssl.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libcrypto.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libwebp.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libwebpmux.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libwebpdemux.a
  OBJECT_DIR = CMakeFiles\osgearth_simple_earth.dir
  POST_BUILD = cmd.exe /C "cd /D F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\src\applications\my_osgearth_sample\build_wasm && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/build_wasm/osgearth_simple_earth.html F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/build_wasm/osgearth_simple_earth.js F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/build_wasm/osgearth_simple_earth.wasm F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/redist_wasm/"
  PRE_LINK = cd .
  TARGET_FILE = osgearth_simple_earth.html
  TARGET_PDB = osgearth_simple_earth.html.dbg
  RSP_FILE = CMakeFiles\osgearth_simple_earth.rsp


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\src\applications\my_osgearth_sample\build_wasm && "C:\Program Files\CMake\bin\cmake-gui.exe" -SF:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\src\applications\my_osgearth_sample -BF:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\src\applications\my_osgearth_sample\build_wasm"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\src\applications\my_osgearth_sample\build_wasm && "C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -SF:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\src\applications\my_osgearth_sample -BF:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\src\applications\my_osgearth_sample\build_wasm"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for install

build CMakeFiles/install.util: CUSTOM_COMMAND all
  COMMAND = cmd.exe /C "cd /D F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\src\applications\my_osgearth_sample\build_wasm && "C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake"
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles/install.util


#############################################
# Utility command for install/local

build CMakeFiles/install/local.util: CUSTOM_COMMAND all
  COMMAND = cmd.exe /C "cd /D F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\src\applications\my_osgearth_sample\build_wasm && "C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake"
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install/local: phony CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build CMakeFiles/install/strip.util: CUSTOM_COMMAND all
  COMMAND = cmd.exe /C "cd /D F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\src\applications\my_osgearth_sample\build_wasm && "C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake"
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build install/strip: phony CMakeFiles/install/strip.util

# =============================================================================
# Target aliases.

build osgearth_simple_earth: phony osgearth_simple_earth.html

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/build_wasm

build all: phony osgearth_simple_earth.html

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCCompiler.cmake.in C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCXXCompiler.cmake.in C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCXXInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCommonLanguageInclude.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCXXCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeDetermineSystem.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeFindBinUtils.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeGenericSystem.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeInitializeConfigs.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeLanguageInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeNinjaFindMake.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystem.cmake.in C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInitialize.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeTestCCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-C.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-CXX.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/GNU.cmake C$:/dev/emsdk/upstream/emscripten/cmake/Modules/Platform/Emscripten.cmake CMakeCache.txt CMakeFiles/3.26.4/CMakeCCompiler.cmake CMakeFiles/3.26.4/CMakeCXXCompiler.cmake CMakeFiles/3.26.4/CMakeSystem.cmake F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/CMakeLists.txt
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCCompiler.cmake.in C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCXXCompiler.cmake.in C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCXXInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCommonLanguageInclude.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCXXCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeDetermineSystem.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeFindBinUtils.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeGenericSystem.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeInitializeConfigs.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeLanguageInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeNinjaFindMake.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystem.cmake.in C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInitialize.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeTestCCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-C.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-CXX.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/GNU.cmake C$:/dev/emsdk/upstream/emscripten/cmake/Modules/Platform/Emscripten.cmake CMakeCache.txt CMakeFiles/3.26.4/CMakeCCompiler.cmake CMakeFiles/3.26.4/CMakeCXXCompiler.cmake CMakeFiles/3.26.4/CMakeSystem.cmake F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/CMakeLists.txt: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
