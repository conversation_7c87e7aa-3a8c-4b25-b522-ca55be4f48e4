@echo off
echo ========================================
echo Simple WebAssembly Test Build
echo ========================================

REM 设置Emscripten环境
echo Setting up Emscripten environment...
call C:\dev\emsdk\emsdk_env.bat

REM 检查emcc是否可用
echo Checking emcc...
C:\dev\emsdk\python\3.13.3_64bit\python.exe C:\dev\emsdk\upstream\emscripten\emcc.py --version
if %ERRORLEVEL% neq 0 (
    echo Failed to run emcc
    pause
    exit /b 1
)

echo emcc is working!

REM 编译简单测试
echo Compiling simple test...
C:\dev\emsdk\python\3.13.3_64bit\python.exe C:\dev\emsdk\upstream\emscripten\emcc.py ^
    simple_test.cpp ^
    -o simple_test.html ^
    -s WASM=1 ^
    -s USE_WEBGL2=1 ^
    -s FULL_ES3=1 ^
    -std=c++20

if %ERRORLEVEL% neq 0 (
    echo Compilation failed
    pause
    exit /b 1
)

echo Simple test compiled successfully!
echo Files generated:
dir simple_test.*

echo.
echo To test, run a local server:
echo   python -m http.server 8080
echo   Then open http://localhost:8080/simple_test.html

pause
