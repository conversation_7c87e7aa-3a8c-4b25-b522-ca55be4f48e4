/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once
#include <osgEarth/Common>
#include <osgEarth/TileKey>
#include <osgEarth/VisibleLayer>
#include <osgEarth/TextureArena>
#include <osgEarth/StateSetCache>
#include <osgEarth/Chonk>
#include <osg/Node>

namespace osgEarth
{
    class ProgressCallback;

    /**
     * Layer that creates a scene graph from a tiled data source.
     * This class is abstract; subclasses must implement createTileImplementation().
     */
    class OSGEARTH_EXPORT TiledModelLayer : public VisibleLayer
    {
    public:
        // options serialization
        struct OSGEARTH_EXPORT Options : public VisibleLayer::Options
        {
            META_LayerOptions(osgEarth, Options, VisibleLayer::Options);
            OE_OPTION(bool, additive, false);
            OE_OPTION(float, rangeFactor, 6.0);
            OE_OPTION(unsigned, minLevel, 0u);
            OE_OPTION(unsigned, maxLevel, 20u);
            OE_OPTION(bool, nvgl, false);

            Config getConfig() const override;
            void fromConfig(const Config& conf);
        };

    public:
        META_Layer_Abstract(osgEarth, TiledModelLayer, Options, VisibleLayer);

        //! Create an OSG node from a tile key.
        //! @param key The tile key.
        //! @param  progress A progress callback
        osg::ref_ptr<osg::Node> createTile(const TileKey& key, ProgressCallback* progress) const;

        //! Profile of the underlying tiled data source.
        virtual const Profile* getProfile() const = 0;

        //! Minimum available LOD of the underlying tiled data source.
        virtual unsigned getMinLevel() const;
        void setMinLevel(unsigned value);

        //! Maximum available LOD of the underlying tiled data source.
        virtual unsigned getMaxLevel() const;
        void setMaxLevel(unsigned value);

        //! Whether new LODs should be added to the scene (true) or replace existing ones (false)
        void setAdditive(const bool& value);
        const bool& getAdditive() const;

        //! The range factor.  Default is 6
        void setRangeFactor(const float &value);
        const float& getRangeFactor() const;

        //! Forces a rebuild on this layer.
        void dirty() override;

    public: // Layer

        // The Node representing this layer.
        osg::Node* getNode() const override;

        // called by the map when this layer is added
        void addedToMap(const Map*) override;

        // called by the map when this layer is removed
        void removedFromMap(const Map*) override;

        // post-ctor initialization
        void init() override;

        // close
        Status closeImplementation() override;

    protected:
        //! Subclasses must implement this to create a tile.
        //! @param key The tile key, in the underlying tiled data source's profile.
        //! @param progress A progress callback that can be checked for user cancellation.
        virtual osg::ref_ptr<osg::Node> createTileImplementation(const TileKey& key, ProgressCallback* progress) const = 0;

        // NVGL support:
        osg::ref_ptr<TextureArena> _textures;
        mutable ChonkFactory _chonkFactory;
        mutable std::vector<Texture::WeakPtr> _texturesCache;
        mutable std::mutex _texturesCacheMutex;

        virtual void create();
        osg::ref_ptr<osg::Group> _root;

        osg::ref_ptr<const Map> getMap() const;

    private:
        osg::observer_ptr<const Map> _map;
        bool _graphDirty;
        osg::ref_ptr<StateSetCache> _statesetCache;

        using L2Cache = LRUCache<TileKey, osg::ref_ptr<osg::Node>>;
        mutable L2Cache _localcache{ 128u };
        mutable Gate<TileKey> _gate;
    };
}
