// osgEarth WebAssembly 版本 - 使用 SDL 修复 WebGL 上下文问题
// 参考成功的 OSG 示例，使用 SDL 创建窗口和 OpenGL 上下文

#ifdef EMSCRIPTEN
#define _LIBCPP_HAS_NO_PRAGMA_SYSTEM_HEADER
#define _LIBCPP_DISABLE_AVAILABILITY
#endif

#ifdef _WIN32
#define _USE_MATH_DEFINES
#define NOMINMAX
#endif

#include <iostream>
#include <memory>
#include <string>
#include <typeinfo>

// OSG核心头文件
#include <osg/ref_ptr>
#include <osg/Group>
#include <osg/Geode>
#include <osg/ShapeDrawable>
#include <osg/Shape>
#include <osg/StateSet>
#include <osg/Material>
#include <osg/Texture2D>
#include <osg/Image>
#include <osg/TexEnv>
#include <osg/TexGen>
#include <osg/MatrixTransform>
#include <osg/PositionAttitudeTransform>
#include <osg/DisplaySettings>
#include <osg/PolygonMode>
#include <osgDB/Registry>

// OSG Viewer
#include <osgViewer/Viewer>
#include <osgViewer/ViewerEventHandlers>
#include <osgGA/TrackballManipulator>
#include <osgGA/StateSetManipulator>

// osgEarth 头文件
#include <osgEarth/Registry>
#include <osgEarth/TerrainEngineNode>
#include <osgEarth/TerrainOptions>
#include <osgEarth/Map>
#include <osgEarth/MapNode>
#include <osgEarth/EarthManipulator>
#include <osgEarth/ExampleResources>
#include <osgEarth/XYZ>
#include <osgEarth/ImageLayer>
#include <osgEarth/ElevationLayer>
#include <osgEarth/GeodeticGraticule>

// 添加缺少的头文件
#include <thread>
#include <chrono>

// 平台相关头文件
#ifdef EMSCRIPTEN
#include <SDL2/SDL.h>
#include <emscripten.h>
#include <emscripten/html5.h>
#include <emscripten/threading.h>
#else
#include <SDL.h>
#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#endif
#endif

#define DEBUG_LOG(msg) std::cout << "[DEBUG] " << msg << std::endl
#define ERROR_LOG(msg) std::cerr << "[ERROR] " << msg << std::endl
#define INFO_LOG(msg) std::cout << "[INFO] " << msg << std::endl

// 全局变量
struct AppContext
{
    osg::ref_ptr<osgViewer::Viewer> viewer;
    osg::ref_ptr<osg::Group> rootNode;
    osg::ref_ptr<osgEarth::MapNode> mapNode;

    SDL_Window *window;
    SDL_GLContext context;
    bool shouldExit;

    AppContext() : shouldExit(false), window(nullptr), context(nullptr) {}
};

static AppContext *g_appContext = nullptr;

/**
 * 检查 osgEarth 插件状态
 */
void checkOsgEarthPlugins()
{
    INFO_LOG("=== 检查 osgEarth 插件状态 ===");

    // 检查 osgDB 注册表
    osgDB::Registry *registry = osgDB::Registry::instance();
    if (!registry)
    {
        ERROR_LOG("❌ osgDB::Registry 未初始化");
        return;
    }

    INFO_LOG("✅ osgDB::Registry 已初始化");

    // 检查地形引擎插件
    auto rw_rex = registry->getReaderWriterForExtension("osgearth_engine_rex");
    if (rw_rex)
    {
        INFO_LOG("✅ REX 地形引擎插件已注册: " << rw_rex->className());
    }
    else
    {
        ERROR_LOG("❌ REX 地形引擎插件未注册");
    }

    // 检查其他重要插件
    auto rw_earth = registry->getReaderWriterForExtension("earth");
    if (rw_earth)
    {
        INFO_LOG("✅ Earth 文件插件已注册: " << rw_earth->className());
    }
    else
    {
        ERROR_LOG("❌ Earth 文件插件未注册");
    }

    // 列出所有已注册的插件
    INFO_LOG("已注册的 ReaderWriter 插件数量: " << registry->getReaderWriterList().size());
}

/**
 * 测试基础 osgEarth 功能
 */
void testOsgEarthBasics()
{
    INFO_LOG("=== 测试基础 osgEarth 功能 ===");

    try
    {
        // 1. 测试地图创建
        INFO_LOG("测试 1: 创建空地图");
        osg::ref_ptr<osgEarth::Map> map = new osgEarth::Map();
        if (map.valid())
        {
            INFO_LOG("✅ 空地图创建成功");
        }
        else
        {
            ERROR_LOG("❌ 空地图创建失败");
            return;
        }

        // 2. 测试地形引擎创建
        INFO_LOG("测试 2: 创建地形引擎");
        osgEarth::TerrainOptions terrainOptions;
        auto terrainEngine = osgEarth::TerrainEngineNode::create(terrainOptions);
        if (terrainEngine != nullptr)
        {
            INFO_LOG("✅ 地形引擎创建成功: " << typeid(*terrainEngine).name());
        }
        else
        {
            ERROR_LOG("❌ 地形引擎创建失败 - 这是主要问题");
        }

        // 3. 测试 MapNode 创建（不添加复杂图层）
        INFO_LOG("测试 3: 创建 MapNode");
        osg::ref_ptr<osgEarth::MapNode> mapNode = new osgEarth::MapNode(map.get());
        if (mapNode.valid())
        {
            INFO_LOG("✅ MapNode 创建成功");

            // 检查是否有地形引擎附加
            auto attachedEngine = mapNode->getTerrainEngine();
            if (attachedEngine)
            {
                INFO_LOG("✅ 地形引擎已自动附加到 MapNode");
            }
            else
            {
                ERROR_LOG("❌ 地形引擎未附加到 MapNode");
            }
        }
        else
        {
            ERROR_LOG("❌ MapNode 创建失败");
        }
    }
    catch (const std::exception &e)
    {
        ERROR_LOG("❌ osgEarth 基础测试异常: " << e.what());
    }
}

/**
 * 创建 osgEarth 地球场景
 */
osg::ref_ptr<osg::Node> createOsgEarthScene()
{
    INFO_LOG("=== 创建 osgEarth 地球场景 ===");

    try
    {
        // 1. 创建地图
        INFO_LOG("步骤 1: 创建地图");
        osg::ref_ptr<osgEarth::Map> map = new osgEarth::Map();
        if (!map.valid())
        {
            ERROR_LOG("❌ 地图创建失败");
            return nullptr;
        }
        INFO_LOG("✅ 地图创建成功");

        // 2. 设置地形选项
        INFO_LOG("步骤 2: 配置地形选项");
        osgEarth::TerrainOptions terrainOptions;
        terrainOptions.setDriver("rex"); // 明确指定使用 REX 引擎

#ifdef EMSCRIPTEN
        // WebGL 特定配置 - 极简模式
        INFO_LOG("WebGL 环境：应用极简兼容性设置");

        // 禁用所有可能导致着色器问题的功能
        terrainOptions.enableBlending() = false;  // 禁用混合
        terrainOptions.gpuTessellation() = false; // 禁用 GPU 细分
        terrainOptions.morphImagery() = false;    // 禁用图像变形
        terrainOptions.morphTerrain() = false;    // 禁用地形变形
        terrainOptions.enableLighting() = false;  // 禁用光照
        terrainOptions.castShadows() = false;     // 禁用阴影

        // 禁用高级渲染特性
        terrainOptions.normalizeEdges() = false; // 禁用边缘标准化
        terrainOptions.maxLOD() = 10;            // 限制 LOD 层级

        INFO_LOG("✅ WebGL 极简兼容性设置完成");
#endif

        // 3. 创建 MapNode 并手动设置地形引擎
        INFO_LOG("步骤 3: 创建 MapNode");
        osg::ref_ptr<osgEarth::MapNode> mapNode = new osgEarth::MapNode(map.get(), terrainOptions);

        if (!mapNode.valid())
        {
            ERROR_LOG("❌ MapNode 创建失败");
            return nullptr;
        }
        INFO_LOG("✅ MapNode 创建成功");

        // 4. 检查地形引擎
        auto terrainEngine = mapNode->getTerrainEngine();
        if (terrainEngine)
        {
            INFO_LOG("✅ 地形引擎已附加: " << typeid(*terrainEngine).name());
        }
        else
        {
            ERROR_LOG("❌ 地形引擎未附加，尝试手动创建");

            // 手动创建地形引擎
            auto manualEngine = osgEarth::TerrainEngineNode::create(terrainOptions);
            if (manualEngine)
            {
                INFO_LOG("✅ 手动创建地形引擎成功: " << typeid(*manualEngine).name());
                // 注意：这里我们不能直接设置地形引擎，需要通过其他方式
            }
            else
            {
                ERROR_LOG("❌ 手动创建地形引擎也失败");
            }
        }

        // 5. 设置 WebGL 兼容的显示模式
        INFO_LOG("步骤 4: 设置 WebGL 兼容的显示模式");
        osg::ref_ptr<osg::StateSet> stateSet = mapNode->getOrCreateStateSet();

// WebGL 不支持 PolygonMode，使用替代方案
#ifdef EMSCRIPTEN
        INFO_LOG("WebGL 环境：跳过 PolygonMode，使用基础材质");

        // 完全禁用材质和纹理系统，避免 WebGL 兼容性问题
        INFO_LOG("应用极简 WebGL 状态设置");

        // 强制禁用所有可能导致问题的状态
        stateSet->setMode(GL_LIGHTING, osg::StateAttribute::OFF | osg::StateAttribute::PROTECTED);
        stateSet->setMode(GL_BLEND, osg::StateAttribute::OFF | osg::StateAttribute::PROTECTED);
        stateSet->setMode(GL_ALPHA_TEST, osg::StateAttribute::OFF | osg::StateAttribute::PROTECTED);
        stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON | osg::StateAttribute::PROTECTED);
        stateSet->setMode(GL_CULL_FACE, osg::StateAttribute::ON | osg::StateAttribute::PROTECTED);

        // 禁用所有纹理单元，避免纹理参数错误
        for (int i = 0; i < 8; ++i)
        {
            stateSet->setTextureMode(i, GL_TEXTURE_2D, osg::StateAttribute::OFF | osg::StateAttribute::PROTECTED);
            stateSet->setTextureMode(i, GL_TEXTURE_3D, osg::StateAttribute::OFF | osg::StateAttribute::PROTECTED);
            stateSet->setTextureMode(i, GL_TEXTURE_CUBE_MAP, osg::StateAttribute::OFF | osg::StateAttribute::PROTECTED);
        }

        // 移除所有纹理属性，避免纹理参数设置
        stateSet->removeTextureAttribute(0, osg::StateAttribute::TEXTURE);
        stateSet->removeTextureAttribute(1, osg::StateAttribute::TEXTURE);
        stateSet->removeTextureAttribute(2, osg::StateAttribute::TEXTURE);
        stateSet->removeTextureAttribute(3, osg::StateAttribute::TEXTURE);

        // 设置最简单的固定颜色材质
        osg::ref_ptr<osg::Material> material = new osg::Material();
        material->setColorMode(osg::Material::OFF); // 禁用颜色跟踪
        material->setDiffuse(osg::Material::FRONT_AND_BACK, osg::Vec4(0.0f, 0.8f, 0.0f, 1.0f));
        stateSet->setAttributeAndModes(material.get(), osg::StateAttribute::ON | osg::StateAttribute::PROTECTED);

        // 强制禁用着色器程序，使用固定管线
        stateSet->removeAttribute(osg::StateAttribute::PROGRAM);

#else
        // 桌面版本可以使用 wireframe
        osg::ref_ptr<osg::PolygonMode> polygonMode = new osg::PolygonMode();
        polygonMode->setMode(osg::PolygonMode::FRONT_AND_BACK, osg::PolygonMode::LINE);
        stateSet->setAttributeAndModes(polygonMode.get(), osg::StateAttribute::ON);

        osg::ref_ptr<osg::Material> material = new osg::Material();
        material->setDiffuse(osg::Material::FRONT_AND_BACK, osg::Vec4(0.0f, 1.0f, 0.0f, 1.0f));
        material->setAmbient(osg::Material::FRONT_AND_BACK, osg::Vec4(0.0f, 0.5f, 0.0f, 1.0f));
        stateSet->setAttributeAndModes(material.get(), osg::StateAttribute::ON);
        stateSet->setMode(GL_LIGHTING, osg::StateAttribute::OFF);
#endif

        INFO_LOG("✅ Wireframe 模式设置完成");

        // 6. 创建根节点并添加地图
        osg::ref_ptr<osg::Group> root = new osg::Group();
        root->addChild(mapNode.get());

        INFO_LOG("✅ osgEarth 地球场景创建成功");
        return root.get();
    }
    catch (const std::exception &e)
    {
        ERROR_LOG("❌ osgEarth 场景创建异常: " << e.what());
        return nullptr;
    }
}

/**
 * 创建简单的测试场景（不使用 osgEarth）
 */
osg::ref_ptr<osg::Node> createSimpleTestScene()
{
    DEBUG_LOG("Creating simple test scene...");

    osg::ref_ptr<osg::Group> root = new osg::Group();

    // 创建一个简单的立方体
    osg::ref_ptr<osg::Geode> geode = new osg::Geode();
    osg::ref_ptr<osg::ShapeDrawable> box = new osg::ShapeDrawable(
        new osg::Box(osg::Vec3(0, 0, 0), 1.0f));

    // 设置材质
    osg::ref_ptr<osg::StateSet> stateSet = box->getOrCreateStateSet();
    osg::ref_ptr<osg::Material> material = new osg::Material();
    material->setDiffuse(osg::Material::FRONT_AND_BACK, osg::Vec4(0.8f, 0.2f, 0.2f, 1.0f));
    stateSet->setAttributeAndModes(material.get());

    geode->addDrawable(box.get());
    root->addChild(geode.get());

    DEBUG_LOG("Simple test scene created successfully");
    return root.get();
}

/**
 * 初始化SDL
 */
bool initializeSDL()
{
    DEBUG_LOG("Initializing SDL...");

    if (SDL_Init(SDL_INIT_VIDEO) < 0)
    {
        ERROR_LOG("SDL initialization failed: " << SDL_GetError());
        return false;
    }

    // 设置OpenGL属性
#ifdef EMSCRIPTEN
    // WebAssembly版本使用OpenGL ES
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 3);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 0);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_PROFILE_MASK, SDL_GL_CONTEXT_PROFILE_ES);
#else
    // 桌面版使用标准OpenGL
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 3);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 3);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_PROFILE_MASK, SDL_GL_CONTEXT_PROFILE_CORE);
#endif
    SDL_GL_SetAttribute(SDL_GL_DOUBLEBUFFER, 1);
    SDL_GL_SetAttribute(SDL_GL_DEPTH_SIZE, 24);
    SDL_GL_SetAttribute(SDL_GL_STENCIL_SIZE, 8);

    // 创建窗口
    g_appContext->window = SDL_CreateWindow(
        "osgEarth WebAssembly - SDL Fixed Version",
        SDL_WINDOWPOS_CENTERED, SDL_WINDOWPOS_CENTERED,
        800, 600,
        SDL_WINDOW_OPENGL | SDL_WINDOW_SHOWN);

    if (!g_appContext->window)
    {
        ERROR_LOG("Window creation failed: " << SDL_GetError());
        return false;
    }

    // 创建OpenGL上下文
    g_appContext->context = SDL_GL_CreateContext(g_appContext->window);
    if (!g_appContext->context)
    {
        ERROR_LOG("OpenGL context creation failed: " << SDL_GetError());
        return false;
    }

    SDL_GL_MakeCurrent(g_appContext->window, g_appContext->context);
    SDL_GL_SetSwapInterval(1);

    DEBUG_LOG("SDL initialized successfully");
    return true;
}

/**
 * 初始化OSG
 */
bool initializeOSG()
{
    DEBUG_LOG("Initializing OSG...");

    // 创建viewer
    g_appContext->viewer = new osgViewer::Viewer();

    // 设置为嵌入式窗口
    int x, y, width, height;
    SDL_GetWindowPosition(g_appContext->window, &x, &y);
    SDL_GetWindowSize(g_appContext->window, &width, &height);

#ifdef EMSCRIPTEN
    // WebAssembly版本 - 使用与成功的 OSG 示例相同的设置
    INFO_LOG("WebAssembly mode: Using embedded window setup for WebGL compatibility");

    g_appContext->viewer->setUpViewerAsEmbeddedInWindow(0, 0, width, height);
    g_appContext->viewer->setThreadingModel(osgViewer::Viewer::SingleThreaded);

    // WebGL兼容性设置
    osg::ref_ptr<osg::StateSet> globalStateSet = g_appContext->viewer->getCamera()->getOrCreateStateSet();
    globalStateSet->setMode(GL_ALPHA_TEST, osg::StateAttribute::OFF);
    globalStateSet->setMode(GL_CULL_FACE, osg::StateAttribute::ON);
    globalStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);

    // 强制使用VBO
    osg::DisplaySettings::instance()->setVertexBufferHint(osg::DisplaySettings::VERTEX_BUFFER_OBJECT);

    // 设置清除颜色
    g_appContext->viewer->getCamera()->setClearColor(osg::Vec4(0.1f, 0.1f, 0.3f, 1.0f));

    INFO_LOG("WebGL compatibility settings applied successfully");
#else
    // 桌面版本设置
    osg::ref_ptr<osg::GraphicsContext::Traits> traits = new osg::GraphicsContext::Traits();
    traits->x = x;
    traits->y = y;
    traits->width = width;
    traits->height = height;
    traits->windowDecoration = false;
    traits->doubleBuffer = true;
    traits->sharedContext = 0;

    osg::ref_ptr<osg::GraphicsContext> gc = osg::GraphicsContext::createGraphicsContext(traits.get());
    g_appContext->viewer->getCamera()->setGraphicsContext(gc.get());
#endif

    // 设置相机参数
    g_appContext->viewer->getCamera()->setViewport(new osg::Viewport(0, 0, width, height));
    g_appContext->viewer->getCamera()->setProjectionMatrixAsPerspective(30.0, (double)width / height, 1.0, 1000000000.0);

    // 添加操作器
    g_appContext->viewer->setCameraManipulator(new osgGA::TrackballManipulator());

    // 添加事件处理器
    g_appContext->viewer->addEventHandler(new osgGA::StateSetManipulator(g_appContext->viewer->getCamera()->getOrCreateStateSet()));
    g_appContext->viewer->addEventHandler(new osgViewer::StatsHandler());
    g_appContext->viewer->addEventHandler(new osgViewer::WindowSizeHandler());

    DEBUG_LOG("OSG initialized successfully");
    return true;
}

/**
 * 主循环 - 确保在主线程中执行
 */
void mainLoop()
{
#ifdef EMSCRIPTEN
    // 检查是否在主线程中
    if (!emscripten_is_main_browser_thread())
    {
        ERROR_LOG("⚠️ 主循环不在主浏览器线程中执行");
        return;
    }
#endif

    // 处理SDL事件
    SDL_Event event;
    while (SDL_PollEvent(&event))
    {
        if (event.type == SDL_QUIT)
        {
            g_appContext->shouldExit = true;
        }
    }

    // 渲染OSG场景 - 确保在主线程中
    if (g_appContext->viewer.valid())
    {
        try
        {
            g_appContext->viewer->frame();
        }
        catch (const std::exception &e)
        {
            ERROR_LOG("❌ 渲染异常: " << e.what());
        }
    }

#ifndef EMSCRIPTEN
    // 桌面版本需要交换缓冲区
    SDL_GL_SwapWindow(g_appContext->window);
#endif
}

/**
 * 主函数
 */
int main(int argc, char *argv[])
{
#ifndef EMSCRIPTEN
    // 对于桌面版本，分配控制台用于调试输出
    if (AllocConsole())
    {
        freopen_s((FILE **)stdout, "CONOUT$", "w", stdout);
        freopen_s((FILE **)stderr, "CONOUT$", "w", stderr);
        freopen_s((FILE **)stdin, "CONIN$", "r", stdin);
        SetConsoleTitle("osgEarth SDL Fixed Version - Debug Output");
    }
#endif

    std::cout << "========================================" << std::endl;
    std::cout << "osgEarth WebAssembly - SDL 修复版本" << std::endl;
    std::cout << "========================================" << std::endl;

    // 创建应用上下文
    g_appContext = new AppContext();

    // 0. 首先初始化 osgEarth（在创建任何图形窗口之前）
    INFO_LOG("初始化 osgEarth...");
    osgEarth::initialize();
    INFO_LOG("✅ osgEarth 初始化完成");

    // 1. 初始化SDL
    if (!initializeSDL())
    {
        delete g_appContext;
        return -1;
    }

    // 2. 初始化OSG
    if (!initializeOSG())
    {
        delete g_appContext;
        return -1;
    }

    // 3. 检查插件状态
    checkOsgEarthPlugins();

    // 4. 测试 osgEarth 基础功能
    testOsgEarthBasics();

    // 5. 创建 osgEarth 地球场景
    g_appContext->rootNode = new osg::Group();
    INFO_LOG("尝试创建 osgEarth 地球场景...");
    osg::ref_ptr<osg::Node> sceneNode = createOsgEarthScene();

    if (sceneNode.valid())
    {
        INFO_LOG("✅ 使用 osgEarth 地球场景");
        g_appContext->rootNode->addChild(sceneNode.get());
    }
    else
    {
        ERROR_LOG("❌ osgEarth 场景创建失败，使用简单测试场景作为后备");
        osg::ref_ptr<osg::Node> fallbackScene = createSimpleTestScene();
        if (fallbackScene.valid())
        {
            g_appContext->rootNode->addChild(fallbackScene.get());
        }
    }

    // 6. 设置场景
    g_appContext->viewer->setSceneData(g_appContext->rootNode.get());

    INFO_LOG("Starting main loop...");

    // 7. 启动主循环（使用 requestAnimationFrame）
#ifdef EMSCRIPTEN
    emscripten_set_main_loop(mainLoop, 0, 1); // 使用 0 启用 requestAnimationFrame
#else
    while (!g_appContext->shouldExit)
    {
        mainLoop();
        std::this_thread::sleep_for(std::chrono::milliseconds(16)); // ~60 FPS
    }
#endif

    // 清理资源
    INFO_LOG("Cleaning up...");

    if (g_appContext->context)
    {
        SDL_GL_DeleteContext(g_appContext->context);
    }

    if (g_appContext->window)
    {
        SDL_DestroyWindow(g_appContext->window);
    }

    delete g_appContext;
    SDL_Quit();

    return 0;
}
