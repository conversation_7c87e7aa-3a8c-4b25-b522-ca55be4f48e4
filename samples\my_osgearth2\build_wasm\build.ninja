# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.26

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: osgearth_myviewer_wasm
# Configurations: Release
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Release
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/build_wasm/
# =============================================================================
# Object build statements for EXECUTABLE target osgearth_myviewer_wasm


#############################################
# Order-only phony target for osgearth_myviewer_wasm

build cmake_object_order_depends_target_osgearth_myviewer_wasm: phony || CMakeFiles/osgearth_myviewer_wasm.dir

build CMakeFiles/osgearth_myviewer_wasm.dir/osgearth_myviewer_wasm.cpp.o: CXX_COMPILER__osgearth_myviewer_wasm_unscanned_Release F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/osgearth_myviewer_wasm.cpp || cmake_object_order_depends_target_osgearth_myviewer_wasm
  DEP_FILE = CMakeFiles\osgearth_myviewer_wasm.dir\osgearth_myviewer_wasm.cpp.o.d
  FLAGS = -std=c++20 -s USE_PTHREADS=1 -s PTHREAD_POOL_SIZE=4 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=2GB -s STACK_SIZE=8MB -s DISABLE_EXCEPTION_CATCHING=0 -s ASSERTIONS=1 -s SAFE_HEAP=1 -s STACK_OVERFLOW_CHECK=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s OFFSCREENCANVAS_SUPPORT=0 -s FETCH=1 -s WASM=1 -s MODULARIZE=0 -s ENVIRONMENT='web,worker' -s SHARED_MEMORY=1 -s EXPORTED_FUNCTIONS=['_main','_getOSGEarthStatus','_getMapInfo','_isRunning','_forceUpdate'] -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap'] -O3 -DNDEBUG -std=gnu++20
  INCLUDES = -IF:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../../../osgearth_third_party/wasm_dep/include -IF:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../../../osgearth_third_party/wasm_dep/include/osg -IF:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../src
  OBJECT_DIR = CMakeFiles\osgearth_myviewer_wasm.dir
  OBJECT_FILE_DIR = CMakeFiles\osgearth_myviewer_wasm.dir


# =============================================================================
# Link build statements for EXECUTABLE target osgearth_myviewer_wasm


#############################################
# Link the executable osgearth_myviewer_wasm.js

build osgearth_myviewer_wasm.js: CXX_EXECUTABLE_LINKER__osgearth_myviewer_wasm_Release CMakeFiles/osgearth_myviewer_wasm.dir/osgearth_myviewer_wasm.cpp.o | F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../redist_wasm/libosgEarth.a F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../redist_wasm/osgdb_earth.a F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../redist_wasm/osgdb_osgearth_engine_rex.a F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../redist_wasm/osgdb_osgearth_sky_simple.a F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../redist_wasm/osgdb_osgearth_sky_gl.a F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../redist_wasm/osgdb_osgearth_cache_filesystem.a F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../redist_wasm/osgdb_osgearth_terrainshader.a F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../redist_wasm/osgdb_osgearth_viewpoints.a F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../../../osgearth_third_party/wasm_dep/lib/libosg.a F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../../../osgearth_third_party/wasm_dep/lib/libosgViewer.a F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../../../osgearth_third_party/wasm_dep/lib/libosgGA.a F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../../../osgearth_third_party/wasm_dep/lib/libosgDB.a F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../../../osgearth_third_party/wasm_dep/lib/libosgUtil.a F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../../../osgearth_third_party/wasm_dep/lib/libOpenThreads.a F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../../../osgearth_third_party/wasm_dep/lib/libosgText.a F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../../../osgearth_third_party/wasm_dep/lib/libosgSim.a F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../../../osgearth_third_party/wasm_dep/lib/libosgShadow.a F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../../../osgearth_third_party/wasm_dep/lib/libGeographicLib.a
  FLAGS = -std=c++20 -s USE_PTHREADS=1 -s PTHREAD_POOL_SIZE=4 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=2GB -s STACK_SIZE=8MB -s DISABLE_EXCEPTION_CATCHING=0 -s ASSERTIONS=1 -s SAFE_HEAP=1 -s STACK_OVERFLOW_CHECK=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s OFFSCREENCANVAS_SUPPORT=0 -s FETCH=1 -s WASM=1 -s MODULARIZE=0 -s ENVIRONMENT='web,worker' -s SHARED_MEMORY=1 -s EXPORTED_FUNCTIONS=['_main','_getOSGEarthStatus','_getMapInfo','_isRunning','_forceUpdate'] -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap'] -O3 -DNDEBUG
  LINK_FLAGS = -s USE_PTHREADS=1 -s PTHREAD_POOL_SIZE=4 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=2GB -s STACK_SIZE=8MB -s DISABLE_EXCEPTION_CATCHING=0 -s ASSERTIONS=1 -s SAFE_HEAP=1 -s STACK_OVERFLOW_CHECK=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s OFFSCREENCANVAS_SUPPORT=0 -s FETCH=1 -s WASM=1 -s MODULARIZE=0 -s ENVIRONMENT='web,worker' -s SHARED_MEMORY=1 -s EXPORTED_FUNCTIONS=['_main','_getOSGEarthStatus','_getMapInfo','_isRunning','_forceUpdate'] -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap']
  LINK_LIBRARIES = F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../redist_wasm/libosgEarth.a  F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../redist_wasm/osgdb_earth.a  F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../redist_wasm/osgdb_osgearth_engine_rex.a  F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../redist_wasm/osgdb_osgearth_sky_simple.a  F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../redist_wasm/osgdb_osgearth_sky_gl.a  F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../redist_wasm/osgdb_osgearth_cache_filesystem.a  F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../redist_wasm/osgdb_osgearth_terrainshader.a  F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../redist_wasm/osgdb_osgearth_viewpoints.a  F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../../../osgearth_third_party/wasm_dep/lib/libosg.a  F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../../../osgearth_third_party/wasm_dep/lib/libosgViewer.a  F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../../../osgearth_third_party/wasm_dep/lib/libosgGA.a  F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../../../osgearth_third_party/wasm_dep/lib/libosgDB.a  F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../../../osgearth_third_party/wasm_dep/lib/libosgUtil.a  F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../../../osgearth_third_party/wasm_dep/lib/libOpenThreads.a  F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../../../osgearth_third_party/wasm_dep/lib/libosgText.a  F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../../../osgearth_third_party/wasm_dep/lib/libosgSim.a  F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../../../osgearth_third_party/wasm_dep/lib/libosgShadow.a  F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/../../../../osgearth_third_party/wasm_dep/lib/libGeographicLib.a
  OBJECT_DIR = CMakeFiles\osgearth_myviewer_wasm.dir
  POST_BUILD = cmd.exe /C "cd /D F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\samples\my_osgearth2\build_wasm && "C:\Program Files\CMake\bin\cmake.exe" -E make_directory F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/redist_wasm && "C:\Program Files\CMake\bin\cmake.exe" -E copy F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/build_wasm/osgearth_myviewer_wasm.js F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/redist_wasm/ && "C:\Program Files\CMake\bin\cmake.exe" -E copy F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/build_wasm/osgearth_myviewer_wasm.wasm F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/redist_wasm/"
  PRE_LINK = cd .
  TARGET_FILE = osgearth_myviewer_wasm.js
  TARGET_PDB = osgearth_myviewer_wasm.js.dbg
  RSP_FILE = CMakeFiles\osgearth_myviewer_wasm.rsp


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\samples\my_osgearth2\build_wasm && "C:\Program Files\CMake\bin\cmake-gui.exe" -SF:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\samples\my_osgearth2 -BF:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\samples\my_osgearth2\build_wasm"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\samples\my_osgearth2\build_wasm && "C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -SF:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\samples\my_osgearth2 -BF:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\samples\my_osgearth2\build_wasm"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build osgearth_myviewer_wasm: phony osgearth_myviewer_wasm.js

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/build_wasm

build all: phony osgearth_myviewer_wasm.js

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCXXInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCommonLanguageInclude.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeGenericSystem.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeInitializeConfigs.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeLanguageInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInitialize.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-C.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-CXX.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/GNU.cmake C$:/dev/emsdk/upstream/emscripten/cmake/Modules/Platform/Emscripten.cmake CMakeCache.txt CMakeFiles/3.26.4/CMakeCCompiler.cmake CMakeFiles/3.26.4/CMakeCXXCompiler.cmake CMakeFiles/3.26.4/CMakeSystem.cmake F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/CMakeLists.txt
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCXXInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCommonLanguageInclude.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeGenericSystem.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeInitializeConfigs.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeLanguageInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInitialize.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-C.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-CXX.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/GNU.cmake C$:/dev/emsdk/upstream/emscripten/cmake/Modules/Platform/Emscripten.cmake CMakeCache.txt CMakeFiles/3.26.4/CMakeCCompiler.cmake CMakeFiles/3.26.4/CMakeCXXCompiler.cmake CMakeFiles/3.26.4/CMakeSystem.cmake F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/samples/my_osgearth2/CMakeLists.txt: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
