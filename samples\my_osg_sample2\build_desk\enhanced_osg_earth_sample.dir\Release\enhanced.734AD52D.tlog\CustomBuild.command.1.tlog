^C:\GEMINICLI\MY_OSG_SAMPLE\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/GeminiCLI/my_osg_sample -BC:/GeminiCLI/my_osg_sample/build_desk --check-stamp-file C:/GeminiCLI/my_osg_sample/build_desk/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
