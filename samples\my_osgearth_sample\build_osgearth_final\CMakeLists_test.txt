# osgEarth WebAssembly 最小化测试 - 分段排查问题
cmake_minimum_required(VERSION 3.10)
project(osgearth_minimal_test)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

if(EMSCRIPTEN)
    include_directories(SYSTEM ${CMAKE_JS_INC_PATH})
endif()

# OSG 库路径
set(OSG_WASM_LIB_DIR "F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep")

# 包含头文件目录
include_directories(
    "${OSG_WASM_LIB_DIR}/include"
)

# 源文件
set(SOURCES
    osgearth_minimal_test.cpp
)

# 创建可执行文件
add_executable(osgearth_minimal_test ${SOURCES})

if(EMSCRIPTEN)
    # 只使用基本的 OSG 库，不包含 osgEarth
    set(OSG_LIBRARIES
        "${OSG_WASM_LIB_DIR}/lib/libosg.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgViewer.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgDB.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgGA.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgUtil.a"
        "${OSG_WASM_LIB_DIR}/lib/libOpenThreads.a"
    )
    
    # 基本第三方库
    set(THIRD_PARTY_LIBRARIES
        "${OSG_WASM_LIB_DIR}/lib/libz.a"
        "${OSG_WASM_LIB_DIR}/lib/libexpat.a"
    )
    
    # 链接库
    target_link_libraries(osgearth_minimal_test
        ${OSG_LIBRARIES}
        ${THIRD_PARTY_LIBRARIES}
    )
    
    # 编译器标志 - 最简配置
    set(EM_COMPILE_FLAGS
        "-DOSG_GLSL_VERSION=300"
        "-O3"
    )

    # 链接器标志 - 最简配置，强制单线程
    set(EM_LINK_FLAGS_LIST
        "-s USE_SDL=2"
        "-s USE_WEBGL2=1"
        "-s FULL_ES3=1"
        "-s WASM=1"
        "-s INITIAL_MEMORY=134217728"  # 128MB
        "-s ALLOW_MEMORY_GROWTH=1"
        "-s MAXIMUM_MEMORY=536870912"  # 512MB
        "-s ASSERTIONS=1"
        "-s SAFE_HEAP=0"
        "-s STACK_OVERFLOW_CHECK=1"
        "-s EXPORTED_FUNCTIONS=['_main']"
        "-s EXPORTED_RUNTIME_METHODS=['ccall','cwrap']"
        "-s NO_EXIT_RUNTIME=1"
        "-s DISABLE_EXCEPTION_CATCHING=0"
        "-s USE_PTHREADS=0"
        "-s PTHREAD_POOL_SIZE=0"
        "-s PROXY_TO_PTHREAD=0"
        "-s OFFSCREENCANVAS_SUPPORT=0"
        "-O3"
    )
    string(REPLACE ";" " " EM_LINK_FLAGS "${EM_LINK_FLAGS_LIST}")

    # 设置编译器标志
    target_compile_options(osgearth_minimal_test PRIVATE ${EM_COMPILE_FLAGS})
    
    # 设置链接器属性
    set_target_properties(osgearth_minimal_test PROPERTIES
        SUFFIX ".html"
        LINK_FLAGS "${EM_LINK_FLAGS}"
    )

    message(STATUS "osgEarth Minimal Test configured (OSG only, no osgEarth)")
    message(STATUS "Output will be: osgearth_minimal_test.html, osgearth_minimal_test.js, osgearth_minimal_test.wasm")
    
else()
    # 桌面版本配置
    find_package(OpenSceneGraph REQUIRED)
    
    target_link_libraries(osgearth_minimal_test
        ${OPENSCENEGRAPH_LIBRARIES}
    )
endif()
