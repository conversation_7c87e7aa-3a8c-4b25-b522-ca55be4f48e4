@echo off
chcp 65001
echo ========================================
echo OSGEarth My_OSGEarth2 WebAssembly Build
echo Using compiled OSGEarth libraries
echo ========================================

echo Setting up Emscripten environment...
call C:\dev\emsdk\emsdk_env.bat

echo Checking emcc availability...
C:\dev\emsdk\python\3.13.3_64bit\python.exe C:\dev\emsdk\upstream\emscripten\emcc.py --version
if %ERRORLEVEL% neq 0 (
    echo Error: emcc not found
    pause
    exit /b 1
)

echo Setting dependency paths...
set THIRD_PARTY_DIR=E:\project\my-earth202507\third_party\wasm_dep
set OSGEARTH_LIB_DIR=..\..\redist_wasm
set OUTPUT_DIR=redist_wasm

echo Third Party Dir: %THIRD_PARTY_DIR%
echo OSGEarth Lib Dir: %OSGEARTH_LIB_DIR%
echo Output Dir: %OUTPUT_DIR%

echo Verifying OSGEarth libraries...
if not exist "%OSGEARTH_LIB_DIR%\libosgEarth.a" (
    echo Error: OSGEarth library not found at %OSGEARTH_LIB_DIR%\libosgEarth.a
    echo Please compile the main OSGEarth project first
    pause
    exit /b 1
)

echo Verifying third-party dependencies...
if not exist "%THIRD_PARTY_DIR%\lib\libosg.a" (
    echo Error: OSG library not found at %THIRD_PARTY_DIR%\lib\libosg.a
    pause
    exit /b 1
)

echo All dependencies verified!

echo Preparing build directories...
if exist "build_wasm" (
    rmdir /s /q build_wasm
    echo Removed existing build directory
)
mkdir build_wasm

if exist "%OUTPUT_DIR%" (
    rmdir /s /q %OUTPUT_DIR%
    echo Removed existing output directory
)
mkdir %OUTPUT_DIR%

cd build_wasm

echo Running CMake configuration...
C:\dev\emsdk\python\3.13.3_64bit\python.exe C:\dev\emsdk\upstream\emscripten\emcmake.py cmake .. ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DOSGEARTH_WASM_LIB_DIR=%OSGEARTH_LIB_DIR% ^
    -DTHIRD_PARTY_WASM_DIR=%THIRD_PARTY_DIR% ^
    -G "MinGW Makefiles"

if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed
    cd ..
    pause
    exit /b 1
)

echo CMake configuration successful!

echo Starting compilation...
C:\dev\emsdk\python\3.13.3_64bit\python.exe C:\dev\emsdk\upstream\emscripten\emmake.py make -j4

if %ERRORLEVEL% neq 0 (
    echo Compilation failed
    cd ..
    pause
    exit /b 1
)

echo Compilation successful!

cd ..

echo Checking generated files...
if exist "%OUTPUT_DIR%\osgearth_myviewer_wasm.wasm" (
    echo ✓ WebAssembly file generated successfully
) else (
    echo ✗ WebAssembly file not found
)

if exist "%OUTPUT_DIR%\osgearth_myviewer_wasm.js" (
    echo ✓ JavaScript loader generated successfully
) else (
    echo ✗ JavaScript loader not found
)

echo.
echo File sizes:
dir %OUTPUT_DIR%\osgearth_myviewer_wasm.*

echo.
echo ========================================
echo OSGEarth My_OSGEarth2 build completed!
echo Build directory: build_wasm
echo Output directory: %OUTPUT_DIR%
echo ========================================

echo.
echo To test the application:
echo 1. Copy the HTML file to %OUTPUT_DIR%
echo 2. Start a local web server in %OUTPUT_DIR%
echo 3. Open the HTML file in a modern browser

pause
