/* osgEarth
* Copyright 2008-2012 Pelican Mapping
* MIT License
*/
#ifndef OSGEARTH_TEXTURE_BUFFER_H
#define OSGEARTH_TEXTURE_BUFFER_H

#include <osgEarth/Common>
#include <osg/TextureBuffer>

namespace osgEarth
{
    /**
    * Specialized TextureBuffer that properly supports serialization
    */
    class OSGEARTH_EXPORT TextureBuffer : public osg::TextureBuffer
    {
    public:
        TextureBuffer();
        TextureBuffer(const osgEarth::TextureBuffer& rhs, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY);
        META_Object(osgEarth, TextureBuffer);
    };
} // namespace osgEarth

#endif // OSGEARTH_TEXTURE_BUFFER_H
