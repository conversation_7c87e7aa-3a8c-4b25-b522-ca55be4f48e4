^C:\GEMINICLI\MY_OSG_SAMPLE\BUILD_DESK\CMAKEFILES\53EB661242EF7F62934028EFAD86666B\DEPLOY_DESK.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory C:/GeminiCLI/my_osg_sample/../../../redist_desk/bin
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/GeminiCLI/my_osg_sample/build_desk/Release/enhanced_osg_earth_sample.exe C:/GeminiCLI/my_osg_sample/../../../redist_desk/bin/
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\GEMINICLI\MY_OSG_SAMPLE\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/GeminiCLI/my_osg_sample -BC:/GeminiCLI/my_osg_sample/build_desk --check-stamp-file C:/GeminiCLI/my_osg_sample/build_desk/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
