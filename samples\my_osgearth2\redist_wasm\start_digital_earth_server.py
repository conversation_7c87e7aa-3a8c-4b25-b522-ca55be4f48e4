#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OSGEarth Digital Earth WebAssembly Demo Server
支持多线程WebAssembly和WebGL的HTTP服务器
"""

import http.server
import socketserver
import os
import sys
import webbrowser
import threading
import time

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # 添加必要的CORS和WebAssembly多线程支持头
        self.send_header('Cross-Origin-Embedder-Policy', 'require-corp')
        self.send_header('Cross-Origin-Opener-Policy', 'same-origin')
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()

    def guess_type(self, path):
        result = super().guess_type(path)
        if isinstance(result, tuple):
            mimetype, encoding = result
        else:
            mimetype = result
            encoding = None
        
        # 设置WebAssembly MIME类型
        if path.endswith('.wasm'):
            return 'application/wasm'
        elif path.endswith('.js'):
            return 'application/javascript'
        elif path.endswith('.html'):
            return 'text/html'
        
        return mimetype

    def log_message(self, format, *args):
        # 自定义日志格式
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {format % args}")

def start_server(port=8080, directory=None):
    """启动HTTP服务器"""
    if directory:
        os.chdir(directory)
    
    print("=" * 60)
    print("🌍 OSGEarth Digital Earth WebAssembly Demo Server")
    print("=" * 60)
    print(f"📁 服务目录: {os.getcwd()}")
    print(f"🌐 服务端口: {port}")
    print(f"🔗 访问地址: http://localhost:{port}")
    print(f"🎯 演示页面: http://localhost:{port}/digital_earth_demo.html")
    print("=" * 60)
    
    # 检查必要文件
    required_files = [
        'osgearth_digital_earth_webgl.wasm',
        'osgearth_digital_earth_webgl.js',
        'digital_earth_demo.html'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        print("\n请确保已编译WebAssembly文件并创建了HTML页面")
        return False
    
    print("✅ 所有必要文件检查完成")
    
    # 显示文件信息
    print("\n📋 文件信息:")
    for file in required_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            size_mb = size / (1024 * 1024)
            print(f"   📄 {file}: {size_mb:.2f} MB")
    
    try:
        # 创建服务器
        with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
            print(f"\n🚀 服务器启动成功，监听端口 {port}")
            print("💡 提示:")
            print("   - 使用现代浏览器访问 (Chrome 79+, Firefox 72+, Safari 14+)")
            print("   - 确保浏览器支持WebAssembly多线程")
            print("   - 按 Ctrl+C 停止服务器")
            print("\n📊 访问日志:")
            print("-" * 60)
            
            # 自动打开浏览器
            def open_browser():
                time.sleep(1)  # 等待服务器启动
                webbrowser.open(f'http://localhost:{port}/digital_earth_demo.html')
            
            browser_thread = threading.Thread(target=open_browser)
            browser_thread.daemon = True
            browser_thread.start()
            
            # 启动服务器
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n\n🛑 服务器已停止")
        return True
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 端口 {port} 已被占用，请尝试其他端口")
            return False
        else:
            print(f"❌ 启动服务器失败: {e}")
            return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description='OSGEarth Digital Earth WebAssembly Demo Server',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python start_digital_earth_server.py                    # 使用默认端口8080
  python start_digital_earth_server.py -p 8000           # 使用端口8000
  python start_digital_earth_server.py -d /path/to/files  # 指定文件目录
        """
    )
    
    parser.add_argument('-p', '--port', type=int, default=8080,
                        help='HTTP服务器端口 (默认: 8080)')
    parser.add_argument('-d', '--directory', type=str,
                        help='WebAssembly文件所在目录 (默认: 当前目录)')
    
    args = parser.parse_args()
    
    # 启动服务器
    success = start_server(args.port, args.directory)
    
    if not success:
        sys.exit(1)

if __name__ == '__main__':
    main()
