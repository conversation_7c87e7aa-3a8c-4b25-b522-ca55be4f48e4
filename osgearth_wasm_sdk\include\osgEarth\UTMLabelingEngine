/* osgEarth
* Copyright 2025 Pelican Mapping
* MIT License
*/
#ifndef OSGEARTH_UTIL_UTM_LABELING_ENGINE_H
#define OSGEARTH_UTIL_UTM_LABELING_ENGINE_H 1

#include <osgEarth/Common>
#include <osgEarth/GraticuleLabelingEngine>
#include <osgEarth/GeoData>
#include <osgEarth/MapNode>
#include <osgEarth/Containers>
#include <osgEarth/LabelNode>

namespace osgEarth { namespace Util
{
    using namespace osgEarth;

    /**
     * Node that plots UTM coordinats labels along the edge of the
     * viewport when you are looking straight down on a zoomed-in area.
     */
    class UTMLabelingEngine : public GraticuleLabelingEngine
    {
    public:
        //! Construct a new labeling engine with the map's SRS
        UTMLabelingEngine(const SpatialReference* srs);

        //! Sets the maximum resolution (meters) at which to render labels
        void setMaxResolution(double value);

        virtual bool updateLabels(const osg::Vec3d& LL_world, osg::Vec3d& UL_world, osg::Vec3d& LR_world, ClipSpace& window, CameraData& data);

    protected:

        double _maxRes;
    };

} } // namespace osgEarth::Util

#endif // OSGEARTH_UTIL_UTM_LABELING_ENGINE_H
