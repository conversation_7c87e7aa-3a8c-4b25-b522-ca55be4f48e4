<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OSGEarth WebAssembly Multithreaded Viewer</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2em;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .canvas-container {
            position: relative;
            background-color: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 600px;
        }
        
        #canvas {
            border: 2px solid #ddd;
            background-color: #1a1a2e;
            display: block;
        }
        
        .controls {
            padding: 20px;
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }
        
        .status-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .status-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .status-item h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 1.1em;
        }
        
        .status-item p {
            margin: 0;
            color: #666;
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            background-color: #007bff;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #c3e6cb;
        }
        
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌍 OSGEarth WebAssembly Viewer</h1>
            <p>多线程WebAssembly版本 - 支持pthread和共享内存</p>
        </div>
        
        <div class="canvas-container">
            <canvas id="canvas" width="800" height="600"></canvas>
        </div>
        
        <div class="controls">
            <div class="status-panel">
                <div class="status-item">
                    <h3>🔧 系统状态</h3>
                    <p id="system-status">正在初始化...</p>
                </div>
                <div class="status-item">
                    <h3>🗺️ 地图信息</h3>
                    <p id="map-info">等待地图加载...</p>
                </div>
                <div class="status-item">
                    <h3>⚡ 运行状态</h3>
                    <p id="running-status">检查中...</p>
                </div>
                <div class="status-item">
                    <h3>🧵 多线程状态</h3>
                    <p id="thread-status">多线程支持：启用</p>
                </div>
            </div>
            
            <div class="button-group">
                <button onclick="updateStatus()">🔄 更新状态</button>
                <button onclick="forceUpdate()">⚡ 强制更新</button>
                <button onclick="toggleFullscreen()">🖥️ 全屏</button>
                <button onclick="resetView()">🎯 重置视图</button>
            </div>
            
            <div id="messages"></div>
        </div>
    </div>

    <script>
        let Module = {};
        let isInitialized = false;
        
        // 消息显示函数
        function showMessage(message, type = 'info') {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = type;
            messageDiv.textContent = message;
            messagesDiv.appendChild(messageDiv);
            
            // 5秒后自动移除消息
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 5000);
        }
        
        // 更新状态信息
        function updateStatus() {
            if (!isInitialized) {
                showMessage('WebAssembly模块尚未初始化', 'error');
                return;
            }
            
            try {
                // 获取系统状态
                const systemStatus = Module.ccall('getOSGEarthStatus', 'string', [], []);
                document.getElementById('system-status').textContent = systemStatus;
                
                // 获取地图信息
                const mapInfo = Module.ccall('getMapInfo', 'string', [], []);
                document.getElementById('map-info').textContent = mapInfo;
                
                // 获取运行状态
                const isRunning = Module.ccall('isRunning', 'boolean', [], []);
                document.getElementById('running-status').textContent = isRunning ? '✅ 正在运行' : '❌ 已停止';
                
                showMessage('状态更新成功', 'success');
            } catch (error) {
                showMessage('状态更新失败: ' + error.message, 'error');
            }
        }
        
        // 强制更新
        function forceUpdate() {
            if (!isInitialized) {
                showMessage('WebAssembly模块尚未初始化', 'error');
                return;
            }
            
            try {
                Module.ccall('forceUpdate', null, [], []);
                showMessage('强制更新已执行', 'success');
            } catch (error) {
                showMessage('强制更新失败: ' + error.message, 'error');
            }
        }
        
        // 全屏切换
        function toggleFullscreen() {
            const canvas = document.getElementById('canvas');
            if (!document.fullscreenElement) {
                canvas.requestFullscreen().catch(err => {
                    showMessage('无法进入全屏模式: ' + err.message, 'error');
                });
            } else {
                document.exitFullscreen();
            }
        }
        
        // 重置视图
        function resetView() {
            showMessage('视图重置功能待实现', 'info');
        }
        
        // WebAssembly模块配置
        Module = {
            canvas: document.getElementById('canvas'),
            
            onRuntimeInitialized: function() {
                console.log('OSGEarth WebAssembly模块初始化完成');
                isInitialized = true;
                showMessage('OSGEarth WebAssembly模块初始化成功！', 'success');
                
                // 开始定期更新状态
                setInterval(updateStatus, 2000);
            },
            
            print: function(text) {
                console.log('OSGEarth:', text);
            },
            
            printErr: function(text) {
                console.error('OSGEarth Error:', text);
                showMessage('OSGEarth错误: ' + text, 'error');
            },
            
            setStatus: function(text) {
                if (text) {
                    console.log('Status:', text);
                    document.getElementById('system-status').textContent = text;
                }
            }
        };
        
        // 初始化消息
        showMessage('正在加载OSGEarth WebAssembly模块...', 'info');
    </script>
    
    <!-- 加载WebAssembly模块 -->
    <script src="osgearth_myviewer_wasm.js"></script>
</body>
</html>
