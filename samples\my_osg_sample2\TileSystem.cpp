#include "TileSystem.hpp"
#include <osg/Image>
#include <osg/Texture2D>
#include <osg/Notify>
#include <osgDB/ReadFile>
#include <sstream>
#include <iomanip>
#include <cmath>
#include <algorithm>
#include <chrono>

// Windows/MSVC兼容性修复
#ifdef _WIN32
#define _USE_MATH_DEFINES
#include <math.h>
#endif
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

#ifdef EMSCRIPTEN
#include <emscripten/fetch.h>
#include <emscripten.h>
#endif

// STB Image for decoding
#ifndef STB_IMAGE_IMPLEMENTATION
#define STB_IMAGE_IMPLEMENTATION
#endif
#ifndef STBI_NO_STDIO
#define STBI_NO_STDIO
#endif
#ifndef STBI_NO_FAILURE_STRINGS
#define STBI_NO_FAILURE_STRINGS
#endif
#ifndef STBI_ONLY_PNG
#define STBI_ONLY_PNG
#endif
#ifndef STBI_ONLY_JPEG
#define STBI_ONLY_JPEG
#endif
#include "stb_image.h"

using namespace EarthSample;

// ======== TileKey Implementation ========

TileKey::TileKey() : _level(0), _x(0), _y(0), _extentCalculated(false)
{
}

TileKey::TileKey(int level, int x, int y) : _level(level), _x(x), _y(y), _extentCalculated(false)
{
}

GeoExtent TileKey::getExtent() const
{
  if (!_extentCalculated)
  {
    const_cast<TileKey *>(this)->calculateExtent();
  }
  return _extent;
}

void TileKey::calculateExtent()
{
  // Web Mercator瓦片系统
  int numTiles = 1 << _level; // 2^level

  double tileWidth = 360.0 / numTiles;
  double tileHeight = 170.1022 / numTiles; // Web Mercator纬度范围

  double xMin = -180.0 + _x * tileWidth;
  double xMax = xMin + tileWidth;
  double yMax = 85.0511 - _y * tileHeight;
  double yMin = yMax - tileHeight;

  // 限制在有效范围内
  xMin = std::max(-180.0, std::min(180.0, xMin));
  xMax = std::max(-180.0, std::min(180.0, xMax));
  yMin = std::max(-85.0511, std::min(85.0511, yMin));
  yMax = std::max(-85.0511, std::min(85.0511, yMax));

  _extent = GeoExtent(SpatialReference::createGeographic(), xMin, yMin, xMax, yMax);
  _extentCalculated = true;
}

std::vector<TileKey> TileKey::getSubKeys() const
{
  std::vector<TileKey> subKeys;
  subKeys.reserve(4);

  int nextLevel = _level + 1;
  int baseX = _x * 2;
  int baseY = _y * 2;

  subKeys.push_back(TileKey(nextLevel, baseX, baseY));
  subKeys.push_back(TileKey(nextLevel, baseX + 1, baseY));
  subKeys.push_back(TileKey(nextLevel, baseX, baseY + 1));
  subKeys.push_back(TileKey(nextLevel, baseX + 1, baseY + 1));

  return subKeys;
}

TileKey TileKey::getParentKey() const
{
  if (_level == 0)
  {
    return TileKey(); // 无效的父瓦片
  }

  return TileKey(_level - 1, _x / 2, _y / 2);
}

std::string TileKey::toString() const
{
  std::ostringstream oss;
  oss << _level << "/" << _x << "/" << _y;
  return oss.str();
}

bool TileKey::operator<(const TileKey &other) const
{
  if (_level != other._level)
    return _level < other._level;
  if (_x != other._x)
    return _x < other._x;
  return _y < other._y;
}

bool TileKey::operator==(const TileKey &other) const
{
  return _level == other._level && _x == other._x && _y == other._y;
}

bool TileKey::operator!=(const TileKey &other) const
{
  return !(*this == other);
}

TileKey TileKey::fromString(const std::string &str)
{
  std::istringstream iss(str);
  std::string token;
  std::vector<int> parts;

  while (std::getline(iss, token, '/'))
  {
    parts.push_back(std::stoi(token));
  }

  if (parts.size() == 3)
  {
    return TileKey(parts[0], parts[1], parts[2]);
  }

  return TileKey(); // 无效键
}

TileKey TileKey::fromLonLat(double lon, double lat, int level)
{
  // Web Mercator瓦片计算
  int numTiles = 1 << level;

  int x = static_cast<int>((lon + 180.0) / 360.0 * numTiles);
  int y = static_cast<int>((1.0 - std::log(std::tan(lat * M_PI / 180.0) + 1.0 / std::cos(lat * M_PI / 180.0)) / M_PI) / 2.0 * numTiles);

  // 限制范围
  x = std::max(0, std::min(numTiles - 1, x));
  y = std::max(0, std::min(numTiles - 1, y));

  return TileKey(level, x, y);
}

bool TileKey::valid() const
{
  if (_level < 0 || _level > 25)
    return false; // 合理的层级范围

  int maxTiles = 1 << _level;
  return _x >= 0 && _x < maxTiles && _y >= 0 && _y < maxTiles;
}

// ======== TileServiceConfig Implementation ========

TileServiceConfig::TileServiceConfig() : _type(TileServiceType::GOOGLE_SATELLITE),
                                         _format(TileFormat::JPEG),
                                         _minLevel(0),
                                         _maxLevel(18)
{
  initializePredefinedConfig();
}

TileServiceConfig::TileServiceConfig(TileServiceType type) : _type(type),
                                                             _format(TileFormat::JPEG),
                                                             _minLevel(0),
                                                             _maxLevel(18)
{
  initializePredefinedConfig();
}

void TileServiceConfig::initializePredefinedConfig()
{
  switch (_type)
  {
  case TileServiceType::GOOGLE_SATELLITE:
    _name = "Google Satellite";
    _description = "Google Maps satellite imagery";
    _urlTemplate = "https://mt{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}";
    _servers = {"0", "1", "2", "3"};
    _format = TileFormat::JPEG;
    _maxLevel = 20;
    break;

  case TileServiceType::GOOGLE_STREETS:
    _name = "Google Streets";
    _description = "Google Maps street map";
    _urlTemplate = "https://mt{s}.google.com/vt/lyrs=m&x={x}&y={y}&z={z}";
    _servers = {"0", "1", "2", "3"};
    _format = TileFormat::PNG;
    _maxLevel = 20;
    break;

  case TileServiceType::BING_AERIAL:
    _name = "Bing Aerial";
    _description = "Bing Maps aerial imagery";
    _urlTemplate = "https://t{s}.ssl.ak.tiles.virtualearth.net/tiles/a{q}.jpeg?g=1";
    _servers = {"0", "1", "2", "3"};
    _format = TileFormat::JPEG;
    _maxLevel = 19;
    break;

  case TileServiceType::OSM_MAPNIK:
    _name = "OpenStreetMap";
    _description = "OpenStreetMap standard tile layer";
    _urlTemplate = "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png";
    _servers = {"a", "b", "c"};
    _format = TileFormat::PNG;
    _maxLevel = 19;
    break;

  case TileServiceType::ESRI_WORLD_IMAGERY:
    _name = "ESRI World Imagery";
    _description = "ESRI World Imagery basemap";
    _urlTemplate = "https://services.arcgisonline.com/arcgis/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}";
    _format = TileFormat::JPEG;
    _maxLevel = 19;
    break;

  default:
    _name = "Custom";
    _description = "Custom tile service";
    break;
  }
}

std::string TileServiceConfig::getTileUrl(const TileKey &key) const
{
  std::string url = _urlTemplate;

  // 替换基本占位符
  url = replaceUrlTemplate(url, key);

  // 处理服务器选择
  if (!_servers.empty())
  {
    size_t serverIndex = (key.getX() + key.getY()) % _servers.size();
    std::string serverPlaceholder = "{s}";
    size_t pos = url.find(serverPlaceholder);
    if (pos != std::string::npos)
    {
      url.replace(pos, serverPlaceholder.length(), _servers[serverIndex]);
    }
  }

  return url;
}

std::string TileServiceConfig::replaceUrlTemplate(const std::string &urlTemplate, const TileKey &key) const
{
  std::string result = urlTemplate;

  // 替换{x}, {y}, {z}占位符
  auto replace = [&](const std::string &placeholder, const std::string &value)
  {
    size_t pos = 0;
    while ((pos = result.find(placeholder, pos)) != std::string::npos)
    {
      result.replace(pos, placeholder.length(), value);
      pos += value.length();
    }
  };

  replace("{z}", std::to_string(key.getLevel()));
  replace("{x}", std::to_string(key.getX()));
  replace("{y}", std::to_string(key.getY()));

  // 特殊处理Bing Maps的QuadKey
  if (_type == TileServiceType::BING_AERIAL)
  {
    replace("{q}", computeQuadKey(key));
  }

  return result;
}

std::string TileServiceConfig::computeQuadKey(const TileKey &key) const
{
  std::string quadKey;
  for (int i = key.getLevel(); i > 0; i--)
  {
    char digit = '0';
    int mask = 1 << (i - 1);
    if ((key.getX() & mask) != 0)
      digit++;
    if ((key.getY() & mask) != 0)
      digit += 2;
    quadKey.push_back(digit);
  }
  return quadKey;
}

TileServiceConfig *TileServiceConfig::createGoogleSatellite()
{
  return new TileServiceConfig(TileServiceType::GOOGLE_SATELLITE);
}

TileServiceConfig *TileServiceConfig::createGoogleStreets()
{
  return new TileServiceConfig(TileServiceType::GOOGLE_STREETS);
}

TileServiceConfig *TileServiceConfig::createBingAerial()
{
  return new TileServiceConfig(TileServiceType::BING_AERIAL);
}

TileServiceConfig *TileServiceConfig::createOSMMapnik()
{
  return new TileServiceConfig(TileServiceType::OSM_MAPNIK);
}

TileServiceConfig *TileServiceConfig::createESRIWorldImagery()
{
  return new TileServiceConfig(TileServiceType::ESRI_WORLD_IMAGERY);
}

TileServiceConfig *TileServiceConfig::createMapboxSatellite(const std::string &accessToken)
{
  TileServiceConfig *config = new TileServiceConfig(TileServiceType::MAPBOX_SATELLITE);
  config->_name = "Mapbox Satellite";
  config->_description = "Mapbox satellite imagery";
  config->_urlTemplate = "https://api.mapbox.com/v4/mapbox.satellite/{z}/{x}/{y}.png?access_token=" + accessToken;
  config->_format = TileFormat::PNG;
  config->_maxLevel = 22;
  return config;
}

// ======== TileData Implementation ========

TileData::TileData() : _timestamp(0.0)
{
}

TileData::TileData(const TileKey &key, const std::vector<unsigned char> &data) : _key(key), _data(data), _timestamp(0.0)
{
}

osg::Image *TileData::createOSGImage() const
{
    if (_data.empty())
    {
        OSG_WARN << "TileData::createOSGImage: No data to create image from." << std::endl;
        return nullptr;
    }

    int width, height, channels;
    // 使用stbi_load_from_memory解码内存中的图像数据
    unsigned char *imageData = stbi_load_from_memory(_data.data(), static_cast<int>(_data.size()), &width, &height, &channels, 0);

    if (!imageData)
    {
        OSG_WARN << "TileData::createOSGImage: Failed to decode image using STB Image. Reason: " << stbi_failure_reason() << std::endl;
        return nullptr;
    }

    // 根据通道数确定像素格式
    GLenum pixelFormat;
    switch (channels)
    {
    case 1:
        pixelFormat = GL_LUMINANCE;
        break;
    case 2:
        pixelFormat = GL_LUMINANCE_ALPHA;
        break;
    case 3:
        pixelFormat = GL_RGB;
        break;
    case 4:
        pixelFormat = GL_RGBA;
        break;
    default:
        OSG_WARN << "TileData::createOSGImage: Unsupported number of channels: " << channels << std::endl;
        stbi_image_free(imageData);
        return nullptr;
    }

    osg::ref_ptr<osg::Image> image = new osg::Image();
    // 将解码后的图像数据设置到osg::Image中
    // osg::Image::USE_MALLOC_FREE确保在osg::Image析构时，stbi_image_free会被调用以释放内存
    image->setImage(width, height, 1,
                    pixelFormat, // internalTextureFormat
                    pixelFormat, // pixelFormat
                    GL_UNSIGNED_BYTE, // dataType
                    imageData, // data
                    osg::Image::USE_MALLOC_FREE, // allocationMode
                    1); // packing

    return image.release();
}

osg::Texture2D *TileData::createOSGTexture() const
{
  osg::Image *image = createOSGImage();
  if (!image)
    return nullptr;

  osg::Texture2D *texture = new osg::Texture2D();
  texture->setImage(image);

  // 设置纹理参数
  texture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
  texture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
  texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR);
  texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);

  return texture;
}

// ======== MemoryTileCache Implementation ========

MemoryTileCache::MemoryTileCache(size_t maxSize) : _maxSize(maxSize), _currentSize(0)
{
}

MemoryTileCache::~MemoryTileCache()
{
  clear();
}

bool MemoryTileCache::getTile(const TileKey &key, TileData &tile)
{
  auto it = _cache.find(key);
  if (it != _cache.end())
  {
    tile = it->second.data;
    // 更新时间戳（用于LRU）
    it->second.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                               std::chrono::system_clock::now().time_since_epoch())
                               .count();
    return true;
  }
  return false;
}

void MemoryTileCache::putTile(const TileData &tile)
{
  if (!tile.valid())
    return;

  size_t tileSize = calculateTileSize(tile);

  // 检查缓存大小
  while (_currentSize + tileSize > _maxSize && !_cache.empty())
  {
    cleanupLRU();
  }

  // 添加新瓦片
  CacheEntry entry;
  entry.data = tile;
  entry.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                        std::chrono::system_clock::now().time_since_epoch())
                        .count();
  entry.size = tileSize;

  // 如果键已存在，先删除旧数据
  auto it = _cache.find(tile.getKey());
  if (it != _cache.end())
  {
    _currentSize -= it->second.size;
    _cache.erase(it);
  }

  _cache[tile.getKey()] = entry;
  _currentSize += tileSize;
}

bool MemoryTileCache::hasTile(const TileKey &key)
{
  return _cache.find(key) != _cache.end();
}

void MemoryTileCache::clear()
{
  _cache.clear();
  _currentSize = 0;
}

size_t MemoryTileCache::getSize()
{
  return _currentSize;
}

size_t MemoryTileCache::getCount()
{
  return _cache.size();
}

void MemoryTileCache::setMaxSize(size_t maxSize)
{
  _maxSize = maxSize;
  while (_currentSize > _maxSize && !_cache.empty())
  {
    cleanupLRU();
  }
}

void MemoryTileCache::cleanupLRU()
{
  if (_cache.empty())
    return;

  // 找到最老的条目
  auto oldest = _cache.begin();
  for (auto it = _cache.begin(); it != _cache.end(); ++it)
  {
    if (it->second.timestamp < oldest->second.timestamp)
    {
      oldest = it;
    }
  }

  // 删除最老的条目
  _currentSize -= oldest->second.size;
  _cache.erase(oldest);
}

size_t MemoryTileCache::calculateTileSize(const TileData &tile)
{
  return tile.getSize() + sizeof(TileKey) + sizeof(CacheEntry);
}

// ======== TileDownloader Implementation ========

TileDownloader::TileDownloader() : _maxConcurrentDownloads(4),
                                   _timeout(30.0),
                                   _proxyPort(0),
                                   _useProxy(false),
                                   _downloadedCount(0),
                                   _failedCount(0),
                                   _totalDownloadTime(0.0)
{
}

TileDownloader::~TileDownloader()
{
  cancelAllDownloads();
}

void TileDownloader::setServiceConfig(const TileServiceConfig &config)
{
  _config = config;
}

void TileDownloader::setTileCache(std::shared_ptr<TileCache> cache)
{
  _cache = cache;
}

void TileDownloader::setLoadCallback(std::shared_ptr<TileLoadCallback> callback)
{
  _callback = callback;
}

void TileDownloader::downloadTile(const TileKey &key)
{
  // 检查是否已在下载
  if (isDownloading(key))
  {
    return;
  }

  // 检查缓存
  if (_cache)
  {
    TileData cachedTile;
    if (_cache->getTile(key, cachedTile))
    {
      if (_callback)
      {
        _callback->onTileLoaded(cachedTile);
      }
      return;
    }
  }

  // 检查并发下载数量
  if (static_cast<int>(_activeDownloads.size()) >= _maxConcurrentDownloads)
  {
    // 可以实现队列机制，这里简单跳过
    return;
  }

  downloadTileImpl(key);
}

void TileDownloader::downloadTiles(const std::vector<TileKey> &keys)
{
  for (const auto &key : keys)
  {
    downloadTile(key);
  }
}

void TileDownloader::cancelDownload(const TileKey &key)
{
  auto it = _activeDownloads.find(key);
  if (it != _activeDownloads.end())
  {
#ifdef EMSCRIPTEN
    emscripten_fetch_t *fetch = static_cast<emscripten_fetch_t *>(it->second);
    emscripten_fetch_close(fetch);
#endif
    _activeDownloads.erase(it);
  }
}

void TileDownloader::cancelAllDownloads()
{
  for (auto &pair : _activeDownloads)
  {
#ifdef EMSCRIPTEN
    emscripten_fetch_t *fetch = static_cast<emscripten_fetch_t *>(pair.second);
    emscripten_fetch_close(fetch);
#endif
  }
  _activeDownloads.clear();
}

bool TileDownloader::isDownloading(const TileKey &key) const
{
  return _activeDownloads.find(key) != _activeDownloads.end();
}

size_t TileDownloader::getDownloadingCount() const
{
  return _activeDownloads.size();
}

void TileDownloader::setMaxConcurrentDownloads(int maxDownloads)
{
  _maxConcurrentDownloads = maxDownloads;
}

void TileDownloader::setTimeout(double timeoutSeconds)
{
  _timeout = timeoutSeconds;
}

void TileDownloader::setProxy(const std::string &host, int port)
{
  _proxyHost = host;
  _proxyPort = port;
  _useProxy = !host.empty() && port > 0;
}

void TileDownloader::setProxyAuth(const std::string &username, const std::string &password)
{
  _proxyUsername = username;
  _proxyPassword = password;
}

void TileDownloader::clearProxy()
{
  _proxyHost.clear();
  _proxyPort = 0;
  _proxyUsername.clear();
  _proxyPassword.clear();
  _useProxy = false;
}

void TileDownloader::downloadTileImpl(const TileKey &key)
{
#ifdef EMSCRIPTEN
  downloadTileEmscripten(key);
#else
  // 桌面版本可以使用curl或其他HTTP库
  // 这里简单起见，使用OSG的readImageFile
  std::string url = _config.getTileUrl(key);
  osg::ref_ptr<osg::Image> image = osgDB::readImageFile(url);

  if (image.valid())
  {
    // 将图像转换为瓦片数据
    std::vector<unsigned char> data;
    // 这里需要将OSG图像转换为字节数组
    // 简化实现，实际应该序列化图像数据

    TileData tile(key, data);
    handleDownloadComplete(key, data);
  }
  else
  {
    handleDownloadError(key, "Failed to load image");
  }
#endif
}

void TileDownloader::handleDownloadComplete(const TileKey &key, const std::vector<unsigned char> &data)
{
  // 移除活动下载
  _activeDownloads.erase(key);

  // 创建瓦片数据
  TileData tile(key, data);
  tile.setTimestamp(std::chrono::duration_cast<std::chrono::milliseconds>(
                        std::chrono::system_clock::now().time_since_epoch())
                        .count());

  // 添加到缓存
  if (_cache)
  {
    _cache->putTile(tile);
  }

  // 调用回调
  if (_callback)
  {
    _callback->onTileLoaded(tile);
  }

  _downloadedCount++;
}

void TileDownloader::handleDownloadError(const TileKey &key, const std::string &error)
{
  // 移除活动下载
  _activeDownloads.erase(key);

  // 调用错误回调
  if (_callback)
  {
    _callback->onTileLoadError(key, error);
  }

  _failedCount++;
}

#ifdef EMSCRIPTEN
void TileDownloader::downloadTileEmscripten(const TileKey &key)
{
  std::string url = _config.getTileUrl(key);

  emscripten_fetch_attr_t attr;
  emscripten_fetch_attr_init(&attr);
  strcpy(attr.requestMethod, "GET");
  attr.attributes = EMSCRIPTEN_FETCH_LOAD_TO_MEMORY;
  attr.timeoutMSecs = static_cast<int>(_timeout * 1000);

  // 设置回调
  attr.onsuccess = onEmscriptenDownloadSuccess;
  attr.onerror = onEmscriptenDownloadError;

  // 创建用户数据
  struct UserData
  {
    TileKey key;
    TileDownloader *downloader;
  };

  UserData *userData = new UserData{key, this};
  attr.userData = userData;

  // 开始下载
  emscripten_fetch_t *fetch = emscripten_fetch(&attr, url.c_str());
  if (fetch)
  {
    _activeDownloads[key] = fetch;
  }
  else
  {
    delete userData;
    handleDownloadError(key, "Failed to start download");
  }
}

void TileDownloader::onEmscriptenDownloadSuccess(emscripten_fetch_t *fetch)
{
  struct UserData
  {
    TileKey key;
    TileDownloader *downloader;
  };

  UserData *userData = static_cast<UserData *>(fetch->userData);
  if (userData && userData->downloader)
  {
    // 创建数据向量
    std::vector<unsigned char> data(fetch->data, fetch->data + fetch->numBytes);
    userData->downloader->handleDownloadComplete(userData->key, data);
  }

  delete userData;
  emscripten_fetch_close(fetch);
}

void TileDownloader::onEmscriptenDownloadError(emscripten_fetch_t *fetch)
{
  struct UserData
  {
    TileKey key;
    TileDownloader *downloader;
  };

  UserData *userData = static_cast<UserData *>(fetch->userData);
  if (userData && userData->downloader)
  {
    std::string error = "Download failed: " + std::to_string(fetch->status);
    userData->downloader->handleDownloadError(userData->key, error);
  }

  delete userData;
  emscripten_fetch_close(fetch);
}
#endif

// ======== TileSystemManager Implementation ========

TileSystemManager::TileSystemManager()
{
  initializeDefaultServices();

  // 创建默认缓存
  _globalCache = std::make_shared<MemoryTileCache>(200 * 1024 * 1024); // 200MB
}

TileSystemManager::~TileSystemManager()
{
  cleanup();
}

void TileSystemManager::initializeDefaultServices()
{
  // 添加默认瓦片服务
  addTileService("google_satellite", *TileServiceConfig::createGoogleSatellite());
  addTileService("google_streets", *TileServiceConfig::createGoogleStreets());
  addTileService("bing_aerial", *TileServiceConfig::createBingAerial());
  addTileService("osm_mapnik", *TileServiceConfig::createOSMMapnik());
  addTileService("esri_world_imagery", *TileServiceConfig::createESRIWorldImagery());

  // 设置默认服务
  setDefaultTileService("google_satellite");
}

void TileSystemManager::addTileService(const std::string &name, const TileServiceConfig &config)
{
  _tileServices[name] = config;
}

void TileSystemManager::removeTileService(const std::string &name)
{
  _tileServices.erase(name);
  _downloaders.erase(name);

  if (_defaultService == name)
  {
    _defaultService.clear();
  }
}

const TileServiceConfig *TileSystemManager::getTileService(const std::string &name) const
{
  auto it = _tileServices.find(name);
  return it != _tileServices.end() ? &it->second : nullptr;
}

std::vector<std::string> TileSystemManager::getTileServiceNames() const
{
  std::vector<std::string> names;
  for (const auto &pair : _tileServices)
  {
    names.push_back(pair.first);
  }
  return names;
}

void TileSystemManager::setDefaultTileService(const std::string &name)
{
  if (_tileServices.find(name) != _tileServices.end())
  {
    _defaultService = name;
  }
}

void TileSystemManager::setGlobalTileCache(std::shared_ptr<TileCache> cache)
{
  _globalCache = cache;
}

std::shared_ptr<TileDownloader> TileSystemManager::createTileDownloader(const std::string &serviceName)
{
  std::string actualServiceName = serviceName.empty() ? _defaultService : serviceName;

  auto it = _downloaders.find(actualServiceName);
  if (it != _downloaders.end())
  {
    return it->second;
  }

  const TileServiceConfig *config = getTileService(actualServiceName);
  if (!config)
  {
    return nullptr;
  }

  auto downloader = std::make_shared<TileDownloader>();
  downloader->setServiceConfig(*config);
  downloader->setTileCache(_globalCache);

  _downloaders[actualServiceName] = downloader;
  return downloader;
}

bool TileSystemManager::getTile(const TileKey &key, TileData &tile, const std::string &serviceName)
{
  if (_globalCache)
  {
    return _globalCache->getTile(key, tile);
  }
  return false;
}

void TileSystemManager::getTileAsync(const TileKey &key, std::shared_ptr<TileLoadCallback> callback, const std::string &serviceName)
{
  auto downloader = createTileDownloader(serviceName);
  if (downloader)
  {
    downloader->setLoadCallback(callback);
    downloader->downloadTile(key);
  }
}

void TileSystemManager::preloadTiles(const std::vector<TileKey> &keys, const std::string &serviceName)
{
  auto downloader = createTileDownloader(serviceName);
  if (downloader)
  {
    downloader->downloadTiles(keys);
  }
}

void TileSystemManager::cleanup()
{
  _downloaders.clear();
  if (_globalCache)
  {
    _globalCache->clear();
  }
}

TileSystemManager::Statistics TileSystemManager::getStatistics() const
{
  Statistics stats = {};

  if (_globalCache)
  {
    stats.cacheSize = _globalCache->getSize();
    stats.cacheCount = _globalCache->getCount();
  }

  // 累计下载器统计
  for (const auto &pair : _downloaders)
  {
    stats.downloadedCount += pair.second->getDownloadedCount();
    stats.failedCount += pair.second->getFailedCount();
    stats.totalDownloadTime += pair.second->getTotalDownloadTime();
  }

  return stats;
}