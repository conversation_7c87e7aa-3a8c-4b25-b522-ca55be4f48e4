# WebAssembly数字地球纹理全黑问题修复报告

## 问题描述

在WebAssembly版本的数字地球项目中，发现纹理显示全黑的问题。该问题主要出现在浏览器环境中，而桌面版本工作正常。

## 问题分析

经过分析，发现问题的根本原因是WebGL与传统OpenGL在纹理渲染管线上的差异：

1. **纹理环境模式问题**：WebGL中默认的纹理与材质混合模式导致纹理颜色被材质颜色相乘，结果变成全黑
2. **光照模型兼容性**：WebGL对某些OpenGL光照功能的支持有限
3. **纹理格式设置**：WebGL对纹理格式的要求更严格

## 修复方案

### 1. 纹理环境模式修复

在`createEarthNode()`函数中，针对WebAssembly版本添加了专门的纹理设置：

```cpp
#ifdef EMSCRIPTEN
    // WebAssembly版本：修复纹理全黑问题
    // 1. 设置纹理环境模式为REPLACE，避免与材质颜色相乘导致全黑
    osg::TexEnv* texEnv = new osg::TexEnv();
    texEnv->setMode(osg::TexEnv::REPLACE);
    stateSet->setTextureAttributeAndModes(0, texEnv, osg::StateAttribute::ON);
    
    // 2. 禁用光照，使用纹理本身的颜色
    stateSet->setMode(GL_LIGHTING, osg::StateAttribute::OFF);
#endif
```

### 2. 材质设置优化

为WebAssembly版本设置了简化的材质属性，避免颜色干扰：

```cpp
osg::Material *material = new osg::Material();
material->setDiffuse(osg::Material::FRONT_AND_BACK, osg::Vec4(1.0f, 1.0f, 1.0f, 1.0f));
material->setAmbient(osg::Material::FRONT_AND_BACK, osg::Vec4(1.0f, 1.0f, 1.0f, 1.0f));
material->setSpecular(osg::Material::FRONT_AND_BACK, osg::Vec4(0.0f, 0.0f, 0.0f, 1.0f));
material->setEmission(osg::Material::FRONT_AND_BACK, osg::Vec4(0.0f, 0.0f, 0.0f, 1.0f));
```

### 3. 纹理创建优化

在`createProceduralEarthTexture()`函数中增强了WebGL兼容性：

```cpp
#ifdef EMSCRIPTEN
    // WebAssembly版本额外设置：确保WebGL兼容性
    texture->setInternalFormat(GL_RGB);
    texture->setSourceFormat(GL_RGB);
    texture->setSourceType(GL_UNSIGNED_BYTE);
    
    // 强制纹理上传到GPU
    texture->setUnRefImageDataAfterApply(false);
    texture->setResizeNonPowerOfTwoHint(false);
#endif
```

### 4. 瓦片纹理修复

在`EarthTileLoadCallback::updateEarthTexture()`中也应用了相同的修复：

```cpp
#ifdef EMSCRIPTEN
    // WebAssembly版本：确保瓦片纹理也使用REPLACE模式
    osg::TexEnv* texEnv = new osg::TexEnv();
    texEnv->setMode(osg::TexEnv::REPLACE);
    stateSet->setTextureAttributeAndModes(0, texEnv, osg::StateAttribute::ON);
#endif
```

### 5. 全局渲染设置优化

在OSG初始化函数中添加了WebGL优化设置：

```cpp
// WebGL纹理渲染优化设置
globalStateSet->setMode(GL_CULL_FACE, osg::StateAttribute::ON);
globalStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);

// 设置清除颜色为深蓝色，便于调试
g_appContext->viewer->getCamera()->setClearColor(osg::Vec4(0.1f, 0.1f, 0.3f, 1.0f));
```

## 依赖库路径更新

将CMakeLists.txt中的WebAssembly依赖库路径从：
```cmake
set(OSG_WASM_LIB_DIR "C:/GeminiCLI/wasm_dep")
```
更新为：
```cmake
set(OSG_WASM_LIB_DIR "F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep")
```

## 编译和部署

1. 使用PowerShell脚本进行编译：
   ```powershell
   .\build_wasm.ps1 -Clean -Install
   ```

2. 启动HTTP服务器进行测试：
   ```powershell
   .\build_wasm.ps1 -Install -Serve
   ```

3. 在浏览器中访问：
   ```
   http://localhost:8000/enhanced_osg_earth_sample.html
   ```

## 修复结果

经过上述修复，WebAssembly版本的数字地球应用现在能够正确显示纹理，解决了纹理全黑的问题。主要改进包括：

- ✅ 纹理正确显示，不再全黑
- ✅ 程序化地球纹理正常渲染
- ✅ 瓦片纹理加载和显示正常
- ✅ WebGL兼容性得到改善
- ✅ 保持了与桌面版本的功能一致性

## 技术要点

1. **纹理环境模式**：WebGL中使用`TexEnv::REPLACE`模式避免纹理与材质颜色相乘
2. **光照控制**：在WebGL版本中禁用复杂光照，直接使用纹理颜色
3. **材质简化**：使用白色材质避免颜色干扰
4. **格式兼容**：确保纹理格式符合WebGL要求
5. **条件编译**：使用`#ifdef EMSCRIPTEN`实现平台特定优化

## 后续建议

1. 可以考虑添加更多的WebGL调试信息输出
2. 进一步优化纹理加载性能
3. 添加更多的错误处理和兼容性检查
4. 考虑实现着色器版本的渲染管线以获得更好的性能

---

**修复完成时间**：2025年7月13日  
**修复版本**：Enhanced OSG Earth Sample v1.0.1  
**测试环境**：Chrome浏览器，WebGL 2.0支持
