/* osgEarth
* Copyright 2025 Pelican Mapping
* MIT License
*/

#ifndef VERTEX_COLLECTION_VISITOR
#define VERTEX_COLLECTION_VISITOR 1

#include <osg/CoordinateSystemNode>
#include <osg/NodeVisitor>

class VertexCollectionVisitor : public osg::NodeVisitor
{
public:

    VertexCollectionVisitor(bool geocentric = false, TraversalMode traversalMode = TRAVERSE_ALL_CHILDREN);
    
    virtual void reset();
    
    osg::Vec3dArray* getVertices() { return _vertices.get(); }
    
    void apply(osg::Node& node);
    
    void apply(osg::Transform& transform);

    void apply(osg::Drawable& drawable);
    
    inline void pushMatrix(osg::Matrix& matrix) { _matrixStack.push_back(matrix); }
    
    inline void popMatrix() { _matrixStack.pop_back(); }
    
protected:
    
    typedef std::vector<osg::Matrix> MatrixStack;

    osg::ref_ptr<osg::Vec3dArray>  _vertices;
    MatrixStack _matrixStack;
    bool _geocentric;
    osg::ref_ptr<osg::EllipsoidModel> _ellipsoidModel;

    void addVertex(osg::Vec3d vertex);
};

#endif //VERTEX_COLLECTION_VISITOR
