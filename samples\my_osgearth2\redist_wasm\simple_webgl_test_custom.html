<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OSG 简化 WebGL 测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #000;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }

        #container {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        #header {
            background-color: #333;
            color: white;
            padding: 10px;
            text-align: center;
            font-size: 18px;
        }

        #canvas-container {
            flex: 1;
            position: relative;
            background-color: #000;
        }

        #canvas {
            width: 100%;
            height: 100%;
            display: block;
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            text-align: center;
        }

        #status {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
        }

        .hidden {
            display: none;
        }
    </style>
</head>

<body>
    <div id="container">
        <div id="header">OSG 简化 WebGL 测试 - 验证基本渲染</div>
        <div id="canvas-container">
            <canvas id="canvas"></canvas>
            <div id="loading">正在加载 OSG WebGL 模块...</div>
            <div id="status" class="hidden">
                <div>状态: <span id="render-status">--</span></div>
                <div>说明: 应该显示一个蓝色的地球球体</div>
                <div>操作: 鼠标拖拽旋转，滚轮缩放</div>
            </div>
        </div>
    </div>

    <script>
        let canvas = document.getElementById('canvas');
        let loadingDiv = document.getElementById('loading');
        let statusDiv = document.getElementById('status');

        function resizeCanvas() {
            const container = document.getElementById('canvas-container');
            canvas.width = container.clientWidth;
            canvas.height = container.clientHeight;
        }

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 在加载脚本之前设置 Module 配置
        window.Module = {
            canvas: canvas,
            postRun: [function () {
                console.log('OSG WebGL 模块加载完成');
                loadingDiv.classList.add('hidden');
                statusDiv.classList.remove('hidden');
                startStatusMonitoring();
            }],
            print: function (text) {
                console.log('OSG:', text);
            },
            printErr: function (text) {
                console.error('OSG Error:', text);
            },
            onRuntimeInitialized: function () {
                console.log('OSG WebGL 运行时初始化完成');
            }
        };

        function startStatusMonitoring() {
            setInterval(function () {
                if (window.Module && window.Module.getRenderStatus) {
                    document.getElementById('render-status').textContent = window.Module.getRenderStatus();
                } else {
                    document.getElementById('render-status').textContent = 'OSG WebGL 渲染中...';
                }
            }, 1000);
        }

        // 加载模块
        const script = document.createElement('script');
        script.src = 'simple_webgl_test.js';
        script.onload = function () {
            console.log('OSG WebGL 测试脚本加载完成');
        };
        script.onerror = function () {
            loadingDiv.innerHTML = '<div style="color: red;">加载失败: 找不到 simple_webgl_test.js 文件</div>';
        };
        document.head.appendChild(script);
    </script>
</body>

</html>