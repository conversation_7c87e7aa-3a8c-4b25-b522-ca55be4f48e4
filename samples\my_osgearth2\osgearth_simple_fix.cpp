#include <osgViewer/Viewer>
#include <osgGA/TrackballManipulator>
#include <osgEarth/MapNode>
#include <osgEarth/Map>
#include <osgEarth/ImageLayer>
#include <osgEarth/XYZ>
#include <osgEarth/Profile>
#include <osgEarth/URI>
#include <osgEarth/GLUtils>
#include <osgEarth/Registry>
#include <osgGA/StateSetManipulator>
#include <osgGA/GUIEventHandler>
#include <osgViewer/ViewerEventHandlers>
#include <osg/Group>
#include <iostream>
#include <string>

#ifdef __EMSCRIPTEN__
#include <emscripten.h>
#include <emscripten/html5.h>
#include <osg/GraphicsContext>

// 全局 WebGL 上下文
static EMSCRIPTEN_WEBGL_CONTEXT_HANDLE g_webgl_context = 0;

// 简化的 Emscripten GraphicsContext
class SimpleEmscriptenGraphicsContext : public osg::GraphicsContext
{
public:
    SimpleEmscriptenGraphicsContext() : osg::GraphicsContext()
    {
        _traits = new Traits();
        _traits->x = 0;
        _traits->y = 0;
        _traits->width = 800;
        _traits->height = 600;
        _traits->doubleBuffer = true;
        _traits->depth = 24;
        _traits->stencil = 8;

        _valid = true;
        _realized = false;

        // 创建 WebGL 上下文
        if (g_webgl_context == 0)
        {
            EmscriptenWebGLContextAttributes attrs;
            emscripten_webgl_init_context_attributes(&attrs);
            attrs.alpha = true;
            attrs.depth = true;
            attrs.stencil = true;
            attrs.antialias = true;
            attrs.premultipliedAlpha = false;
            attrs.preserveDrawingBuffer = false;
            attrs.powerPreference = EM_WEBGL_POWER_PREFERENCE_DEFAULT;
            attrs.failIfMajorPerformanceCaveat = false;
            attrs.majorVersion = 2;
            attrs.minorVersion = 0;
            attrs.enableExtensionsByDefault = true;

            g_webgl_context = emscripten_webgl_create_context("#canvas", &attrs);
            if (g_webgl_context > 0)
            {
                std::cout << "[DEBUG] WebGL context created successfully: " << g_webgl_context << std::endl;
                emscripten_webgl_make_context_current(g_webgl_context);
            }
            else
            {
                std::cout << "[ERROR] Failed to create WebGL context: " << g_webgl_context << std::endl;
            }
        }

        // 创建状态对象
        setState(new osg::State);
        getState()->setGraphicsContext(this);

        std::cout << "[DEBUG] SimpleEmscriptenGraphicsContext created" << std::endl;
    }

    virtual ~SimpleEmscriptenGraphicsContext()
    {
        std::cout << "[DEBUG] SimpleEmscriptenGraphicsContext destructor called" << std::endl;

        // 安全地清理资源，避免内存访问越界
        try
        {
            // 不调用 close()，避免在析构函数中触发复杂的清理逻辑
            _realized = false;
            _valid = false;

            // 清理状态对象的引用，但不删除
            if (getState())
            {
                getState()->setGraphicsContext(nullptr);
            }
        }
        catch (...)
        {
            std::cout << "[ERROR] Exception in SimpleEmscriptenGraphicsContext destructor" << std::endl;
        }

        std::cout << "[DEBUG] SimpleEmscriptenGraphicsContext destructor completed" << std::endl;
    }

    virtual bool valid() const override { return _valid; }
    virtual bool realizeImplementation() override
    {
        _realized = true;
        std::cout << "[DEBUG] SimpleEmscriptenGraphicsContext realized" << std::endl;
        return true;
    }
    virtual bool isRealizedImplementation() const override { return _realized; }
    virtual void closeImplementation() override
    {
        // 在关闭前清理 WebGL 上下文
        if (g_webgl_context > 0)
        {
            // 不要销毁上下文，只是标记为未实现
            std::cout << "[DEBUG] Closing WebGL context (keeping alive)" << std::endl;
        }
        _realized = false;
    }
    virtual bool makeCurrentImplementation() override
    {
        if (g_webgl_context > 0)
        {
            EMSCRIPTEN_RESULT result = emscripten_webgl_make_context_current(g_webgl_context);
            return result == EMSCRIPTEN_RESULT_SUCCESS;
        }
        return true;
    }
    virtual bool makeContextCurrentImplementation(osg::GraphicsContext *) override
    {
        return makeCurrentImplementation();
    }
    virtual bool releaseContextImplementation() override { return true; }
    virtual void swapBuffersImplementation() override {}
    virtual void bindPBufferToTextureImplementation(GLenum) override {}

private:
    bool _valid;
    bool _realized;
};

// 简化的窗口系统接口
class SimpleEmscriptenWindowingSystemInterface : public osg::GraphicsContext::WindowingSystemInterface
{
public:
    virtual unsigned int getNumScreens(const osg::GraphicsContext::ScreenIdentifier &) override
    {
        return 1;
    }

    virtual void getScreenSettings(const osg::GraphicsContext::ScreenIdentifier &, osg::GraphicsContext::ScreenSettings &resolution) override
    {
        resolution.width = 800;
        resolution.height = 600;
        resolution.colorDepth = 32;
        resolution.refreshRate = 60.0;
    }

    virtual void enumerateScreenSettings(const osg::GraphicsContext::ScreenIdentifier &si, osg::GraphicsContext::ScreenSettingsList &resolutionList) override
    {
        osg::GraphicsContext::ScreenSettings resolution;
        getScreenSettings(si, resolution);
        resolutionList.push_back(resolution);
    }

    virtual osg::GraphicsContext *createGraphicsContext(osg::GraphicsContext::Traits *) override
    {
        std::cout << "[DEBUG] Creating SimpleEmscriptenGraphicsContext" << std::endl;
        return new SimpleEmscriptenGraphicsContext();
    }
};
#endif

// 全局变量
static osgViewer::Viewer *g_viewer = nullptr;
static bool g_initialized = false;

/**
 * 创建简单地图
 */
osgEarth::Map *createSimpleMap()
{
    std::cout << "[DEBUG] Creating simple map..." << std::endl;

    // 创建地图，使用地理坐标系
    osgEarth::Map *map = new osgEarth::Map();
    map->setProfile(osgEarth::Profile::create(osgEarth::Profile::GLOBAL_GEODETIC));

    std::cout << "[DEBUG] Simple map created successfully" << std::endl;
    return map;
}

/**
 * 初始化查看器
 */
bool initializeViewer()
{
    std::cout << "[DEBUG] Initializing viewer..." << std::endl;

    try
    {
        // 创建查看器
        g_viewer = new osgViewer::Viewer();

        // 设置单线程模式
        g_viewer->setThreadingModel(osgViewer::Viewer::SingleThreaded);
        g_viewer->setRunFrameScheme(osgViewer::Viewer::CONTINUOUS);

#ifdef __EMSCRIPTEN__
        // WebAssembly 环境设置
        std::cout << "[DEBUG] WebAssembly mode - setting up embedded window" << std::endl;
        g_viewer->setUpViewerAsEmbeddedInWindow(0, 0, 800, 600);

        // 应用 GLUtils 设置
        osgEarth::GLUtils::setGlobalDefaults(g_viewer->getCamera()->getOrCreateStateSet());

        // 设置相机
        osg::Camera *camera = g_viewer->getCamera();
        camera->setClearColor(osg::Vec4(0.1f, 0.1f, 0.3f, 1.0f));
        camera->setClearMask(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

        // 设置投影
        camera->setProjectionMatrixAsPerspective(45.0, 800.0 / 600.0, 1.0, 1e12);
        camera->setComputeNearFarMode(osg::CullSettings::COMPUTE_NEAR_FAR_USING_BOUNDING_VOLUMES);
#else
        g_viewer->setUpViewInWindow(100, 100, 800, 600);
#endif

        // 创建地图
        osgEarth::Map *map = createSimpleMap();
        osgEarth::MapNode *mapNode = new osgEarth::MapNode(map);

        // 设置场景
        g_viewer->setSceneData(mapNode);

        // 添加操作器
        g_viewer->setCameraManipulator(new osgGA::TrackballManipulator());

        // 添加事件处理器
        g_viewer->addEventHandler(new osgGA::StateSetManipulator(g_viewer->getCamera()->getOrCreateStateSet()));
        g_viewer->addEventHandler(new osgViewer::StatsHandler());
        g_viewer->addEventHandler(new osgViewer::WindowSizeHandler());

#ifdef __EMSCRIPTEN__
        // WebAssembly 环境下跳过 realize()
        std::cout << "[DEBUG] WebAssembly mode: Skipping realize()" << std::endl;
        g_viewer->setDone(false);
#else
        g_viewer->realize();
#endif

        std::cout << "[DEBUG] Viewer initialized successfully" << std::endl;
        g_initialized = true;
        return true;
    }
    catch (const std::exception &e)
    {
        std::cerr << "[ERROR] Exception in initializeViewer: " << e.what() << std::endl;
        return false;
    }
}

/**
 * 主循环函数
 */
void mainLoop()
{
    static bool firstFrame = true;

    if (firstFrame)
    {
        std::cout << "[DEBUG] First frame - initializing..." << std::endl;
        if (!initializeViewer())
        {
            std::cerr << "[ERROR] Failed to initialize viewer" << std::endl;
            return;
        }
        firstFrame = false;
    }

    if (g_viewer && g_initialized && !g_viewer->done())
    {
        try
        {
            g_viewer->frame();
        }
        catch (const std::exception &e)
        {
            std::cerr << "[ERROR] Exception during frame: " << e.what() << std::endl;
        }
    }
}

/**
 * 主函数
 */
int main()
{
    std::cout << "========================================" << std::endl;
    std::cout << "osgEarth Simple Fix Test" << std::endl;
    std::cout << "========================================" << std::endl;

#ifdef __EMSCRIPTEN__
    // 注册简化的窗口系统接口
    std::cout << "[DEBUG] Registering SimpleEmscriptenWindowingSystemInterface..." << std::endl;
    osg::ref_ptr<SimpleEmscriptenWindowingSystemInterface> wsi = new SimpleEmscriptenWindowingSystemInterface();
    wsi->setName("Emscripten");
    osg::GraphicsContext::getWindowingSystemInterfaces()->addWindowingSystemInterface(wsi.get());
    std::cout << "[DEBUG] WindowingSystemInterface registered" << std::endl;
#endif

    // 初始化 osgEarth
    std::cout << "[DEBUG] Initializing osgEarth..." << std::endl;
    osgEarth::initialize();
    std::cout << "[DEBUG] osgEarth initialized successfully" << std::endl;

#ifdef __EMSCRIPTEN__
    std::cout << "[INFO] Starting Emscripten main loop..." << std::endl;
    emscripten_set_main_loop(mainLoop, 60, 1);
#else
    std::cout << "[INFO] Starting desktop main loop..." << std::endl;

    if (!initializeViewer())
    {
        std::cerr << "[ERROR] Failed to initialize viewer" << std::endl;
        return -1;
    }

    while (!g_viewer->done())
    {
        g_viewer->frame();
    }
#endif

    return 0;
}
