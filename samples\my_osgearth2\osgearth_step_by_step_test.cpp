// osgEarth 逐步测试 - 找出确切的失败点
// 基于成功的 OSG 模式，逐步添加 osgEarth 组件

#ifdef EMSCRIPTEN
#define _LIBCPP_HAS_NO_PRAGMA_SYSTEM_HEADER
#define _LIBCPP_DISABLE_AVAILABILITY
#endif

#ifdef _WIN32
#define _USE_MATH_DEFINES
#define NOMINMAX
#endif

#include <iostream>
#include <memory>
#include <string>

// OSG核心头文件
#include <osg/ref_ptr>
#include <osg/Group>
#include <osg/Geode>
#include <osg/ShapeDrawable>
#include <osg/Shape>
#include <osg/StateSet>
#include <osg/Material>

// OSG Viewer
#include <osgViewer/Viewer>
#include <osgViewer/ViewerEventHandlers>
#include <osgGA/TrackballManipulator>
#include <osgGA/StateSetManipulator>

// osgEarth 头文件 - 在这里包含
#include <osgEarth/Map>
#include <osgEarth/MapNode>

// 平台相关头文件
#ifdef EMSCRIPTEN
#include <SDL2/SDL.h>
#include <emscripten.h>
#include <emscripten/html5.h>
#else
#include <SDL.h>
#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#endif
#endif

// 测试阶段枚举
enum TestPhase
{
    PHASE_OSG_ONLY = 0,            // 只测试 OSG
    PHASE_INCLUDE_OSGEARTH = 1,    // 包含 osgEarth 头文件
    PHASE_CREATE_MAP = 2,          // 创建 osgEarth::Map
    PHASE_CREATE_MAPNODE = 3,      // 创建 osgEarth::MapNode
    PHASE_INITIALIZE_OSGEARTH = 4, // 调用 osgEarth::initialize()
    PHASE_FULL_OSGEARTH = 5        // 完整的 osgEarth 功能
};

// 当前测试阶段
static TestPhase g_currentPhase = PHASE_OSG_ONLY;

// 全局变量
struct AppContext
{
    osg::ref_ptr<osgViewer::Viewer> viewer;
    osg::ref_ptr<osg::Group> rootNode;

    SDL_Window *window;
    SDL_GLContext context;
    bool shouldExit;

    AppContext() : shouldExit(false) {}
};

static AppContext *g_appContext = nullptr;

/**
 * 创建简单的 OSG 球体
 */
osg::ref_ptr<osg::Node> createSimpleEarth()
{
    std::cout << "[PHASE " << g_currentPhase << "] Creating simple OSG sphere..." << std::endl;

    osg::ref_ptr<osg::Geode> geode = new osg::Geode();
    osg::ref_ptr<osg::ShapeDrawable> sphere = new osg::ShapeDrawable(new osg::Sphere(osg::Vec3(0, 0, 0), 6378137.0f));

    // 设置材质
    osg::ref_ptr<osg::StateSet> stateSet = sphere->getOrCreateStateSet();
    osg::ref_ptr<osg::Material> material = new osg::Material();
    material->setDiffuse(osg::Material::FRONT_AND_BACK, osg::Vec4(0.2f, 0.5f, 0.8f, 1.0f));
    stateSet->setAttributeAndModes(material.get());

    geode->addDrawable(sphere.get());

    std::cout << "[PHASE " << g_currentPhase << "] Simple OSG sphere created successfully" << std::endl;
    return geode.get();
}

/**
 * 测试阶段 1：包含 osgEarth 头文件
 */
bool testPhaseIncludeOsgEarth()
{
    std::cout << "[PHASE 1] Testing osgEarth header inclusion..." << std::endl;

    try
    {
        // 头文件已经在文件顶部包含了
        std::cout << "[PHASE 1] osgEarth headers included successfully" << std::endl;
        return true;
    }
    catch (const std::exception &e)
    {
        std::cerr << "[PHASE 1] Failed to include osgEarth headers: " << e.what() << std::endl;
        return false;
    }
}

/**
 * 测试阶段 2：创建 osgEarth::Map
 */
bool testPhaseCreateMap()
{
    std::cout << "[PHASE 2] Testing osgEarth::Map creation..." << std::endl;

    try
    {
        // 尝试创建 Map 对象
        osg::ref_ptr<osgEarth::Map> map = new osgEarth::Map();

        std::cout << "[PHASE 2] osgEarth::Map created successfully" << std::endl;
        return true;
    }
    catch (const std::exception &e)
    {
        std::cerr << "[PHASE 2] Failed to create osgEarth::Map: " << e.what() << std::endl;
        return false;
    }
}

/**
 * 测试阶段 3：创建 osgEarth::MapNode
 */
bool testPhaseCreateMapNode()
{
    std::cout << "[PHASE 3] Testing osgEarth::MapNode creation..." << std::endl;

    try
    {
        // 创建 Map 和 MapNode
        osg::ref_ptr<osgEarth::Map> map = new osgEarth::Map();
        osg::ref_ptr<osgEarth::MapNode> mapNode = new osgEarth::MapNode(map.get());

        std::cout << "[PHASE 3] osgEarth::MapNode created successfully" << std::endl;
        return true;
    }
    catch (const std::exception &e)
    {
        std::cerr << "[PHASE 3] Failed to create osgEarth::MapNode: " << e.what() << std::endl;
        return false;
    }
}

/**
 * 测试阶段 4：调用 osgEarth::initialize()
 */
bool testPhaseInitializeOsgEarth()
{
    std::cout << "[PHASE 4] Testing osgEarth::initialize()..." << std::endl;

    try
    {
        // 这是最可能出问题的地方
        osgEarth::initialize();

        std::cout << "[PHASE 4] osgEarth::initialize() completed successfully" << std::endl;
        return true;
    }
    catch (const std::exception &e)
    {
        std::cerr << "[PHASE 4] Failed in osgEarth::initialize(): " << e.what() << std::endl;
        return false;
    }
}

/**
 * 初始化SDL
 */
bool initializeSDL()
{
    std::cout << "[SDL] Initializing SDL..." << std::endl;

    if (SDL_Init(SDL_INIT_VIDEO) < 0)
    {
        std::cerr << "[ERROR] SDL initialization failed: " << SDL_GetError() << std::endl;
        return false;
    }

    // 设置OpenGL属性
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 3);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 0);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_PROFILE_MASK, SDL_GL_CONTEXT_PROFILE_ES);
    SDL_GL_SetAttribute(SDL_GL_DOUBLEBUFFER, 1);
    SDL_GL_SetAttribute(SDL_GL_DEPTH_SIZE, 24);
    SDL_GL_SetAttribute(SDL_GL_STENCIL_SIZE, 8);

    // 创建窗口
    g_appContext->window = SDL_CreateWindow(
        "osgEarth Step by Step Test",
        SDL_WINDOWPOS_CENTERED, SDL_WINDOWPOS_CENTERED,
        800, 600,
        SDL_WINDOW_OPENGL | SDL_WINDOW_SHOWN);

    if (!g_appContext->window)
    {
        std::cerr << "[ERROR] Window creation failed: " << SDL_GetError() << std::endl;
        return false;
    }

    // 创建OpenGL上下文
    g_appContext->context = SDL_GL_CreateContext(g_appContext->window);
    if (!g_appContext->context)
    {
        std::cerr << "[ERROR] OpenGL context creation failed: " << SDL_GetError() << std::endl;
        return false;
    }

    SDL_GL_MakeCurrent(g_appContext->window, g_appContext->context);
    SDL_GL_SetSwapInterval(1);

    std::cout << "[SDL] SDL initialized successfully" << std::endl;
    return true;
}

/**
 * 初始化OSG
 */
bool initializeOSG()
{
    std::cout << "[OSG] Initializing OSG..." << std::endl;

    // 创建viewer
    g_appContext->viewer = new osgViewer::Viewer();

    // 设置为嵌入式窗口
    int x, y, width, height;
    SDL_GetWindowPosition(g_appContext->window, &x, &y);
    SDL_GetWindowSize(g_appContext->window, &width, &height);

#ifdef EMSCRIPTEN
    // WebAssembly版本 - 使用与成功的 OSG 示例相同的设置
    g_appContext->viewer->setUpViewerAsEmbeddedInWindow(0, 0, width, height);
    g_appContext->viewer->setThreadingModel(osgViewer::Viewer::SingleThreaded);

    // WebGL兼容性设置
    osg::ref_ptr<osg::StateSet> globalStateSet = g_appContext->viewer->getCamera()->getOrCreateStateSet();
    globalStateSet->setMode(GL_ALPHA_TEST, osg::StateAttribute::OFF);
    globalStateSet->setMode(GL_CULL_FACE, osg::StateAttribute::ON);
    globalStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);

    // 强制使用VBO
    osg::DisplaySettings::instance()->setVertexBufferHint(osg::DisplaySettings::VERTEX_BUFFER_OBJECT);

    // 设置清除颜色
    g_appContext->viewer->getCamera()->setClearColor(osg::Vec4(0.1f, 0.1f, 0.3f, 1.0f));
#else
    // 桌面版本设置
    osg::ref_ptr<osg::GraphicsContext::Traits> traits = new osg::GraphicsContext::Traits();
    traits->x = x;
    traits->y = y;
    traits->width = width;
    traits->height = height;
    traits->windowDecoration = false;
    traits->doubleBuffer = true;
    traits->sharedContext = 0;

    osg::ref_ptr<osg::GraphicsContext> gc = osg::GraphicsContext::createGraphicsContext(traits.get());
    g_appContext->viewer->getCamera()->setGraphicsContext(gc.get());
#endif

    // 设置相机参数
    g_appContext->viewer->getCamera()->setViewport(new osg::Viewport(0, 0, width, height));
    g_appContext->viewer->getCamera()->setProjectionMatrixAsPerspective(30.0, (double)width / height, 1.0, 1000000000.0);

    // 添加操作器
    g_appContext->viewer->setCameraManipulator(new osgGA::TrackballManipulator());

    // 添加事件处理器
    g_appContext->viewer->addEventHandler(new osgGA::StateSetManipulator(g_appContext->viewer->getCamera()->getOrCreateStateSet()));
    g_appContext->viewer->addEventHandler(new osgViewer::StatsHandler());
    g_appContext->viewer->addEventHandler(new osgViewer::WindowSizeHandler());

    std::cout << "[OSG] OSG initialized successfully" << std::endl;
    return true;
}

/**
 * 主循环
 */
void mainLoop()
{
    // 处理SDL事件
    SDL_Event event;
    while (SDL_PollEvent(&event))
    {
        if (event.type == SDL_QUIT)
        {
            g_appContext->shouldExit = true;
        }
        else if (event.type == SDL_KEYDOWN)
        {
            // 按数字键切换测试阶段
            if (event.key.keysym.sym >= SDLK_0 && event.key.keysym.sym <= SDLK_5)
            {
                int newPhase = event.key.keysym.sym - SDLK_0;
                if (newPhase != g_currentPhase)
                {
                    g_currentPhase = (TestPhase)newPhase;
                    std::cout << "[INFO] Switched to test phase " << g_currentPhase << std::endl;

                    // 重新创建场景
                    g_appContext->rootNode->removeChildren(0, g_appContext->rootNode->getNumChildren());

                    // 根据阶段添加不同的内容
                    if (g_currentPhase >= PHASE_OSG_ONLY)
                    {
                        osg::ref_ptr<osg::Node> simpleEarth = createSimpleEarth();
                        if (simpleEarth.valid())
                        {
                            g_appContext->rootNode->addChild(simpleEarth.get());
                        }
                    }
                }
            }
        }
    }

    // 渲染OSG场景
    if (g_appContext->viewer.valid())
    {
        g_appContext->viewer->frame();
    }

#ifndef EMSCRIPTEN
    // 桌面版本需要交换缓冲区
    SDL_GL_SwapWindow(g_appContext->window);
#endif
}

/**
 * 主函数
 */
int main(int argc, char *argv[])
{
#ifndef EMSCRIPTEN
    // 对于桌面版本，分配控制台用于调试输出
    if (AllocConsole())
    {
        freopen_s((FILE **)stdout, "CONOUT$", "w", stdout);
        freopen_s((FILE **)stderr, "CONOUT$", "w", stderr);
        freopen_s((FILE **)stdin, "CONIN$", "r", stdin);
        SetConsoleTitle("osgEarth Step by Step Test - Debug Output");
    }
#endif

    std::cout << "========================================" << std::endl;
    std::cout << "osgEarth Step by Step Test" << std::endl;
    std::cout << "========================================" << std::endl;
    std::cout << "Press 0-5 to switch test phases:" << std::endl;
    std::cout << "0: OSG Only" << std::endl;
    std::cout << "1: Include osgEarth Headers" << std::endl;
    std::cout << "2: Create osgEarth::Map" << std::endl;
    std::cout << "3: Create osgEarth::MapNode" << std::endl;
    std::cout << "4: Call osgEarth::initialize()" << std::endl;
    std::cout << "5: Full osgEarth" << std::endl;
    std::cout << "========================================" << std::endl;

    // 创建应用上下文
    g_appContext = new AppContext();

    // 初始化SDL
    if (!initializeSDL())
    {
        delete g_appContext;
        return -1;
    }

    // 初始化OSG
    if (!initializeOSG())
    {
        delete g_appContext;
        return -1;
    }

    // 创建根节点
    g_appContext->rootNode = new osg::Group();

    // 开始时只显示 OSG 球体
    osg::ref_ptr<osg::Node> simpleEarth = createSimpleEarth();
    if (simpleEarth.valid())
    {
        g_appContext->rootNode->addChild(simpleEarth.get());
    }

    // 设置场景
    g_appContext->viewer->setSceneData(g_appContext->rootNode.get());

    std::cout << "[INFO] Starting main loop..." << std::endl;

    // 启动主循环
#ifdef EMSCRIPTEN
    emscripten_set_main_loop(mainLoop, 60, 1);
#else
    while (!g_appContext->shouldExit)
    {
        mainLoop();
        std::this_thread::sleep_for(std::chrono::milliseconds(16)); // ~60 FPS
    }
#endif

    // 清理资源
    std::cout << "[INFO] Cleaning up..." << std::endl;
    delete g_appContext;
    SDL_Quit();

    return 0;
}
