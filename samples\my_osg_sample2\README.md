# Enhanced OSG Earth Sample - 增强版数字地球示例

## 项目简介

Enhanced OSG Earth Sample 是一个基于 OpenSceneGraph (OSG) 的高级数字地球应用程序，集成了完整的坐标系统支持和多源瓦片贴图功能。该项目支持桌面版和 WebAssembly 版本，为地理信息系统 (GIS) 和三维地球可视化提供了强大的基础平台。

## 主要特性

### 🌍 坐标系统支持
- **多坐标系统**: 支持地理坐标系 (WGS84)、Web墨卡托投影、UTM投影、地心坐标系
- **坐标转换**: 高精度的坐标系统间转换功能
- **地理范围**: 完整的地理边界框和范围计算
- **坐标显示**: 实时显示鼠标位置的地理坐标

### 🗺️ 瓦片贴图系统
- **多瓦片源**: 支持 Google、Bing、OpenStreetMap、ESRI、Mapbox 等多种瓦片服务
- **异步下载**: 高效的异步瓦片下载和缓存机制
- **LOD 管理**: 智能的层级细节 (Level of Detail) 管理
- **内存缓存**: 可配置的内存瓦片缓存系统

### 🎮 交互功能
- **鼠标控制**: 拖拽旋转、滚轮缩放
- **键盘快捷键**: 丰富的键盘控制选项
- **视角重置**: 一键恢复默认视角
- **服务切换**: 动态切换不同的瓦片服务

### 🖥️ 跨平台支持
- **桌面版**: Windows、Linux、macOS 原生应用
- **WebAssembly**: 浏览器中运行的高性能 3D 应用
- **现代架构**: 基于 C++17 和现代 OpenGL/WebGL

## 技术架构

### 核心组件

1. **SpatialReference 类**: 空间参考系统管理
   - 椭球体参数定义
   - 坐标系统创建和管理
   - 坐标转换算法实现

2. **TileSystem 类**: 瓦片系统管理
   - 瓦片键 (TileKey) 系统
   - 瓦片服务配置
   - 下载和缓存管理

3. **EarthNode**: 地球几何体渲染
   - 高质量球体网格生成
   - 程序化纹理创建
   - 真实瓦片纹理应用

### 依赖库

- **OpenSceneGraph**: 核心 3D 图形渲染
- **SDL2**: 跨平台窗口和输入管理
- **STB Image**: 图像解码库
- **Emscripten**: WebAssembly 编译工具链 (可选)

## 快速开始

### 环境要求

#### 桌面版
- CMake 3.10+
- C++17 兼容编译器 (MSVC 2019+, GCC 9+, Clang 10+)
- OpenSceneGraph 3.6+
- SDL2 2.0+
- 可选: VCPKG (Windows)

#### WebAssembly版
- Emscripten SDK 3.0+
- Python 3.6+ (用于 HTTP 服务器)
- 现代浏览器 (Chrome 70+, Firefox 65+)

### 构建和运行

#### 桌面版构建

```powershell
# 标准构建
.\build_desktop.ps1 -Install

# 调试构建
.\build_desktop.ps1 -BuildType Debug -Clean -Install

# 构建并运行
.\build_desktop.ps1 -Install -Run
```

#### WebAssembly版构建

```powershell
# 标准构建
.\build_wasm.ps1 -Install

# 构建并启动服务器
.\build_wasm.ps1 -Install -Serve

# 自定义端口
.\build_wasm.ps1 -Install -Serve -Port 9000
```

### 控制说明

#### 鼠标控制
- **左键拖拽**: 旋转地球
- **滚轮**: 缩放地球

#### 键盘快捷键
- **R**: 重置视角
- **C**: 切换坐标显示
- **T**: 切换瓦片服务
- **L**: 重新加载瓦片
- **S**: 显示统计信息
- **H**: 显示帮助
- **ESC**: 退出程序

## 项目结构

```
enhanced_my_osg_sample/
├── main.cpp                   # 主应用程序
├── SpatialReference.hpp/.cpp  # 坐标系统实现
├── TileSystem.hpp/.cpp        # 瓦片系统实现
├── stb_image.h                # STB 图像解码库
├── CMakeLists.txt             # CMake 构建配置
├── build_desktop.ps1          # 桌面版构建脚本
├── build_wasm.ps1             # WebAssembly 构建脚本
├── README.md                  # 项目文档
├── build_desk/                # 桌面版构建目录
├── build_wasm/                # WebAssembly 构建目录
└── assets/                    # 资源文件 (可选)
```

## 配置选项

### 瓦片服务配置

项目默认支持以下瓦片服务：

1. **Google Satellite** (默认)
   - 高分辨率卫星影像
   - 最大层级: 20

2. **Google Streets**
   - 详细的街道地图
   - 最大层级: 20

3. **Bing Aerial**
   - Bing Maps 正射影像
   - 最大层级: 19

4. **OpenStreetMap**
   - 开源地图数据
   - 最大层级: 19

5. **ESRI World Imagery**
   - ESRI 全球影像
   - 最大层级: 19

### 缓存配置

```cpp
// 设置缓存大小 (默认 200MB)
tileManager->setGlobalTileCache(
    std::make_shared<MemoryTileCache>(200 * 1024 * 1024)
);

// 设置并发下载数 (默认 6)
tileDownloader->setMaxConcurrentDownloads(6);

// 设置超时时间 (默认 30 秒)
tileDownloader->setTimeout(30.0);
```

## 性能优化

### 渲染优化
- 使用 VBO 和现代 OpenGL 管线
- 智能 LOD 系统减少渲染负担
- 纹理压缩和 Mipmap 支持

### 内存优化
- LRU 缓存算法
- 可配置的内存限制
- 及时释放不需要的资源

### 网络优化
- 并发下载控制
- 智能重试机制
- 渐进式纹理加载

## 开发指南

### 添加新的瓦片服务

```cpp
// 创建自定义瓦片服务配置
TileServiceConfig customConfig;
customConfig.setName("Custom Tile Service");
customConfig.setUrlTemplate("https://example.com/tiles/{z}/{x}/{y}.png");
customConfig.setFormat(TileFormat::PNG);
customConfig.setMaxLevel(18);

// 添加到瓦片管理器
tileManager->addTileService("custom", customConfig);
```

### 扩展坐标系统

```cpp
// 创建自定义投影
SpatialReference* customSRS = SpatialReference::createUTM(32, true); // UTM Zone 32N

// 坐标转换
GeoPoint point(geographicSRS, 116.4074, 39.9042, 0.0);
GeoPoint utmPoint = point.transform(customSRS);
```

### 自定义交互处理

```cpp
class CustomInteractionHandler : public EarthTileLoadCallback {
public:
    virtual void onTileLoaded(const TileData& tile) override {
        // 自定义瓦片加载处理
    }
    
    virtual void onTileLoadError(const TileKey& key, const std::string& error) override {
        // 自定义错误处理
    }
};
```

## 故障排除

### 常见问题

1. **编译错误 - 找不到 OSG 库**
   ```
   解决: 安装 OpenSceneGraph 或设置 CMAKE_PREFIX_PATH
   Windows: 使用 VCPKG 安装 osg
   ```

2. **WebAssembly 构建失败**
   ```
   解决: 确保 Emscripten SDK 正确安装并激活
   运行: emsdk activate latest
   ```

3. **瓦片加载失败**
   ```
   解决: 检查网络连接和瓦片服务 URL
   某些服务可能需要 API 密钥
   ```

4. **性能问题**
   ```
   解决: 调整缓存大小和并发下载数
   降低瓦片最大层级
   ```

### 调试模式

```powershell
# 启用调试构建
.\build_desktop.ps1 -BuildType Debug -Install

# WebAssembly 调试
.\build_wasm.ps1 -BuildType Debug -Install
```

## 许可证

本项目基于 MIT 许可证开源。详见 LICENSE 文件。

## 贡献

欢迎提交 Issue 和 Pull Request！

### 开发流程
1. Fork 项目
2. 创建特性分支
3. 提交变更
4. 发起 Pull Request

### 代码规范
- 使用 C++17 标准
- 遵循现有的代码风格
- 添加适当的注释和文档

## 更新日志

### v1.0.0 (2024-07-12)
- 🎉 初始版本发布
- ✨ 完整的坐标系统支持
- ✨ 多源瓦片贴图系统
- ✨ 桌面版和 WebAssembly 双版本支持
- ✨ 丰富的交互功能
- 📚 完整的文档和构建脚本

## 技术支持

如有问题，请通过以下方式联系：

- 📧 Email: [项目维护者邮箱]
- 🐛 Issues: [GitHub Issues 页面]
- 📖 Wiki: [项目 Wiki 页面]

---

**Enhanced OSG Earth Sample** - 让三维地球可视化变得简单而强大！ 