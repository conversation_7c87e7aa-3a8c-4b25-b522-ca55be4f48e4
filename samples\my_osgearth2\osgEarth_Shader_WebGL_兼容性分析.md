# osgEarth Shader WebGL 兼容性深度分析

## 🎯 问题核心

您提出的 shader 兼容性问题是 osgEarth WebAssembly 版本无法正常运行的关键因素之一。

## 🔍 Shader 兼容性问题分析

### 1. GLSL 版本差异

#### 桌面版 osgEarth Shader
```glsl
// 典型的桌面版 osgEarth shader
#version 330 core
#extension GL_ARB_texture_rectangle : enable

uniform sampler2DRect colorTexture;
uniform sampler2D depthTexture;

in vec2 texCoord;
out vec4 fragColor;

void main() {
    vec4 color = texture(colorTexture, texCoord);
    float depth = texture2D(depthTexture, texCoord).r;
    fragColor = color;
}
```

#### WebGL 需要的版本
```glsl
// WebGL 兼容版本
#version 300 es
precision mediump float;

uniform sampler2D colorTexture;  // 不支持 sampler2DRect
uniform sampler2D depthTexture;

in vec2 texCoord;
out vec4 fragColor;

void main() {
    vec4 color = texture(colorTexture, texCoord);
    float depth = texture(depthTexture, texCoord).r;
    fragColor = color;
}
```

### 2. osgEarth 内置 Shader 问题

#### 地形渲染 Shader
osgEarth 的地形渲染系统使用复杂的 shader，包括：
- **顶点 Shader**: 地形变形、高度计算
- **片段 Shader**: 纹理混合、光照计算
- **几何 Shader**: 细分曲面（WebGL 不支持）

#### 图层混合 Shader
```glsl
// osgEarth 图层混合 shader（桌面版）
#version 330 core
#extension GL_EXT_texture_array : enable

uniform sampler2DArray layerTextures;
uniform float layerOpacity[8];

// WebGL 不支持 sampler2DArray（在 WebGL 1.0 中）
```

### 3. WebGL 限制对比

| 功能 | 桌面 OpenGL | WebGL 1.0 | WebGL 2.0 | osgEarth 使用 |
|------|-------------|------------|-----------|---------------|
| Geometry Shader | ✅ | ❌ | ❌ | ✅ 地形细分 |
| Texture Rectangle | ✅ | ❌ | ❌ | ✅ 纹理采样 |
| Texture Array | ✅ | ❌ | ✅ | ✅ 图层混合 |
| Transform Feedback | ✅ | ❌ | ✅ | ✅ GPU 计算 |
| Uniform Buffer | ✅ | ❌ | ✅ | ✅ 参数传递 |
| Multiple Render Targets | ✅ | 扩展 | ✅ | ✅ 延迟渲染 |

## 🛠️ 解决方案

### 方案 1: Shader 自动转换系统

```cpp
class WebGLShaderConverter {
public:
    std::string convertVertexShader(const std::string& desktopShader) {
        std::string webglShader = desktopShader;
        
        // 版本转换
        webglShader = std::regex_replace(webglShader, 
            std::regex("#version 330 core"), 
            "#version 300 es\nprecision highp float;");
        
        // 移除不支持的扩展
        webglShader = std::regex_replace(webglShader,
            std::regex("#extension GL_ARB_texture_rectangle.*\n"), "");
        
        // 属性名称转换
        webglShader = std::regex_replace(webglShader,
            std::regex("\\battribute\\b"), "in");
        webglShader = std::regex_replace(webglShader,
            std::regex("\\bvarying\\b"), "out");
        
        return webglShader;
    }
    
    std::string convertFragmentShader(const std::string& desktopShader) {
        std::string webglShader = desktopShader;
        
        // 版本和精度
        webglShader = std::regex_replace(webglShader, 
            std::regex("#version 330 core"), 
            "#version 300 es\nprecision mediump float;");
        
        // 纹理函数转换
        webglShader = std::regex_replace(webglShader,
            std::regex("\\btexture2D\\b"), "texture");
        webglShader = std::regex_replace(webglShader,
            std::regex("\\btextureCube\\b"), "texture");
        
        // 输出变量
        webglShader = std::regex_replace(webglShader,
            std::regex("\\bgl_FragColor\\b"), "fragColor");
        
        // 添加输出声明
        if (webglShader.find("out vec4 fragColor") == std::string::npos) {
            size_t pos = webglShader.find("void main()");
            if (pos != std::string::npos) {
                webglShader.insert(pos, "out vec4 fragColor;\n");
            }
        }
        
        return webglShader;
    }
};
```

### 方案 2: 简化 Shader 系统

```cpp
// 为 WebGL 创建简化的地球渲染 shader
class SimpleEarthShader {
public:
    static const char* getVertexShader() {
        return R"(
#version 300 es
precision highp float;

in vec3 osg_Vertex;
in vec3 osg_Normal;
in vec2 osg_MultiTexCoord0;

uniform mat4 osg_ModelViewProjectionMatrix;
uniform mat4 osg_ModelViewMatrix;
uniform mat3 osg_NormalMatrix;

out vec3 worldPos;
out vec3 normal;
out vec2 texCoord;

void main() {
    gl_Position = osg_ModelViewProjectionMatrix * vec4(osg_Vertex, 1.0);
    worldPos = (osg_ModelViewMatrix * vec4(osg_Vertex, 1.0)).xyz;
    normal = normalize(osg_NormalMatrix * osg_Normal);
    texCoord = osg_MultiTexCoord0;
}
)";
    }
    
    static const char* getFragmentShader() {
        return R"(
#version 300 es
precision mediump float;

in vec3 worldPos;
in vec3 normal;
in vec2 texCoord;

uniform sampler2D earthTexture;
uniform vec3 lightDirection;
uniform vec4 materialColor;

out vec4 fragColor;

void main() {
    // 简单的漫反射光照
    float NdotL = max(dot(normalize(normal), normalize(lightDirection)), 0.0);
    
    // 纹理采样
    vec4 texColor = texture(earthTexture, texCoord);
    
    // 最终颜色
    vec3 diffuse = texColor.rgb * materialColor.rgb * NdotL;
    fragColor = vec4(diffuse, materialColor.a);
}
)";
    }
};
```

### 方案 3: 运行时 Shader 检测和替换

```cpp
class OSGWebGLShaderManager {
private:
    std::map<std::string, std::string> shaderReplacements;
    
public:
    void initializeReplacements() {
        // 替换 osgEarth 的复杂 shader
        shaderReplacements["osgEarth::TerrainShader"] = SimpleEarthShader::getVertexShader();
        shaderReplacements["osgEarth::LayerShader"] = SimpleEarthShader::getFragmentShader();
    }
    
    osg::Shader* processShader(osg::Shader* originalShader) {
        if (!originalShader) return nullptr;
        
        std::string source = originalShader->getShaderSource();
        
        // 检查是否需要替换
        for (const auto& replacement : shaderReplacements) {
            if (source.find(replacement.first) != std::string::npos) {
                auto newShader = new osg::Shader(originalShader->getType());
                newShader->setShaderSource(replacement.second);
                return newShader;
            }
        }
        
        // 应用兼容性转换
        WebGLShaderConverter converter;
        std::string convertedSource;
        
        if (originalShader->getType() == osg::Shader::VERTEX) {
            convertedSource = converter.convertVertexShader(source);
        } else if (originalShader->getType() == osg::Shader::FRAGMENT) {
            convertedSource = converter.convertFragmentShader(source);
        }
        
        auto newShader = new osg::Shader(originalShader->getType());
        newShader->setShaderSource(convertedSource);
        return newShader;
    }
};
```

## 🎯 对 osgEarth Web 应用的影响

### 1. 功能限制
- **地形细分**: 无法使用几何 shader，需要在 CPU 端预处理
- **高级光照**: 复杂的光照模型需要简化
- **多图层混合**: 需要使用替代方案

### 2. 性能影响
- **CPU 负载增加**: 更多计算转移到 CPU
- **内存使用**: 预处理的几何数据占用更多内存
- **渲染质量**: 视觉效果可能有所降低

### 3. 开发复杂度
- **Shader 维护**: 需要维护两套 shader 代码
- **兼容性测试**: 需要在多个浏览器中测试
- **调试困难**: WebGL 调试工具相对有限

## 📊 建议的实施策略

### 立即行动
1. **实施 Shader 转换器**: 自动处理基本的兼容性问题
2. **创建简化 Shader**: 为核心功能提供 WebGL 兼容版本
3. **运行时检测**: 检测 WebGL 能力并选择合适的渲染路径

### 中期目标
1. **渐进式增强**: 根据 WebGL 版本提供不同的功能级别
2. **性能优化**: 优化 WebGL 特定的渲染管道
3. **工具链完善**: 开发 shader 转换和验证工具

### 长期规划
1. **架构重构**: 设计 WebGL 优先的渲染架构
2. **标准化**: 建立 WebGL 地理渲染的最佳实践
3. **社区贡献**: 向 osgEarth 社区贡献 WebGL 支持

## 🔧 当前问题修复

### WebGL 上下文创建问题
基于当前的错误分析，我们发现主要问题是：

```cpp
// 问题代码（不工作）
g_viewer->setUpViewerAsEmbeddedInWindow(0, 0, 800, 600);

// 修复代码（工作）
osg::ref_ptr<osg::GraphicsContext::Traits> traits = new osg::GraphicsContext::Traits();
traits->width = 800;
traits->height = 600;
osg::ref_ptr<osg::GraphicsContext> gc = osg::GraphicsContext::createGraphicsContext(traits.get());
g_viewer->getCamera()->setGraphicsContext(gc.get());
```

### 测试结果
- **修复版本**: `simple_webgl_test_fixed.html`
- **测试地址**: http://localhost:8081/test_fixed.html
- **预期效果**: 应该能正确显示蓝色地球球体

## 📝 结论

### 双重挑战
osgEarth WebAssembly 版本面临两个层面的挑战：

1. **底层技术问题**: WebGL 上下文创建、图形管道适配
2. **高层兼容性问题**: Shader 系统、渲染特性差异

### 解决策略
1. **立即修复**: 解决基础的 WebGL 上下文问题
2. **系统性解决**: 不是简单的修补，需要架构级别的考虑
3. **分阶段实施**: 从简化版本开始，逐步增强功能
4. **工具支持**: 开发自动化的 shader 转换和验证工具

### 对 Web 应用的影响
基于 osgEarth 的 Web 应用需要：
- **降低期望**: 初期功能会比桌面版本简化
- **渐进增强**: 随着技术成熟逐步添加高级功能
- **架构调整**: 可能需要混合渲染架构

这个问题的解决将显著提升基于 osgEarth 的 Web 应用的可行性和性能表现。

---

**分析完成时间**: 2025年1月13日
**关键发现**: WebGL 上下文创建和 Shader 兼容性是双重技术瓶颈
**推荐方案**: 底层修复 + 分阶段的 Shader 转换和简化策略
**当前状态**: 已提供 WebGL 上下文修复版本进行测试
