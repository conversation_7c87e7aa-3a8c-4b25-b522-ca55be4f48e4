# Enhanced OSG Earth Sample - WebAssembly Build Script
# PowerShell script to automate the build process for the WebAssembly version

param(
    [string]$BuildType = "Release",
    [switch]$Clean = $false,
    [switch]$Install = $false,
    [switch]$Serve = $false,
    [switch]$Help = $false,
    [int]$Port = 8000
)

# Display help information
if ($Help) {
    Write-Host "Enhanced OSG Earth Sample - WebAssembly Build Script" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage: .\build_wasm.ps1 [Options]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -BuildType <Type>  Build type (Debug/Release) [Default: Release]"
    Write-Host "  -Clean             Clean the build directory"
    Write-Host "  -Install           Install to redist_wasm directory"
    Write-Host "  -Serve             Start HTTP server after build"
    Write-Host "  -Port <Port>       HTTP server port [Default: 8000]"
    Write-Host "  -Help              Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\build_wasm.ps1                       # Standard build"
    Write-Host "  .\build_wasm.ps1 -Clean -Install      # Clean build and install"
    Write-Host "  .\build_wasm.ps1 -BuildType Debug     # Debug build"
    Write-Host "  .\build_wasm.ps1 -Serve               # Build and start server"
    Write-Host "  .\build_wasm.ps1 -Port 9000           # Use a different port"
    exit 0
}

# Script Configuration
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = $ScriptDir # Set ProjectRoot to the script's directory
$BuildDir = Join-Path $ScriptDir "build_wasm"
$RedistDir = Join-Path $ProjectRoot "redist_wasm"

# Environment Check
Write-Host "Enhanced OSG Earth Sample - WebAssembly Build" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green
Write-Host "Project directory: $ScriptDir"
Write-Host "Project root: $ProjectRoot"
Write-Host "Build directory: $BuildDir"
Write-Host "Build type: $BuildType"
Write-Host "HTTP port: $Port"
Write-Host ""

# Check for necessary tools
Write-Host "Checking build environment..." -ForegroundColor Yellow

# Check for Emscripten
$emsdkFound = $false
if ($env:EMSDK) {
    Write-Host "✓ EMSDK: $env:EMSDK" -ForegroundColor Green
    $emsdkFound = $true
} else {
    # Try default paths
    $defaultEmsdkPaths = @(
        "C:\dev\emsdk",
        "C:\emsdk",
        "$env:USERPROFILE\emsdk"
    )
    
    foreach ($path in $defaultEmsdkPaths) {
        if (Test-Path $path) {
            $env:EMSDK = $path
            Write-Host "✓ EMSDK: $path (auto-detected)" -ForegroundColor Green
            $emsdkFound = $true
            break
        }
    }
}

if (-not $emsdkFound) {
    Write-Host "✗ Emscripten SDK not found" -ForegroundColor Red
    Write-Host "Please install the Emscripten SDK and set the EMSDK environment variable" -ForegroundColor Red
    Write-Host "Download from: https://emscripten.org/docs/getting_started/downloads.html" -ForegroundColor Yellow
    exit 1
}

# Check for emcmake
try {
    $emcmakeVersion = & emcmake cmake --version 2>$null | Select-Object -First 1
    Write-Host "✓ emcmake: $emcmakeVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ emcmake not found, please ensure Emscripten is installed correctly" -ForegroundColor Red
    Write-Host "Please run: emsdk activate latest" -ForegroundColor Yellow
    exit 1
}

# Check for emmake
try {
    $emmakeVersion = & emmake --version 2>$null
    Write-Host "✓ emmake available" -ForegroundColor Green
} catch {
    Write-Host "⚠ emmake not found, but it's not essential" -ForegroundColor Yellow
}

# Check for Python (for the HTTP server)
if ($Serve) {
    try {
        $pythonVersion = & python --version 2>$null
        Write-Host "✓ Python: $pythonVersion" -ForegroundColor Green
    } catch {
        Write-Host "⚠ Python not found, cannot start HTTP server" -ForegroundColor Yellow
        $Serve = $false
    }
}

Write-Host ""

# Activate Emscripten environment
Write-Host "Activating Emscripten environment..." -ForegroundColor Yellow
if (Test-Path "$env:EMSDK\emsdk_env.ps1") {
    & "$env:EMSDK\emsdk_env.ps1"
    Write-Host "✓ Emscripten environment activated" -ForegroundColor Green
} elseif (Test-Path "$env:EMSDK\emsdk_env.bat") {
    cmd /c "$env:EMSDK\emsdk_env.bat"
    Write-Host "✓ Emscripten environment activated (via bat)" -ForegroundColor Green
} else {
    Write-Host "⚠ Could not find emsdk_env script, attempting to continue..." -ForegroundColor Yellow
}

# Clean build directory
if ($Clean) {
    Write-Host "Cleaning build directory..." -ForegroundColor Yellow
    if (Test-Path $BuildDir) {
        Remove-Item $BuildDir -Recurse -Force
        Write-Host "✓ Build directory cleaned" -ForegroundColor Green
    }
}

# Create build directory
if (-not (Test-Path $BuildDir)) {
    New-Item -Path $BuildDir -ItemType Directory -Force | Out-Null
    Write-Host "✓ Created build directory: $BuildDir" -ForegroundColor Green
}

# Entering build directory
Push-Location $BuildDir

try {
    # emcmake configuration
    Write-Host "Configuring with emcmake..." -ForegroundColor Yellow
    
    $emcmakeArgs = @(
        "cmake",
        "..",
        "-DCMAKE_BUILD_TYPE=$BuildType",
        "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON",
        "-DEMSCRIPTEN=ON",
        "-DCMAKE_CROSSCOMPILING_EMULATOR=node"
    )
    
    Write-Host "emcmake command: emcmake $($emcmakeArgs -join ' ')" -ForegroundColor Gray
    
    $configResult = & emcmake @emcmakeArgs
    if ($LASTEXITCODE -ne 0) {
        Write-Host "✗ emcmake configuration failed" -ForegroundColor Red
        Write-Host $configResult
        exit 1
    }
    Write-Host "✓ emcmake configuration successful" -ForegroundColor Green
    
    # Build project
    Write-Host "Building project..." -ForegroundColor Yellow
    
    $buildArgs = @(
        "--build", ".",
        "--config", $BuildType,
        "--", "-j", [Environment]::ProcessorCount
    )
    
    Write-Host "Build command: emmake make $($buildArgs -join ' ')" -ForegroundColor Gray
    
    $buildResult = & emmake make VERBOSE=1 -j ([Environment]::ProcessorCount)
    if ($LASTEXITCODE -ne 0) {
        Write-Host "✗ Build failed" -ForegroundColor Red
        Write-Host $buildResult
        exit 1
    }
    Write-Host "✓ Build successful" -ForegroundColor Green
    
    # Find generated WebAssembly files
    $wasmFiles = @(
        "enhanced_osg_earth_sample.html",
        "enhanced_osg_earth_sample.js",
        "enhanced_osg_earth_sample.wasm"
    )
    
    $foundFiles = @()
    foreach ($file in $wasmFiles) {
        $filePath = Get-ChildItem -Path . -Name $file -Recurse | Select-Object -First 1
        if ($filePath) {
            $fullPath = Join-Path $BuildDir $filePath
            $foundFiles += $fullPath
            
            $fileInfo = Get-Item $fullPath
            Write-Host "✓ Generated file: $file" -ForegroundColor Green
            Write-Host "  File size: $([math]::Round($fileInfo.Length / 1MB, 2)) MB" -ForegroundColor Gray
            Write-Host "  Modified: $($fileInfo.LastWriteTime)" -ForegroundColor Gray
        } else {
            Write-Host "⚠ File not found: $file" -ForegroundColor Yellow
        }
    }
    
    # Check for .data file
    $dataFile = Get-ChildItem -Path . -Name "enhanced_osg_earth_sample.data" -Recurse | Select-Object -First 1
    if ($dataFile) {
        $dataPath = Join-Path $BuildDir $dataFile
        $foundFiles += $dataPath
        $fileInfo = Get-Item $dataPath
        Write-Host "✓ Data file: enhanced_osg_earth_sample.data" -ForegroundColor Green
        Write-Host "  File size: $([math]::Round($fileInfo.Length / 1KB, 2)) KB" -ForegroundColor Gray
    }
    
    # Deploy to redist_wasm directory
    if ($Install) {
        Write-Host "Deploying to redist_wasm directory..." -ForegroundColor Yellow
        
        $deployResult = & cmake --build . --target deploy_wasm --config $BuildType
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Deployment successful" -ForegroundColor Green
            Write-Host "  Deployment directory: $RedistDir" -ForegroundColor Gray
        } else {
            Write-Host "⚠ Deployment failed, copying files manually" -ForegroundColor Yellow
            
            # Manual copy
            $redistBinDir = Join-Path $RedistDir "bin"
            if (-not (Test-Path $redistBinDir)) {
                New-Item -Path $redistBinDir -ItemType Directory -Force | Out-Null
            }
            
            foreach ($file in $foundFiles) {
                if (Test-Path $file) {
                    Copy-Item $file $redistBinDir -Force
                    Write-Host "  Copied: $(Split-Path $file -Leaf)" -ForegroundColor Gray
                }
            }
            Write-Host "✓ Manual copy complete" -ForegroundColor Green
        }
    }
    
} finally {
    # Return to original directory
    Pop-Location
}

# Display build summary
Write-Host ""
Write-Host "Build Summary:" -ForegroundColor Green
Write-Host "==============" -ForegroundColor Green
Write-Host "Build Type: $BuildType"
Write-Host "Emscripten: $env:EMSDK"
Write-Host "Build Directory: $BuildDir"
if ($Install) {
    Write-Host "Deployment Directory: $RedistDir"
}

# Start HTTP server
if ($Serve -and $Install) {
    Write-Host ""
    Write-Host "Starting HTTP server..." -ForegroundColor Yellow
    
    $serveDir = Join-Path $RedistDir "bin"
    if (Test-Path $serveDir) {
        $htmlFile = Join-Path $serveDir "enhanced_osg_earth_sample.html"
        if (Test-Path $htmlFile) {
            Write-Host "Serving directory: $serveDir" -ForegroundColor Gray
            Write-Host "Access URL: http://localhost:$Port/enhanced_osg_earth_sample.html" -ForegroundColor Gray
            Write-Host ""
            Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
            Write-Host ""
            
            # Attempt to open browser automatically
            try {
                Start-Process "http://localhost:$Port/enhanced_osg_earth_sample.html"
                Write-Host "✓ Attempted to open browser" -ForegroundColor Green
            } catch {
                Write-Host "⚠ Could not open browser automatically" -ForegroundColor Yellow
            }
            
            # Start Python HTTP server
            Push-Location $serveDir
            try {
                & python -m http.server $Port
            } finally {
                Pop-Location
            }
        } else {
            Write-Host "✗ HTML file not found: $htmlFile" -ForegroundColor Red
        }
    } else {
        Write-Host "✗ Serving directory does not exist: $serveDir" -ForegroundColor Red
    }
} elseif ($Serve) {
    Write-Host ""
    Write-Host "⚠ To start the server, add the -Install parameter" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "WebAssembly build complete!" -ForegroundColor Green

if ($Install -and -not $Serve) {
    Write-Host ""
    Write-Host "To test the WebAssembly application, run:" -ForegroundColor Yellow
    Write-Host "  .\build_wasm.ps1 -Serve" -ForegroundColor Cyan
    Write-Host "Or start the HTTP server manually:" -ForegroundColor Yellow
    Write-Host "  cd $(Join-Path $RedistDir 'bin')" -ForegroundColor Cyan
    Write-Host "  python -m http.server $Port" -ForegroundColor Cyan
    Write-Host "Then visit in your browser:" -ForegroundColor Yellow
    Write-Host "  http://localhost:$Port/enhanced_osg_earth_sample.html" -ForegroundColor Cyan
}