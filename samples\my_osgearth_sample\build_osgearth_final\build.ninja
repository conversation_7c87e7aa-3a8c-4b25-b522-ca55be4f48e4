# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.26

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: osgearth_wasm_fixed
# Configurations: Release
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Release
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/build_osgearth_final/
# =============================================================================
# Object build statements for EXECUTABLE target osgearth_wasm_fixed


#############################################
# Order-only phony target for osgearth_wasm_fixed

build cmake_object_order_depends_target_osgearth_wasm_fixed: phony || CMakeFiles/osgearth_wasm_fixed.dir

build CMakeFiles/osgearth_wasm_fixed.dir/osgearth_wasm.cpp.o: CXX_COMPILER__osgearth_wasm_fixed_unscanned_Release F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/build_osgearth_final/osgearth_wasm.cpp || cmake_object_order_depends_target_osgearth_wasm_fixed
  DEP_FILE = CMakeFiles\osgearth_wasm_fixed.dir\osgearth_wasm.cpp.o.d
  FLAGS = -O3 -DNDEBUG -std=gnu++17 -DOSG_GLSL_VERSION=300 -DOSGEARTH_HAVE_GEOS=1 -DOSGEARTH_HAVE_GDAL=0 -DUSE_EXTERNAL_WASM_DEPENDS=ON -DOSGEARTH_DISABLE_THREADING=1 -O3
  INCLUDES = -IF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include -IF:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src
  OBJECT_DIR = CMakeFiles\osgearth_wasm_fixed.dir
  OBJECT_FILE_DIR = CMakeFiles\osgearth_wasm_fixed.dir


# =============================================================================
# Link build statements for EXECUTABLE target osgearth_wasm_fixed


#############################################
# Link the executable osgearth_wasm_fixed.html

build osgearth_wasm_fixed.html: CXX_EXECUTABLE_LINKER__osgearth_wasm_fixed_Release CMakeFiles/osgearth_wasm_fixed.dir/osgearth_wasm.cpp.o | F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/../redist_wasm/libosgEarth.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosg.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgViewer.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgDB.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgGA.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgUtil.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgText.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgShadow.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgSim.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libOpenThreads.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libgeos.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libproj.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libcurl.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libGeographicLib.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libz.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libexpat.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libpng16.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libjpeg.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libtiff.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libfreetype.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libfontconfig.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libbz2.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/liblzma.a
  FLAGS = -O3 -DNDEBUG
  LINK_FLAGS = -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s WASM=1 -s INITIAL_MEMORY=268435456 -s ALLOW_MEMORY_GROWTH=1 -s MAXIMUM_MEMORY=2147483648 -s FETCH=1 -s ASYNCIFY=1 -s ASSERTIONS=1 -s SAFE_HEAP=0 -s STACK_OVERFLOW_CHECK=1 -s EXPORTED_FUNCTIONS=['_main'] -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','HEAPU8'] -s NO_EXIT_RUNTIME=1 -s DISABLE_EXCEPTION_CATCHING=0 -s FORCE_FILESYSTEM=1 -s USE_PTHREADS=0 -s PTHREAD_POOL_SIZE=0 -O3
  LINK_LIBRARIES = F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/../redist_wasm/libosgEarth.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosg.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgViewer.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgDB.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgGA.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgUtil.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgText.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgShadow.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgSim.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libOpenThreads.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libgeos.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libproj.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libcurl.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libGeographicLib.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libz.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libexpat.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libpng16.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libjpeg.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libtiff.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libfreetype.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libfontconfig.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libbz2.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/liblzma.a
  OBJECT_DIR = CMakeFiles\osgearth_wasm_fixed.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = osgearth_wasm_fixed.html
  TARGET_PDB = osgearth_wasm_fixed.html.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\src\applications\my_osgearth_sample\build_osgearth_final && "C:\Program Files\CMake\bin\cmake-gui.exe" -SF:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\src\applications\my_osgearth_sample\build_osgearth_final -BF:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\src\applications\my_osgearth_sample\build_osgearth_final"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\src\applications\my_osgearth_sample\build_osgearth_final && "C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -SF:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\src\applications\my_osgearth_sample\build_osgearth_final -BF:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250711\src\applications\my_osgearth_sample\build_osgearth_final"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build osgearth_wasm_fixed: phony osgearth_wasm_fixed.html

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src/applications/my_osgearth_sample/build_osgearth_final

build all: phony osgearth_wasm_fixed.html

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCCompiler.cmake.in C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCXXCompiler.cmake.in C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCXXInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCommonLanguageInclude.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCXXCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeDetermineSystem.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeFindBinUtils.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeGenericSystem.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeInitializeConfigs.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeLanguageInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeNinjaFindMake.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystem.cmake.in C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInitialize.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeTestCCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-C.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-CXX.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/GNU.cmake C$:/dev/emsdk/upstream/emscripten/cmake/Modules/Platform/Emscripten.cmake CMakeCache.txt CMakeFiles/3.26.4/CMakeCCompiler.cmake CMakeFiles/3.26.4/CMakeCXXCompiler.cmake CMakeFiles/3.26.4/CMakeSystem.cmake CMakeLists.txt
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCCompiler.cmake.in C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCXXCompiler.cmake.in C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCXXInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCommonLanguageInclude.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCXXCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeDetermineSystem.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeFindBinUtils.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeGenericSystem.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeInitializeConfigs.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeLanguageInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeNinjaFindMake.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystem.cmake.in C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInitialize.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeTestCCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-C.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-CXX.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/GNU.cmake C$:/dev/emsdk/upstream/emscripten/cmake/Modules/Platform/Emscripten.cmake CMakeCache.txt CMakeFiles/3.26.4/CMakeCCompiler.cmake CMakeFiles/3.26.4/CMakeCXXCompiler.cmake CMakeFiles/3.26.4/CMakeSystem.cmake CMakeLists.txt: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
