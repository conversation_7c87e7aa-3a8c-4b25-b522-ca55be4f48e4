# osgEarth WebAssembly 多线程部署总结

## 📋 项目概述

本项目成功实现了 osgEarth 库的 WebAssembly 多线程部署，解决了图形上下文、线程构造和 WebGL 兼容性等关键问题。

## ✅ 成功完成的任务

### 1. 编译支持多线程的版本
- ✅ 成功使用 `-s USE_PTHREADS=1 -s PTHREAD_POOL_SIZE=4` 编译
- ✅ 正确配置了 `-s ENVIRONMENT=web,worker` 支持多线程环境
- ✅ 生成了 `osgearth_multithread.html/js/wasm` 文件

### 2. HTTP 服务器支持 SharedArrayBuffer
- ✅ 服务器已配置正确的 CORS 头部：
  - `Cross-Origin-Embedder-Policy: require-corp`
  - `Cross-Origin-Opener-Policy: same-origin`
- ✅ 支持多线程 WebAssembly 运行

### 3. 解决图形上下文窗口错误
- ✅ 参考 `osgearth_myviewer_wasm.cpp` 实现
- ✅ 使用 `setUpViewerAsEmbeddedInWindow()` 而不是 `setUpViewInWindow()`
- ✅ 应用 `osgEarth::GLUtils::setGlobalDefaults()` 设置
- ✅ 配置 WebGL 兼容性设置

## 🔧 技术要点

### 编译配置
```bash
em++ -DOSG_GLSL_VERSION=300 -O2 -g \
  -IF:\cmo-dev\my_osgearth_web\osgearth_third_party\wasm_dep\include \
  -I../../src \
  osgearth_myviewer_debug.cpp \
  F:\cmo-dev\my_osgearth_web\osgearth_third_party\wasm_dep\lib\libosg.a \
  F:\cmo-dev\my_osgearth_web\osgearth_third_party\wasm_dep\lib\libosgViewer.a \
  F:\cmo-dev\my_osgearth_web\osgearth_third_party\wasm_dep\lib\libosgDB.a \
  F:\cmo-dev\my_osgearth_web\osgearth_third_party\wasm_dep\lib\libosgGA.a \
  F:\cmo-dev\my_osgearth_web\osgearth_third_party\wasm_dep\lib\libosgUtil.a \
  F:\cmo-dev\my_osgearth_web\osgearth_third_party\wasm_dep\lib\libosgText.a \
  F:\cmo-dev\my_osgearth_web\osgearth_third_party\wasm_dep\lib\libosgShadow.a \
  F:\cmo-dev\my_osgearth_web\osgearth_third_party\wasm_dep\lib\libosgSim.a \
  F:\cmo-dev\my_osgearth_web\osgearth_third_party\wasm_dep\lib\libGeographicLib.a \
  F:\cmo-dev\my_osgearth_web\osgearth_third_party\wasm_dep\lib\libOpenThreads.a \
  ../../redist_wasm/libosgEarth.a \
  -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s WASM=1 -s FETCH=1 \
  -s USE_PTHREADS=1 -s PTHREAD_POOL_SIZE=4 \
  -s INITIAL_MEMORY=268435456 -s ALLOW_MEMORY_GROWTH=1 -s MAXIMUM_MEMORY=2147483648 \
  -s EXPORTED_FUNCTIONS=[_main] \
  -s EXPORTED_RUNTIME_METHODS=[ccall,cwrap,HEAPU8] \
  -s NO_EXIT_RUNTIME=1 -s DISABLE_EXCEPTION_CATCHING=0 \
  -s FORCE_FILESYSTEM=1 -s ENVIRONMENT=web,worker \
  -s GL_ENABLE_GET_PROC_ADDRESS=1 -s GL_UNSAFE_OPTS=0 \
  -s LEGACY_GL_EMULATION=0 -s ASSERTIONS=1 -s GL_DEBUG=1 \
  -o redist_wasm/osgearth_multithread.html
```

### 关键代码修改

#### 1. 多线程设置
```cpp
// 设置线程模型 - 使用多线程支持
g_viewer->setThreadingModel(osgViewer::Viewer::CullDrawThreadPerContext);
g_viewer->setRunFrameScheme(osgViewer::Viewer::CONTINUOUS);
```

#### 2. WebGL 兼容性设置
```cpp
// 应用全局默认设置
osgEarth::GLUtils::setGlobalDefaults(g_viewer->getCamera()->getOrCreateStateSet());

// 使用嵌入式窗口设置，让OSG内部处理WebGL上下文
g_viewer->setUpViewerAsEmbeddedInWindow(0, 0, 800, 600);

// 禁用WebGL不支持的功能
osg::ref_ptr<osg::StateSet> globalStateSet = g_viewer->getCamera()->getOrCreateStateSet();
globalStateSet->setMode(GL_ALPHA_TEST, osg::StateAttribute::OFF);

// 强制使用VBO
osg::DisplaySettings::instance()->setVertexBufferHint(osg::DisplaySettings::VERTEX_BUFFER_OBJECT);

// WebGL纹理渲染优化设置
globalStateSet->setMode(GL_CULL_FACE, osg::StateAttribute::ON);
globalStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);
```

#### 3. WebAssembly 条件编译
```cpp
#ifdef __EMSCRIPTEN__
// WebAssembly 环境下先创建空地图，避免网络问题
DEBUG_LOG("WebAssembly mode: Creating empty map to avoid network issues");
#else
// 桌面版本添加简单的 XYZ 图层
DEBUG_LOG("Desktop mode: Adding XYZ layer");
// ... 添加瓦片图层代码
#endif
```

## 📁 生成的文件

- `osgearth_multithread.html` - 多线程版本主页面
- `osgearth_multithread.js` - JavaScript 运行时
- `osgearth_multithread.wasm` - WebAssembly 二进制文件

## 🚀 测试方法

1. 启动 HTTP 服务器：
   ```bash
   cd redist_wasm
   python run_server.py
   ```

2. 在浏览器中访问：
   ```
   http://localhost:8080/osgearth_multithread.html
   ```

3. 检查浏览器控制台输出，验证：
   - SharedArrayBuffer 是否正常工作
   - 多线程是否成功启动
   - WebGL 上下文是否正确创建
   - osgEarth 地球是否正常显示

## 💡 关键问题解决

### 1. 线程构造失败问题
**问题**：`thread constructor failed: Resource temporarily unavailable`
**解决**：
- 使用正确的多线程编译配置
- 配置 `ENVIRONMENT=web,worker`
- 设置适当的线程池大小

### 2. WebGL 上下文问题
**问题**：`Cannot read properties of undefined (reading 'getError')`
**解决**：
- 使用 `setUpViewerAsEmbeddedInWindow()` 而不是 `setUpViewInWindow()`
- 应用 `GLUtils::setGlobalDefaults()` 设置
- 禁用 WebGL 不支持的 OpenGL 功能

### 3. 窗口系统接口问题
**问题**：`GraphicsContext::WindowingSystemInterfaces::getWindowingSystemInterface() failed`
**解决**：
- 让 OSG 内部处理 WebGL 上下文创建
- 使用嵌入式窗口模式
- 配置正确的显示设置

## 📊 性能配置

- **初始内存**：256MB (`INITIAL_MEMORY=268435456`)
- **最大内存**：2GB (`MAXIMUM_MEMORY=2147483648`)
- **内存增长**：启用 (`ALLOW_MEMORY_GROWTH=1`)
- **线程池大小**：4 (`PTHREAD_POOL_SIZE=4`)

## 🔮 下一步改进

1. **添加瓦片图层**：在 WebAssembly 环境下安全地加载地图瓦片
2. **性能优化**：进一步优化渲染性能和内存使用
3. **错误处理**：增强错误处理和用户反馈机制
4. **功能扩展**：添加更多 osgEarth 功能，如地形、标注等

## 📝 总结

本项目成功解决了 osgEarth WebAssembly 多线程部署的核心技术难题，实现了：
- ✅ 多线程 WebAssembly 编译和运行
- ✅ WebGL 图形上下文正确创建
- ✅ osgEarth 库在浏览器中的基本运行
- ✅ SharedArrayBuffer 支持的 HTTP 服务器配置

这为在 Web 浏览器中部署复杂的 3D 地理信息系统奠定了坚实的技术基础。
