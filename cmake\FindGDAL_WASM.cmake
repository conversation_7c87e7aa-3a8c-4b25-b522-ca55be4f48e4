# FindGDAL.cmake - WebAssembly版本
# 查找GDAL库，支持外部WASM依赖

# WebAssembly外部依赖支持
IF(USE_EXTERNAL_WASM_DEPENDS)
    message(STATUS "Using external WASM dependencies for GDAL")
    
    # 设置GDAL根目录
    if(NOT GDAL_DIR)
        set(GDAL_DIR "$ENV{GDAL_DIR}")
        if(NOT GDAL_DIR)
            message(FATAL_ERROR "GDAL_DIR not set for external WASM dependencies")
        endif()
    endif()
    
    # 设置包含目录
    SET(GDAL_INCLUDE_DIR "${GDAL_DIR}/include")
    
    # 设置库文件路径 - 强制使用静态库
    SET(GDAL_LIBRARY "${GDAL_DIR}/lib/libgdal.a")
    
    # 设置找到标志
    SET(GDAL_FOUND TRUE)
    
    # 设置版本信息
    SET(GDAL_VERSION "3.4.0")  # 假设版本，实际应该从头文件读取
    
    message(STATUS "GDAL WASM library configured:")
    message(STATUS "  GDAL_DIR: ${GDAL_DIR}")
    message(STATUS "  GDAL_INCLUDE_DIR: ${GDAL_INCLUDE_DIR}")
    message(STATUS "  GDAL_LIBRARY: ${GDAL_LIBRARY}")
    
ELSE()
    # 使用vcpkg查找GDAL
    find_package(GDAL REQUIRED)
    
    # 设置变量以保持兼容性
    if(TARGET GDAL::GDAL)
        get_target_property(GDAL_INCLUDE_DIR GDAL::GDAL INTERFACE_INCLUDE_DIRECTORIES)
        set(GDAL_LIBRARY GDAL::GDAL)
    endif()
    
    # 设置找到标志
    SET(GDAL_FOUND TRUE)
    
ENDIF()

# 设置库列表
SET(GDAL_LIBRARIES ${GDAL_LIBRARY})

# 设置包含目录列表
SET(GDAL_INCLUDE_DIRS ${GDAL_INCLUDE_DIR})

# 处理标准参数
include(FindPackageHandleStandardArgs)
FIND_PACKAGE_HANDLE_STANDARD_ARGS(GDAL 
    REQUIRED_VARS GDAL_LIBRARY GDAL_INCLUDE_DIR
    VERSION_VAR GDAL_VERSION
)

# 标记为高级变量
MARK_AS_ADVANCED(
    GDAL_INCLUDE_DIR
    GDAL_LIBRARY
)
