#!/usr/bin/env python3
"""
HTTP 服务器，支持 SharedArrayBuffer 和多线程 WebAssembly
"""

import http.server
import socketserver
import os
import sys
from urllib.parse import urlparse

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # 设置 CORS 头部
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        
        # 设置 SharedArrayBuffer 支持的安全头部
        self.send_header('Cross-Origin-Embedder-Policy', 'require-corp')
        self.send_header('Cross-Origin-Opener-Policy', 'same-origin')
        
        # 设置缓存控制
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        
        super().end_headers()

    def guess_type(self, path):
        """设置正确的 MIME 类型"""
        # 为 WebAssembly 文件设置正确的 MIME 类型
        if path.endswith('.wasm'):
            return 'application/wasm'
        elif path.endswith('.js'):
            return 'application/javascript'
        elif path.endswith('.html'):
            return 'text/html'

        # 使用父类方法
        return super().guess_type(path)

    def do_OPTIONS(self):
        """处理 OPTIONS 请求"""
        self.send_response(200)
        self.end_headers()

def main():
    # 切换到 redist_wasm 目录
    redist_dir = os.path.join(os.getcwd(), 'redist_wasm')
    if os.path.exists(redist_dir):
        os.chdir(redist_dir)
        print(f"服务器目录: {redist_dir}")
    else:
        print(f"警告: redist_wasm 目录不存在，使用当前目录: {os.getcwd()}")
    
    # 设置端口
    port = 8081
    
    # 创建服务器
    with socketserver.TCPServer(("", port), CORSHTTPRequestHandler) as httpd:
        print("=" * 60)
        print("🚀 osgEarth WebAssembly 开发服务器")
        print("=" * 60)
        print(f"📍 服务器地址: http://localhost:{port}")
        print(f"📁 服务器目录: {os.getcwd()}")
        print("🔧 支持特性:")
        print("   ✅ SharedArrayBuffer 支持")
        print("   ✅ 多线程 WebAssembly")
        print("   ✅ CORS 跨域支持")
        print("   ✅ 正确的 MIME 类型")
        print("=" * 60)
        print("📋 可用文件:")
        
        # 列出可用的 HTML 文件
        for file in os.listdir('.'):
            if file.endswith('.html'):
                print(f"   🌐 http://localhost:{port}/{file}")
        
        print("=" * 60)
        print("按 Ctrl+C 停止服务器")
        print("=" * 60)
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 服务器已停止")

if __name__ == "__main__":
    main()
