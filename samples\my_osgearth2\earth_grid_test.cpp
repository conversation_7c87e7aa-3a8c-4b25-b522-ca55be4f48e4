#include <iostream>
#include <emscripten.h>
#include <emscripten/html5.h>
#include <SDL2/SDL.h>
#include <osgEarth/RenderingEngine.h>
#include <cmath>

using namespace osgEarth;

class EarthGridTest
{
public:
    EarthGridTest() : _engine(nullptr), _running(false), _window(nullptr), _glContext(nullptr) {}

    bool initialize()
    {
        std::cout << "🌍 Initializing Earth Grid Test..." << std::endl;

#ifdef __EMSCRIPTEN__
        if (!initializeWebGL())
        {
            std::cerr << "❌ Failed to initialize WebGL context" << std::endl;
            return false;
        }
#endif

        _engine = RenderingEngineFactory::createBest();
        if (!_engine)
        {
            std::cerr << "❌ Failed to create rendering engine" << std::endl;
            return false;
        }

        std::cout << "✅ Created rendering engine: " << _engine->getTypeName() << std::endl;

        if (!_engine->initialize())
        {
            std::cerr << "❌ Failed to initialize rendering engine" << std::endl;
            return false;
        }

        std::cout << "✅ Rendering engine initialized successfully" << std::endl;

        showSupportedFeatures();

        if (!createEarthGridScene())
        {
            std::cerr << "❌ Failed to create earth grid scene" << std::endl;
            return false;
        }

        _running = true;
        return true;
    }

    bool createEarthGridScene()
    {
        std::cout << "🌍 Creating earth grid scene..." << std::endl;

        if (!createEarthGridShader())
        {
            return false;
        }

        if (!createEarthSphereGeometry())
        {
            return false;
        }

        setupCamera();

        std::cout << "✅ Earth grid scene created successfully" << std::endl;
        return true;
    }

    bool createEarthGridShader()
    {
        ShaderDesc shader;
        shader.name = "EarthGridShader";

        shader.vertexSource = R"(#version 300 es
precision highp float;

layout(location = 0) in vec3 a_position;
layout(location = 1) in vec3 a_normal;
layout(location = 2) in vec2 a_texCoord;

uniform mat4 u_projectionMatrix;
uniform mat4 u_viewMatrix;
uniform mat4 u_modelMatrix;

out vec3 v_worldPos;
out vec3 v_normal;
out vec2 v_texCoord;

void main() {
    vec4 worldPos = u_modelMatrix * vec4(a_position, 1.0);
    v_worldPos = worldPos.xyz;
    v_normal = normalize((u_modelMatrix * vec4(a_normal, 0.0)).xyz);
    v_texCoord = a_texCoord;
    
    mat4 mvp = u_projectionMatrix * u_viewMatrix * u_modelMatrix;
    gl_Position = mvp * vec4(a_position, 1.0);
}
)";

        shader.fragmentSource = R"(#version 300 es
precision highp float;

in vec3 v_worldPos;
in vec3 v_normal;
in vec2 v_texCoord;

out vec4 fragColor;

void main() {
    // 计算经纬度
    float lon = atan(v_worldPos.x, v_worldPos.z) * 180.0 / 3.14159;
    float lat = asin(v_worldPos.y / length(v_worldPos)) * 180.0 / 3.14159;
    
    // 经纬度网格线
    float gridLon = abs(fract(lon / 15.0) - 0.5) * 2.0;  // 15度间隔
    float gridLat = abs(fract(lat / 15.0) - 0.5) * 2.0;  // 15度间隔
    float grid = min(gridLon, gridLat);
    
    // 基础地球颜色
    vec3 oceanColor = vec3(0.1, 0.3, 0.8);  // 海洋蓝
    vec3 landColor = vec3(0.2, 0.6, 0.2);   // 陆地绿
    
    // 简化的陆地分布（基于纬度和经度）
    float landMask = 0.0;
    if (abs(lat) < 60.0) {  // 主要陆地在南北纬60度之间
        if (lon > -30.0 && lon < 60.0 && lat > 10.0 && lat < 70.0) landMask = 1.0;  // 欧亚大陆
        if (lon > -130.0 && lon < -60.0 && lat > 25.0 && lat < 70.0) landMask = 1.0; // 北美
        if (lon > -80.0 && lon < -35.0 && lat > -55.0 && lat < 15.0) landMask = 1.0;  // 南美
        if (lon > 110.0 && lon < 155.0 && lat > -45.0 && lat < -10.0) landMask = 1.0; // 澳洲
    }
    
    vec3 earthColor = mix(oceanColor, landColor, landMask);
    
    // 网格线颜色
    vec3 gridColor = vec3(1.0, 1.0, 1.0);
    float gridWidth = 0.03;
    
    if (grid < gridWidth) {
        earthColor = mix(earthColor, gridColor, 0.7);
    }
    
    // 赤道和本初子午线高亮
    if (abs(lat) < 1.0 || abs(lon) < 1.0) {
        earthColor = mix(earthColor, vec3(1.0, 0.0, 0.0), 0.5);  // 红色高亮
    }
    
    // 简单光照
    vec3 lightDir = normalize(vec3(1.0, 1.0, 1.0));
    float NdotL = max(dot(v_normal, lightDir), 0.3);
    
    fragColor = vec4(earthColor * NdotL, 1.0);
}
)";

        _earthShaderId = _engine->createShaderProgram(shader);
        if (_earthShaderId == 0)
        {
            std::cerr << "❌ Failed to create earth grid shader" << std::endl;
            return false;
        }

        std::cout << "✅ Created earth grid shader: " << _earthShaderId << std::endl;
        return true;
    }

    bool createEarthSphereGeometry()
    {
        GeometryDesc geom;
        geom.primitiveType = GL_TRIANGLES;

        const int segments = 64; // 更高精度的球体
        const float radius = 1.0f;

        // 生成球体顶点
        for (int lat = 0; lat <= segments; ++lat)
        {
            float theta = lat * M_PI / segments;
            float sinTheta = sin(theta);
            float cosTheta = cos(theta);

            for (int lon = 0; lon <= segments; ++lon)
            {
                float phi = lon * 2 * M_PI / segments;
                float sinPhi = sin(phi);
                float cosPhi = cos(phi);

                float x = radius * sinTheta * cosPhi;
                float y = radius * cosTheta;
                float z = radius * sinTheta * sinPhi;

                // 添加顶点位置
                geom.vertices.push_back(osg::Vec3(x, y, z));

                // 添加法线（归一化位置向量）
                geom.normals.push_back(osg::Vec3(x, y, z));

                // 添加纹理坐标
                geom.texCoords.push_back(osg::Vec2((float)lon / segments, (float)lat / segments));
            }
        }

        // 生成球体索引
        for (int lat = 0; lat < segments; ++lat)
        {
            for (int lon = 0; lon < segments; ++lon)
            {
                int current = lat * (segments + 1) + lon;
                int next = current + segments + 1;

                geom.indices.push_back(current);
                geom.indices.push_back(next);
                geom.indices.push_back(current + 1);

                geom.indices.push_back(current + 1);
                geom.indices.push_back(next);
                geom.indices.push_back(next + 1);
            }
        }

        _earthGeometryId = _engine->createGeometry(geom);
        if (_earthGeometryId == 0)
        {
            std::cerr << "❌ Failed to create earth geometry" << std::endl;
            return false;
        }

        std::cout << "✅ Created earth geometry: " << _earthGeometryId
                  << " (" << geom.vertices.size() << " vertices, " << geom.indices.size() << " indices)" << std::endl;
        return true;
    }

    void setupCamera()
    {
        // 透视投影
        float fov = 45.0f;
        float aspect = 800.0f / 600.0f;
        float near = 0.1f;
        float far = 100.0f;

        osg::Matrix projection;
        projection.makePerspective(fov, aspect, near, far);
        _engine->setProjectionMatrix(projection);

        // 视图矩阵（从太空观察地球）
        osg::Vec3 eye(0, 0, 2.5);
        osg::Vec3 center(0, 0, 0);
        osg::Vec3 up(0, 1, 0);

        osg::Matrix view;
        view.makeLookAt(eye, center, up);
        _engine->setViewMatrix(view);

        // 地球自转
        static float rotation = 0.0f;
        rotation += 0.3f;

        osg::Matrix model;
        model.makeRotate(osg::DegreesToRadians(rotation), osg::Vec3(0, 1, 0));
        _engine->setModelMatrix(model);
    }

    void renderFrame()
    {
        if (!_running || !_engine)
            return;

        _engine->beginFrame();
        _engine->clear(osg::Vec4(0.0f, 0.0f, 0.1f, 1.0f)); // 深空背景

        setupCamera();

        if (_earthGeometryId > 0 && _earthShaderId > 0)
        {
            _engine->renderGeometry(_earthGeometryId, _earthShaderId);
        }

        _engine->endFrame();

        // 显示统计
        static int frameCount = 0;
        if (++frameCount % 60 == 0)
        {
            const auto &stats = _engine->getStats();
            std::cout << "🌍 Earth Grid Stats: " << stats.drawCalls << " draws, "
                      << stats.triangles << " triangles" << std::endl;
        }
    }

private:
#ifdef __EMSCRIPTEN__
    bool initializeWebGL()
    {
        std::cout << "🌐 Initializing WebGL..." << std::endl;

        if (SDL_Init(SDL_INIT_VIDEO) < 0)
            return false;

        SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 3);
        SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 0);
        SDL_GL_SetAttribute(SDL_GL_CONTEXT_PROFILE_MASK, SDL_GL_CONTEXT_PROFILE_ES);
        SDL_GL_SetAttribute(SDL_GL_DOUBLEBUFFER, 1);
        SDL_GL_SetAttribute(SDL_GL_DEPTH_SIZE, 24);

        _window = SDL_CreateWindow("Earth Grid Test",
                                   SDL_WINDOWPOS_CENTERED, SDL_WINDOWPOS_CENTERED,
                                   800, 600, SDL_WINDOW_OPENGL | SDL_WINDOW_SHOWN);
        if (!_window)
            return false;

        _glContext = SDL_GL_CreateContext(_window);
        if (!_glContext)
            return false;

        std::cout << "✅ WebGL context created" << std::endl;
        return true;
    }
#endif

    void showSupportedFeatures()
    {
        std::cout << std::endl
                  << "🌍 Earth Grid Features:" << std::endl;
        std::cout << "  sphere_geometry: ✅ Supported" << std::endl;
        std::cout << "  lat_lon_grid: ✅ Supported" << std::endl;
        std::cout << "  land_ocean: ✅ Supported" << std::endl;
        std::cout << "  rotation: ✅ Supported" << std::endl;
        std::cout << "  lighting: ✅ Supported" << std::endl;
        std::cout << std::endl;
    }

    std::unique_ptr<IRenderingEngine> _engine;
    bool _running;
    unsigned int _earthShaderId = 0;
    unsigned int _earthGeometryId = 0;

#ifdef __EMSCRIPTEN__
    SDL_Window *_window;
    SDL_GLContext _glContext;
#endif
};

EarthGridTest *g_earthTest = nullptr;

void emscripten_main_loop()
{
    if (g_earthTest)
    {
        g_earthTest->renderFrame();
    }
}

int main()
{
    std::cout << "🌍 osgEarth Earth Grid WebGL Test" << std::endl;
    std::cout << "=================================" << std::endl;

    g_earthTest = new EarthGridTest();

    if (!g_earthTest->initialize())
    {
        std::cerr << "❌ Failed to initialize earth grid test" << std::endl;
        delete g_earthTest;
        return -1;
    }

#ifdef __EMSCRIPTEN__
    std::cout << "🌍 Starting earth grid render loop..." << std::endl;
    emscripten_set_main_loop(emscripten_main_loop, 60, 1);
    SDL_GL_SetSwapInterval(1);
#endif

    return 0;
}
