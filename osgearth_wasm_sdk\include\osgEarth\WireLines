/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/Common>
#include <osgEarth/OsgGeometryOperator>
#include <osgEarth/Stroke>
#include <osg/Array>
#include <osg/Geometry>

// Make a antialiased 3d wire.


namespace osgEarth
{

    class OSGEARTH_EXPORT WireLinesOperator : public Util::OsgGeometryOperator
    {
    public:
        WireLinesOperator(const Stroke& stroke);
        /**
         * Run the polygonizer.
         *
         * @param[in ] verts    Line string geometry to polygonize. The polygonizer
         *                      will add this array to the resulting geometry.
         * @param[in ] normals  Localized normals associated with the input verts.
         *                      Used to determine the plane in which to polygonize each
         *                      line segment. Optional; can be NULL
         * @return Triangulated wire
         */
        osg::Geometry* operator() (osg::Vec3Array* verts, osg::Vec3Array* normals, float width_UNUSED, Callback* callback =0L, bool twosided =true) const;
        void installShaders(osg::Node* node) const;

        const static int numWireVerts = 8;
    protected:
        Stroke _stroke;
    };
}
