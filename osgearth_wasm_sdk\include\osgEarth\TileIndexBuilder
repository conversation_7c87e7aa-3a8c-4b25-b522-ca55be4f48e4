/* osgEarth
* Copyright 2025 Pelican Mapping
* MIT License
*/

#ifndef OSGEARTHUTIL_TILEINDEXBUILDER_H
#define OSGEARTHUTIL_TILEINDEXBUILDER_H 1

#include <osgEarth/Common>

#include <osgEarth/TileIndex>

namespace osgEarth { namespace Contrib
{    
	/**
	 * Utility class for buildling a TileIndex shapefile.
	 */
	class OSGEARTH_EXPORT TileIndexBuilder : public osg::Referenced
	{
	public:

		/**
		 * Creates a new TileIndexBuilder
		 */
		TileIndexBuilder();

		/**
		 * Sets the progress callback
		 */
		void setProgressCallback( osgEarth::ProgressCallback* progress );

		/**
		 * Gets the list of filenames to process.  If you pass in a directory name
		 * it will recursively try all the files within the directory and it's subdirectories.
		 */
		std::vector< std::string >& getFilenames() { return _filenames; }

		/**
		 * Builds the TileIndex
		 * @param indexFilename
		 *    The filename of the index shapefile to create.
		 * @param srs
		 *    The SRS to use for the output shapefile.  Default is epsg:4326
		 */
		void build(const std::string& indexFilename, const osgEarth::SpatialReference* srs = 0);


	protected:

		void expandFilenames();

		std::string _indexFilename;
		std::vector< std::string > _filenames;
		std::vector< std::string > _expandedFilenames;

		osg::ref_ptr<ProgressCallback> _progress;    
	};

} } // namespace osgEarth::Util

#endif //OSGEARTHUTIL_TILEINDEXBUILDER_H
