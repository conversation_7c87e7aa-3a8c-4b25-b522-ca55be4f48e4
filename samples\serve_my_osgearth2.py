#!/usr/bin/env python3
"""
简单的 HTTP 服务器，用于测试 my_osgearth2 WebAssembly 项目
支持 CORS 和 SharedArrayBuffer 所需的头部
"""

import http.server
import socketserver
import os
import sys
from pathlib import Path

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # 添加 CORS 头部
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        
        # 添加 SharedArrayBuffer 支持的头部（多线程 WebAssembly 必需）
        self.send_header('Cross-Origin-Embedder-Policy', 'require-corp')
        self.send_header('Cross-Origin-Opener-Policy', 'same-origin')

        # 添加多线程支持的额外头部
        self.send_header('Cross-Origin-Resource-Policy', 'cross-origin')
        
        # 缓存控制
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        
        super().end_headers()

    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

def main():
    # 切换到 redist_all_samples 目录
    samples_dir = Path(__file__).parent
    redist_dir = samples_dir / "redist_all_samples"
    
    if not redist_dir.exists():
        print(f"❌ 错误: 目录 {redist_dir} 不存在")
        print("请先运行构建脚本生成 WebAssembly 文件")
        sys.exit(1)
    
    os.chdir(redist_dir)
    
    # 检查必要的文件
    required_files = ["my_osgearth2.html", "my_osgearth2.js", "my_osgearth2.wasm"]
    missing_files = [f for f in required_files if not Path(f).exists()]
    
    if missing_files:
        print(f"❌ 错误: 缺少文件: {', '.join(missing_files)}")
        print("请先运行构建脚本生成这些文件")
        sys.exit(1)
    
    # 启动服务器
    PORT = 8080
    
    print("=" * 60)
    print("🌐 my_osgearth2 WebAssembly 测试服务器")
    print("=" * 60)
    print(f"📂 服务目录: {redist_dir}")
    print(f"🔗 访问地址: http://localhost:{PORT}/my_osgearth2.html")
    print("=" * 60)
    print("📋 项目特点:")
    print("  ✅ osgEarth 三维数字地球")
    print("  ✅ SDL2 窗口和交互")
    print("  ✅ 鼠标拖拽旋转地球")
    print("  ✅ 滚轮缩放")
    print("  ✅ ESC 键退出")
    print("  ✅ 深蓝色太空背景")
    print("  ✅ 异常处理和备用方案")
    print("=" * 60)
    print("🎮 操作说明:")
    print("  • 鼠标左键拖拽: 旋转地球")
    print("  • 鼠标滚轮: 缩放")
    print("  • ESC 键: 退出程序")
    print("=" * 60)
    print("🔧 技术细节:")
    print("  • WebGL 2.0 渲染")
    print("  • 单线程稳定配置")
    print("  • 512MB-2GB 内存配置")
    print("  • 完整的 osgEarth 支持")
    print("=" * 60)
    print("按 Ctrl+C 停止服务器")
    print("=" * 60)
    
    try:
        with socketserver.TCPServer(("", PORT), CORSHTTPRequestHandler) as httpd:
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器错误: {e}")

if __name__ == "__main__":
    main()
