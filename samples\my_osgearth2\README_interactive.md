# osgEarth WebAssembly 交互式数字地球

## 项目概述

这是一个基于 osgEarth 的 WebAssembly 数字地球应用程序，支持鼠标交互和实时渲染。该应用程序展示了如何在 Web 浏览器中运行完整的 osgEarth 3D 地球场景。

## 主要功能

### 🌍 数字地球渲染
- 使用 osgEarth 库创建真实的 3D 地球模型
- 支持球面几何体和纹理贴图
- 实时 WebGL 渲染

### 🖱️ 鼠标交互
- **左键拖拽**: 旋转地球视角
- **右键拖拽**: 缩放地球距离
- **鼠标滚轮**: 快速缩放

### 🎮 相机控制
- 自由视角控制
- 平滑的相机运动
- 视角限制保护

### 🔧 技术特性
- 多线程 WebAssembly 支持
- SharedArrayBuffer 优化
- WebGL 2.0 渲染
- 实时着色器渲染

## 文件结构

```
redist_wasm/
├── osgearth_interactive_final.html    # 主应用程序页面
├── osgearth_interactive_final.js      # JavaScript 运行时
├── osgearth_interactive_final.wasm    # WebAssembly 二进制文件
├── server.py                          # HTTP 服务器
└── README_interactive.md              # 本说明文档
```

## 运行方法

### 1. 启动服务器
```bash
cd redist_wasm
python server.py
```

### 2. 打开浏览器
访问: http://localhost:8080/osgearth_interactive_final.html

### 3. 使用说明
- 等待 WebAssembly 模块加载完成
- 使用鼠标与地球进行交互
- 观察控制台输出了解加载进度

## 技术架构

### 渲染引擎
- **osgEarth**: 地球渲染核心
- **OpenSceneGraph**: 3D 场景图
- **WebGL 2.0**: 硬件加速渲染

### 交互系统
- **SDL2**: 事件处理
- **自定义鼠标状态管理**: 平滑交互
- **相机控制系统**: 视角管理

### 着色器系统
- **顶点着色器**: 几何变换
- **片段着色器**: 像素渲染
- **纹理支持**: 地表贴图

## 编译参数

```bash
em++ -DOSG_GLSL_VERSION=300 -O2 -g \
  -IF:\cmo-dev\my_osgearth_web\osgearth_third_party\wasm_dep\include \
  -I../../src \
  osgearth_myviewer_with_engine.cpp \
  [库文件...] \
  -s USE_SDL=2 \
  -s USE_WEBGL2=1 \
  -s FULL_ES3=1 \
  -s WASM=1 \
  -s FETCH=1 \
  -s USE_PTHREADS=1 \
  -s PTHREAD_POOL_SIZE=8 \
  -s INITIAL_MEMORY=268435456 \
  -s ALLOW_MEMORY_GROWTH=1 \
  -s MAXIMUM_MEMORY=2147483648 \
  -o redist_wasm/osgearth_interactive_final.html
```

## 浏览器要求

- **Chrome/Edge**: 推荐，完整支持
- **Firefox**: 支持，需要启用 SharedArrayBuffer
- **Safari**: 部分支持

## 性能优化

- 多线程处理提升性能
- 内存动态增长
- WebGL 2.0 硬件加速
- 优化的着色器代码

## 故障排除

### 常见问题
1. **页面空白**: 检查浏览器控制台错误
2. **加载缓慢**: 等待 WebAssembly 模块下载
3. **交互无响应**: 确认鼠标事件正常

### 调试方法
- 打开浏览器开发者工具
- 查看控制台日志输出
- 检查网络请求状态

## 开发说明

### 源代码位置
- 主程序: `osgearth_myviewer_with_engine.cpp`
- 编译脚本: 见上述编译参数

### 扩展功能
- 可添加谷歌地图瓦片图层
- 支持更多交互方式
- 可集成更多地理数据

## 版本信息

- **osgEarth**: 2.10.1
- **OpenSceneGraph**: 3.6.x
- **Emscripten**: 最新版本
- **WebGL**: 2.0

---

🎉 **恭喜！** 您已经成功运行了 osgEarth WebAssembly 交互式数字地球应用程序！
