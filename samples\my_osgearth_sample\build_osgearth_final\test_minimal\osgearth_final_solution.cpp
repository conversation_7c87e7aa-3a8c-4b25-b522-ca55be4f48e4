// osgEarth 最终解决方案 - 基于成功的 simple_osg_wasm 模式
#include <iostream>
#include <memory>

// OSG 核心头文件
#include <osg/ref_ptr>
#include <osg/Group>
#include <osg/Geode>
#include <osg/Geometry>
#include <osg/Vec3>
#include <osg/Vec4>
#include <osg/StateSet>
// 不使用 Sphere，改用基本几何体

// OSG 查看器
#include <osgViewer/Viewer>
#include <osgGA/TrackballManipulator>

// osgEarth 头文件
#include <osgEarth/Map>
#include <osgEarth/MapNode>
#include <osgEarth/ImageLayer>

// SDL2 和 Emscripten
#include <SDL2/SDL.h>
#include <emscripten.h>
#include <emscripten/html5.h>

using namespace osg;
using namespace osgEarth;

// Emscripten GraphicsContext实现
class EmscriptenGraphicsContext : public osg::GraphicsContext
{
public:
    EmscriptenGraphicsContext(Traits *traits) : osg::GraphicsContext()
    {
        _traits = traits;
        _valid = true;
        _realized = false;

        setState(new osg::State);
        getState()->setGraphicsContext(this);
    }

    virtual bool valid() const { return _valid; }
    virtual bool realizeImplementation()
    {
        _realized = true;
        return true;
    }
    virtual bool isRealizedImplementation() const { return _realized; }
    virtual void closeImplementation() { _realized = false; }
    virtual bool makeCurrentImplementation() { return true; }
    virtual bool makeContextCurrentImplementation(GraphicsContext *readContext) { return true; }
    virtual bool releaseContextImplementation() { return true; }
    virtual void swapBuffersImplementation()
    {
        if (s_window)
            SDL_GL_SwapWindow(s_window);
    }
    virtual void bindPBufferToTextureImplementation(GLenum buffer) {}
    virtual void resizedImplementation(int x, int y, int width, int height) {}

    static void setWindow(SDL_Window *window) { s_window = window; }

private:
    bool _valid;
    bool _realized;
    static SDL_Window *s_window;
};

SDL_Window *EmscriptenGraphicsContext::s_window = nullptr;

// 全局变量
osg::ref_ptr<osgViewer::Viewer> g_viewer;
SDL_Window *g_window = nullptr;
SDL_GLContext g_glContext = nullptr;

// 创建简单的地球场景（避免复杂的网络加载）
osg::ref_ptr<osg::Node> createSimpleEarthScene()
{
    std::cout << "[DEBUG] Creating simple earth scene..." << std::endl;

    try
    {
        // 方案1：如果 osgEarth 有问题，先用简单的三角形代替
        osg::ref_ptr<osg::Group> root = new osg::Group();

        // 创建一个简单的三角形作为占位符
        osg::ref_ptr<osg::Geode> geode = new osg::Geode();
        osg::ref_ptr<osg::Geometry> geometry = new osg::Geometry();

        // 顶点数组
        osg::ref_ptr<osg::Vec3Array> vertices = new osg::Vec3Array();
        vertices->push_back(osg::Vec3(-1.0f, 0.0f, 0.0f));
        vertices->push_back(osg::Vec3(1.0f, 0.0f, 0.0f));
        vertices->push_back(osg::Vec3(0.0f, 1.0f, 0.0f));
        geometry->setVertexArray(vertices.get());

        // 颜色数组
        osg::ref_ptr<osg::Vec4Array> colors = new osg::Vec4Array();
        colors->push_back(osg::Vec4(0.2f, 0.5f, 0.8f, 1.0f)); // 蓝绿色
        geometry->setColorArray(colors.get());
        geometry->setColorBinding(osg::Geometry::BIND_OVERALL);

        // 添加图元
        geometry->addPrimitiveSet(new osg::DrawArrays(osg::PrimitiveSet::TRIANGLES, 0, 3));

        geode->addDrawable(geometry.get());
        root->addChild(geode.get());

        // 尝试创建 osgEarth 场景
        try
        {
            std::cout << "[DEBUG] Attempting to create osgEarth Map..." << std::endl;

            // 创建空的地图（不添加任何图层）
            osg::ref_ptr<Map> map = new Map();

            std::cout << "[DEBUG] Creating MapNode..." << std::endl;

            // 创建地图节点
            osg::ref_ptr<MapNode> mapNode = new MapNode(map.get());

            std::cout << "[DEBUG] osgEarth MapNode created successfully" << std::endl;

            // 如果成功，返回 MapNode
            return mapNode.get();
        }
        catch (const std::exception &e)
        {
            std::cout << "[WARNING] osgEarth creation failed: " << e.what() << std::endl;
            std::cout << "[INFO] Falling back to simple triangle" << std::endl;
        }

        // 如果 osgEarth 失败，返回简单三角形
        std::cout << "[DEBUG] Simple earth scene created successfully" << std::endl;
        return root.get();
    }
    catch (const std::exception &e)
    {
        std::cout << "[ERROR] Exception in createSimpleEarthScene: " << e.what() << std::endl;
        return nullptr;
    }
}

// 初始化SDL2
bool initializeSDL2()
{
    std::cout << "[DEBUG] Initializing SDL2..." << std::endl;

    if (SDL_Init(SDL_INIT_VIDEO) < 0)
    {
        std::cout << "[ERROR] SDL initialization failed: " << SDL_GetError() << std::endl;
        return false;
    }

    // 使用与 simple_osg_wasm 相同的设置
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 3);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 0);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_PROFILE_MASK, SDL_GL_CONTEXT_PROFILE_ES);
    SDL_GL_SetAttribute(SDL_GL_DOUBLEBUFFER, 1);
    SDL_GL_SetAttribute(SDL_GL_DEPTH_SIZE, 24);

    g_window = SDL_CreateWindow(
        "osgEarth Final Solution",
        SDL_WINDOWPOS_CENTERED,
        SDL_WINDOWPOS_CENTERED,
        1024, 768,
        SDL_WINDOW_OPENGL | SDL_WINDOW_SHOWN);

    if (!g_window)
    {
        std::cout << "[ERROR] Window creation failed: " << SDL_GetError() << std::endl;
        return false;
    }

    g_glContext = SDL_GL_CreateContext(g_window);
    if (!g_glContext)
    {
        std::cout << "[ERROR] OpenGL context creation failed: " << SDL_GetError() << std::endl;
        return false;
    }

    SDL_GL_MakeCurrent(g_window, g_glContext);
    EmscriptenGraphicsContext::setWindow(g_window);

    std::cout << "[DEBUG] SDL2 initialized successfully" << std::endl;
    return true;
}

// 初始化OSG查看器
bool initializeOSGViewer()
{
    std::cout << "[DEBUG] Initializing OSG Viewer..." << std::endl;

    g_viewer = new osgViewer::Viewer();

    osg::ref_ptr<osg::GraphicsContext::Traits> traits = new osg::GraphicsContext::Traits();
    traits->x = 0;
    traits->y = 0;
    traits->width = 1024;
    traits->height = 768;
    traits->windowDecoration = false;
    traits->doubleBuffer = true;
    traits->sharedContext = 0;
    traits->pbuffer = false;

    osg::ref_ptr<EmscriptenGraphicsContext> gc = new EmscriptenGraphicsContext(traits.get());

    if (gc.valid())
    {
        g_viewer->getCamera()->setGraphicsContext(gc.get());
        g_viewer->getCamera()->setViewport(new osg::Viewport(0, 0, traits->width, traits->height));
        g_viewer->getCamera()->setProjectionMatrixAsPerspective(30.0f, (double)traits->width / (double)traits->height, 1.0f, 100.0f);

        std::cout << "[DEBUG] Graphics context created successfully" << std::endl;
    }
    else
    {
        std::cerr << "[ERROR] Failed to create graphics context" << std::endl;
        return false;
    }

    // 使用简单的轨迹球操作器
    osg::ref_ptr<osgGA::TrackballManipulator> manipulator = new osgGA::TrackballManipulator();
    g_viewer->setCameraManipulator(manipulator.get());

    std::cout << "[DEBUG] OSG Viewer initialized successfully" << std::endl;
    return true;
}

// 主循环函数
void mainLoop()
{
    if (g_viewer.valid() && !g_viewer->done())
    {
        g_viewer->frame();
    }
}

int main()
{
    std::cout << "[DEBUG] Starting osgEarth Final Solution..." << std::endl;

    if (!initializeSDL2())
    {
        std::cout << "[ERROR] Failed to initialize SDL2" << std::endl;
        return -1;
    }

    if (!initializeOSGViewer())
    {
        std::cout << "[ERROR] Failed to initialize OSG Viewer" << std::endl;
        return -1;
    }

    // 创建场景
    osg::ref_ptr<osg::Node> scene = createSimpleEarthScene();
    if (!scene.valid())
    {
        std::cout << "[ERROR] Failed to create earth scene" << std::endl;
        return -1;
    }

    g_viewer->setSceneData(scene.get());
    g_viewer->realize();

    std::cout << "[DEBUG] Starting main loop..." << std::endl;
    emscripten_set_main_loop(mainLoop, 0, 1);

    return 0;
}
