# OSGEarth WebAssembly 所有示例项目构建
cmake_minimum_required(VERSION 3.10)
project(osgearth_all_samples)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 确保我们使用 Emscripten 编译
if(EMSCRIPTEN)
    message(STATUS "OSGEarth WebAssembly 多线程构建 - 所有示例项目")
    
    # 库路径配置
    set(OSG_WASM_LIB_DIR "F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep")
    set(OSGEARTH_WASM_LIB_DIR "F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/redist_wasm")
    set(OSGEARTH_SOURCE_DIR "F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/src")
    
    # 通用包含头文件目录
    include_directories(
        "${OSG_WASM_LIB_DIR}/include"
        "${OSGEARTH_SOURCE_DIR}"
    )
    
    # 通用编译器标志
    set(COMMON_COMPILE_FLAGS
        -DOSG_GLSL_VERSION=300
        -DOSGEARTH_HAVE_GEOS=1
        -DOSGEARTH_HAVE_GDAL=0
        -DOSGEARTH_HAVE_PROJ=1
        -DUSE_EXTERNAL_WASM_DEPENDS=ON
        -DSTB_IMAGE_IMPLEMENTATION
        -DSTBI_NO_STDIO
        -DSTBI_NO_FAILURE_STRINGS
        -DSTBI_ONLY_PNG
        -DSTBI_ONLY_JPEG
        -pthread
        -matomics
        -mbulk-memory
        -O3
    )
    
    # 核心库文件
    set(CORE_LIBS
        "${OSGEARTH_WASM_LIB_DIR}/libosgEarth.a"
        "${OSGEARTH_WASM_LIB_DIR}/osgdb_earth.a"
        "${OSGEARTH_WASM_LIB_DIR}/osgdb_osgearth_engine_rex.a"
        "${OSG_WASM_LIB_DIR}/lib/libosg.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgViewer.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgDB.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgGA.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgUtil.a"
        "${OSG_WASM_LIB_DIR}/lib/libOpenThreads.a"
    )
    
    # 第三方库
    set(THIRD_PARTY_LIBS
        "${OSG_WASM_LIB_DIR}/lib/libgeos.a"
        "${OSG_WASM_LIB_DIR}/lib/libproj.a"
        "${OSG_WASM_LIB_DIR}/lib/libcurl.a"
        "${OSG_WASM_LIB_DIR}/lib/libGeographicLib.a"
        "${OSG_WASM_LIB_DIR}/lib/libz.a"
        "${OSG_WASM_LIB_DIR}/lib/libexpat.a"
        "${OSG_WASM_LIB_DIR}/lib/libpng16.a"
        "${OSG_WASM_LIB_DIR}/lib/libjpeg.a"
    )
    
    # 通用链接器标志 - 多线程版本，修复 SharedArrayBuffer 问题
    set(COMMON_LINK_FLAGS 
        "-s USE_SDL=2"
        "-s USE_WEBGL2=1" 
        "-s FULL_ES3=1" 
        "-s WASM=1"
        "-s INITIAL_MEMORY=1073741824"
        "-s ALLOW_MEMORY_GROWTH=1"
        "-s MAXIMUM_MEMORY=4294967296"
        "-s FETCH=1"
        "-s ASYNCIFY=1"
        "-s EXPORTED_FUNCTIONS=['_main']"
        "-s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','HEAPU8']"
        "-s NO_EXIT_RUNTIME=1"
        "-s DISABLE_EXCEPTION_CATCHING=0"
        "-s FORCE_FILESYSTEM=1"
        "-s USE_PTHREADS=1"
        "-s PTHREAD_POOL_SIZE=4"
        "-s SHARED_MEMORY=1"
        "-s PROXY_TO_PTHREAD=1"
        "-s OFFSCREENCANVAS_SUPPORT=1"
        "-s ENVIRONMENT=web,worker"
        "-pthread"
        "-matomics"
        "-mbulk-memory"
        "-O3"
    )
    
    string(REPLACE ";" " " COMMON_LINK_FLAGS_STR "${COMMON_LINK_FLAGS}")
    
    # ========================================
    # 项目 1: my_osg_sample (OSG 基础示例)
    # ========================================
    message(STATUS "配置项目 1: my_osg_sample")
    
    add_executable(my_osg_sample
        ../my_osg_sample/main.cpp
        ../my_osg_sample/TileTextureManager.cpp
        ../my_osg_sample/TileKey.cpp
    )
    
    target_include_directories(my_osg_sample PRIVATE
        "${CMAKE_CURRENT_SOURCE_DIR}/../my_osg_sample"
    )
    
    target_compile_options(my_osg_sample PRIVATE ${COMMON_COMPILE_FLAGS})
    target_link_libraries(my_osg_sample ${CORE_LIBS} ${THIRD_PARTY_LIBS})
    
    set_target_properties(my_osg_sample PROPERTIES
        SUFFIX ".html"
        LINK_FLAGS "${COMMON_LINK_FLAGS_STR}"
    )
    
    # ========================================
    # 项目 2: my_osg_sample2 (OSG 高级示例)
    # ========================================
    message(STATUS "配置项目 2: my_osg_sample2")
    
    add_executable(my_osg_sample2
        ../my_osg_sample2/main.cpp
        ../my_osg_sample2/SpatialReference.cpp
        ../my_osg_sample2/TileSystem.cpp
    )

    target_include_directories(my_osg_sample2 PRIVATE
        "${CMAKE_CURRENT_SOURCE_DIR}/../my_osg_sample2"
    )
    
    target_compile_options(my_osg_sample2 PRIVATE ${COMMON_COMPILE_FLAGS})
    target_link_libraries(my_osg_sample2 ${CORE_LIBS} ${THIRD_PARTY_LIBS})
    
    set_target_properties(my_osg_sample2 PROPERTIES
        SUFFIX ".html"
        LINK_FLAGS "${COMMON_LINK_FLAGS_STR}"
    )
    
    # ========================================
    # 项目 3: my_osgearth_sample (osgEarth 完整示例) - 已修复编译错误
    # ========================================
    message(STATUS "配置项目 3: my_osgearth_sample")

    add_executable(my_osgearth_sample
        ../my_osgearth_sample/main.cpp
    )

    target_include_directories(my_osgearth_sample PRIVATE
        "${CMAKE_CURRENT_SOURCE_DIR}/../my_osgearth_sample"
    )

    target_compile_options(my_osgearth_sample PRIVATE ${COMMON_COMPILE_FLAGS})
    target_link_libraries(my_osgearth_sample ${CORE_LIBS} ${THIRD_PARTY_LIBS})

    set_target_properties(my_osgearth_sample PROPERTIES
        SUFFIX ".html"
        LINK_FLAGS "${COMMON_LINK_FLAGS_STR}"
    )
    
    # ========================================
    # 构建信息
    # ========================================
    message(STATUS "")
    message(STATUS "========================================")
    message(STATUS "OSGEarth WebAssembly 多线程构建配置")
    message(STATUS "========================================")
    message(STATUS "将构建以下项目:")
    message(STATUS "  1. my_osg_sample.html      - OSG 基础示例")
    message(STATUS "  2. my_osg_sample2.html     - OSG 高级示例")
    message(STATUS "  3. my_osgearth_sample.html - osgEarth 完整示例")
    message(STATUS "")
    message(STATUS "多线程配置:")
    message(STATUS "  - 初始内存: 1GB")
    message(STATUS "  - 最大内存: 4GB")
    message(STATUS "  - 线程池: 4 线程")
    message(STATUS "  - SharedArrayBuffer: 启用")
    message(STATUS "  - 跨域隔离: 需要")
    message(STATUS "")
    message(STATUS "使用 serve_with_headers.py 启动服务器以支持多线程")
    message(STATUS "========================================")
    
else()
    message(FATAL_ERROR "此 CMakeLists.txt 仅支持 Emscripten")
endif()
