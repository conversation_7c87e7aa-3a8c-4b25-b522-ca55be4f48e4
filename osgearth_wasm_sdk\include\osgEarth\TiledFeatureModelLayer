/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/Common>
#include <osgEarth/FeatureSource>
#include <osgEarth/GeometryCompiler>
#include <osgEarth/FeatureModelSource>
#include <osgEarth/Session>
#include <osgEarth/Style>
#include <osgEarth/Layer>
#include <osgEarth/LayerReference>
#include <osgEarth/TiledModelLayer>

namespace osgEarth {
    class Map;
}

namespace osgEarth
{
    class FeatureNodeFactory;

    /**
     * Layer that creates a tiled scene graph from feature data and symbology.
     */
    class OSGEARTH_EXPORT TiledFeatureModelLayer : public TiledModelLayer
    {
    public: // serialization
        class OSGEARTH_EXPORT Options : public TiledModelLayer::Options,
                                        public FeatureModelOptions,
                                        public GeometryCompilerOptions
        {
        public:
            // constructors
            Options();
            Options(const ConfigOptions& options);
            OE_OPTION_LAYER(FeatureSource, features);
            OE_OPTION(bool, cropFeaturesToTile, false);
            Config getConfig() const override;

        protected: // LayerOptions
            void mergeConfig(const Config& conf) override;
        private:
            void fromConfig(const Config& conf);
        };

    public:
        META_Layer(osgEarth, TiledFeatureModelLayer, Options, TiledModelLayer, TiledFeatureModel);

        //! The feature source from which to read geometry.
        void setFeatureSource(FeatureSource* layer);
        FeatureSource* getFeatureSource() const;

        //! The feature profile, if available
        const FeatureProfile* getFeatureProfile() const;

        //! Stylesheet to apply to the features
        void setStyleSheet(StyleSheet* value);
        StyleSheet* getStyleSheet() const;

        //! Whether to enable GL blending
        void setAlphaBlending(const bool& value);
        const bool& getAlphaBlending() const;

        //! Whether to support lighting
        void setEnableLighting(const bool& value);
        const bool& getEnableLighting() const;

    public: // Layer

        // opens the layer and returns the status
        Status openImplementation() override;

        // closes the layer
        Status closeImplementation() override;

        //! Extent of the feature layer, if available (INVALID if not)
        const GeoExtent& getExtent() const override;

        //! Serialization
        Config getConfig() const override;

    public: // TiledModelLayer

        //! Tiling profile of this layer
        const Profile* getProfile() const override;

        //! Minimum available LOD of tiles
        unsigned getMinLevel() const override;

        //! Maximum available LOD of tiles
        unsigned getMaxLevel() const override;

    protected: // Layer
        
        // called by the map when this layer is added
        void addedToMap(const Map*) override;

        // called by the map when this layer is removed
        void removedFromMap(const Map*) override;

        // post-ctor initialization
        void init() override;

    protected: // TiledModelLayer

        //! Creates an OSG node from a tile key.
        osg::ref_ptr<osg::Node> createTileImplementation(const TileKey&, ProgressCallback*) const override;

    protected:

        virtual ~TiledFeatureModelLayer();

        osg::ref_ptr<class Session> _session;
        FeatureFilterChain _filters;
        osg::ref_ptr< FeatureSourceIndex > _featureIndex;
    };

} // namespace osgEarth
