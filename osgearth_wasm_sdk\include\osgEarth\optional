/* osgEarth
* Copyright 2025 Pelican Mapping
* MIT License
*/
#pragma once

namespace osgEarth
{
    /**
     * A template for defining "optional" class members. An optional member has a default value
     * and a flag indicating whether the member is "set".
     * This is used extensively in osgEarth's Config subsystem.
     */
    template<typename T> struct optional {
        optional() { }
        optional(const optional<T>& rhs) : _set(rhs._set), _value(rhs._value), _defaultValue(rhs._defaultValue) { }
        optional<T>& operator = (const optional<T>& rhs) { _set=rhs._set; _value=rhs._value; _defaultValue=rhs._defaultValue; return *this; }
        optional(const T& defaultValue) : _value(defaultValue), _defaultValue(defaultValue) { }
        const T& operator =(const T& value) { _set=true; _value=value; return _value; }
        bool operator ==(const optional<T>& rhs) const { return _set && rhs._set && _value==rhs._value; }
        bool operator !=(const optional<T>& rhs) const { return !( (*this)==rhs); }
        bool operator ==(const T& value) const { return _value==value; }
        bool operator !=(const T& value) const { return _value!=value; }
        bool operator > (const T& value) const { return _value>value; }
        bool operator >=(const T& value) const { return _value>=value; }
        bool operator < (const T& value) const { return _value<value; }
        bool operator <=(const T& value) const { return _value<=value; }
        bool isSetTo(const T& value) const { return _set && _value==value; } // differs from == in that the value must be explicity set
        bool isSet() const { return _set; }
        void unset() { _set = false; _value=_defaultValue; }
        void clear() { unset(); }

        const T& get() const { return value(); }
        const T& value() const { return _value; }
        const T& defaultValue() const { return _defaultValue; }

        const T& getOrUse(const T& fallback) const { return _set ? _value : fallback; }

        // gets a mutable reference, automatically setting
        T& mutable_value() { _set = true; return _value; }
        T& edit() { return mutable_value(); }

        // sets a default value (without altering a set value)
        void setDefault(T defValue) { _defaultValue = defValue; if (!_set) _value = defValue; }

        // gets the const value.
        operator const T*() const { return &_value; }

        // gets the const value.
        const T* operator ->() const { return &_value; }


    public: // deprecated methods
        
        // sets a default value and usets the variable
        //[[deprecated("Use setDefault or OE_OPTION(type,name,default) instead")]]
        void init(T defValue) { _value = defValue; _defaultValue = defValue; unset(); }

        //[[deprecated("Use mutable_value() or edit() instead")]]
        T* operator ->() { _set = true; return &_value; }

    private:
        bool _set = false;
        T _value;
        T _defaultValue;
        typedef T* optional::*unspecified_bool_type;

    public:
        operator unspecified_bool_type() const { return 0; }
    };
}
