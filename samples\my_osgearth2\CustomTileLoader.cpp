#include "CustomTileLoader.h"
#include <osgDB/ReadFile>
#include <osgDB/WriteFile>
#include <osgDB/FileUtils>
#include <osg/StateSet>
#include <iostream>
#include <sstream>

#define LC "[CustomTileLoader] "

namespace CustomTileSystem {

CustomTileLoader::CustomTileLoader() : _cacheDir("cache/custom_tiles") {
    // Create cache directory
    if (!osgDB::fileExists(_cacheDir)) {
        osgDB::makeDirectory(_cacheDir);
        std::cout << LC << "Created custom tile cache: " << _cacheDir << std::endl;
    }
    
    // Configure HTTP client
    _httpClient.setConnectTimeout(10.0);
    _httpClient.setReadTimeout(30.0);
    _httpClient.setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
}

CustomTileLoader::~CustomTileLoader() {
}

osg::ref_ptr<osg::Image> CustomTileLoader::downloadTile(const std::string& url) {
    std::cout << LC << "Downloading tile: " << url << std::endl;
    
    // Try cache first
    std::string cachedData = loadTileFromCache(url);
    if (!cachedData.empty()) {
        std::cout << LC << "Loading from cache" << std::endl;
        std::istringstream stream(cachedData);
        return osgDB::readImageFile(stream);
    }
    
    // Download from network
    osgEarth::HTTPResponse response = _httpClient.get(url);
    if (response.isOK()) {
        std::cout << LC << "Download successful, size: " << response.getPartAsString(0).length() << " bytes" << std::endl;
        
        // Save to cache
        saveTileToCache(url, response.getPartAsString(0));
        
        // Create image from data
        std::istringstream stream(response.getPartAsString(0));
        osg::ref_ptr<osg::Image> image = osgDB::readImageFile(stream);
        
        if (image.valid()) {
            std::cout << LC << "Image created successfully: " << image->s() << "x" << image->t() << std::endl;
            return image;
        } else {
            std::cout << LC << "Failed to create image from downloaded data" << std::endl;
        }
    } else {
        std::cout << LC << "Download failed: " << response.getCode() << std::endl;
    }
    
    return nullptr;
}

osg::ref_ptr<osg::Texture2D> CustomTileLoader::createTileTexture(const std::string& url) {
    osg::ref_ptr<osg::Image> image = downloadTile(url);
    if (image.valid()) {
        osg::ref_ptr<osg::Texture2D> texture = new osg::Texture2D(image.get());
        texture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
        texture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
        texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR);
        texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
        
        std::cout << LC << "Texture created successfully" << std::endl;
        return texture;
    }
    
    return nullptr;
}

void CustomTileLoader::applyTileToGeometry(osg::Node* node, const std::string& tileUrl) {
    if (!node) return;
    
    osg::ref_ptr<osg::Texture2D> texture = createTileTexture(tileUrl);
    if (texture.valid()) {
        osg::StateSet* stateSet = node->getOrCreateStateSet();
        stateSet->setTextureAttributeAndModes(0, texture.get(), osg::StateAttribute::ON);
        std::cout << LC << "Applied texture to geometry" << std::endl;
    }
}

void CustomTileLoader::loadTilesForArea(double minLon, double minLat, double maxLon, double maxLat, int zoomLevel) {
    std::cout << LC << "Loading tiles for area: " << minLon << "," << minLat << " to " << maxLon << "," << maxLat 
              << " at zoom " << zoomLevel << std::endl;
    
    // Calculate tile coordinates for the area
    // This is a simplified implementation - in practice you'd need proper tile math
    for (int x = 0; x < 4; ++x) {
        for (int y = 0; y < 4; ++y) {
            std::ostringstream urlStream;
            urlStream << "https://mt1.google.com/vt/lyrs=s&x=" << x << "&y=" << y << "&z=" << zoomLevel;
            
            downloadTile(urlStream.str());
        }
    }
}

std::string CustomTileLoader::getTileCachePath(const std::string& url) {
    // Create a simple hash of the URL for cache filename
    std::hash<std::string> hasher;
    size_t hash = hasher(url);
    
    std::ostringstream path;
    path << _cacheDir << "/" << hash << ".tile";
    return path.str();
}

bool CustomTileLoader::saveTileToCache(const std::string& url, const std::string& data) {
    std::string cachePath = getTileCachePath(url);
    std::ofstream file(cachePath, std::ios::binary);
    if (file.is_open()) {
        file.write(data.c_str(), data.length());
        file.close();
        std::cout << LC << "Saved to cache: " << cachePath << std::endl;
        return true;
    }
    return false;
}

std::string CustomTileLoader::loadTileFromCache(const std::string& url) {
    std::string cachePath = getTileCachePath(url);
    if (osgDB::fileExists(cachePath)) {
        std::ifstream file(cachePath, std::ios::binary);
        if (file.is_open()) {
            std::ostringstream buffer;
            buffer << file.rdbuf();
            file.close();
            return buffer.str();
        }
    }
    return "";
}

} // namespace CustomTileSystem
